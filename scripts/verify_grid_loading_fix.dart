#!/usr/bin/env dart

import 'dart:async';
import 'dart:io';

/// 驗證格位載入修復的腳本
/// 
/// 此腳本檢查以下修復項目：
/// 1. GridRemoteDataSource 中的錯誤處理改善
/// 2. productFormTenantGridsProvider 中的超時機制
/// 3. ProductFormPage 中的重試功能
/// 4. 日誌記錄的改善

void main() async {
  print('🔍 開始驗證格位載入修復...\n');

  final fixes = [
    _verifyGridRemoteDataSourceFix(),
    _verifyProductFormProviderFix(),
    _verifyProductFormPageFix(),
    _verifyTestCoverage(),
  ];

  final results = await Future.wait(fixes);
  
  print('\n📊 修復驗證結果:');
  print('=' * 50);
  
  int passedCount = 0;
  for (int i = 0; i < results.length; i++) {
    final result = results[i];
    final status = result ? '✅ 通過' : '❌ 失敗';
    print('${i + 1}. ${_getFixName(i)}: $status');
    if (result) passedCount++;
  }
  
  print('=' * 50);
  print('總計: $passedCount/${results.length} 項修復通過驗證');
  
  if (passedCount == results.length) {
    print('\n🎉 所有修復都已正確實施！');
    exit(0);
  } else {
    print('\n⚠️  部分修復需要檢查，請查看上述結果。');
    exit(1);
  }
}

String _getFixName(int index) {
  switch (index) {
    case 0:
      return 'GridRemoteDataSource 錯誤處理';
    case 1:
      return 'ProductFormProvider 超時機制';
    case 2:
      return 'ProductFormPage 重試功能';
    case 3:
      return '測試覆蓋率';
    default:
      return '未知修復項目';
  }
}

/// 驗證 GridRemoteDataSource 的修復
Future<bool> _verifyGridRemoteDataSourceFix() async {
  print('🔧 檢查 GridRemoteDataSource 修復...');
  
  final file = File('lib/features/tenant_mgmt/data/datasources/grid_remote_ds.dart');
  if (!file.existsSync()) {
    print('   ❌ 文件不存在');
    return false;
  }
  
  final content = await file.readAsString();
  
  // 檢查關鍵修復項目
  final checks = [
    content.contains('StreamController<List<GridEntity>>'),
    content.contains('streamController.onCancel'),
    content.contains('Logger.error'),
    content.contains('if (!streamController.isClosed)'),
    content.contains('storeId.isEmpty'),
  ];
  
  final passed = checks.every((check) => check);
  
  if (passed) {
    print('   ✅ StreamController 錯誤處理已實施');
    print('   ✅ 資源清理機制已添加');
    print('   ✅ 參數驗證已改善');
  } else {
    print('   ❌ 部分修復項目缺失');
  }
  
  return passed;
}

/// 驗證 ProductFormProvider 的修復
Future<bool> _verifyProductFormProviderFix() async {
  print('🔧 檢查 ProductFormProvider 修復...');
  
  final file = File('lib/features/product_mgmt/presentation/providers/product_form_provider.dart');
  if (!file.existsSync()) {
    print('   ❌ 文件不存在');
    return false;
  }
  
  final content = await file.readAsString();
  
  // 檢查超時機制和錯誤處理
  final checks = [
    content.contains('.timeout('),
    content.contains('const Duration(seconds: 30)'),
    content.contains('onTimeout:'),
    content.contains('載入格位超時'),
    content.contains('.handleError('),
    content.contains('Logger.error'),
  ];
  
  final passed = checks.every((check) => check);
  
  if (passed) {
    print('   ✅ 30秒超時機制已實施');
    print('   ✅ 錯誤處理已改善');
    print('   ✅ 日誌記錄已添加');
  } else {
    print('   ❌ 部分修復項目缺失');
  }
  
  return passed;
}

/// 驗證 ProductFormPage 的修復
Future<bool> _verifyProductFormPageFix() async {
  print('🔧 檢查 ProductFormPage 修復...');
  
  final file = File('lib/features/product_mgmt/presentation/pages/product_form_page.dart');
  if (!file.existsSync()) {
    print('   ❌ 文件不存在');
    return false;
  }
  
  final content = await file.readAsString();
  
  // 檢查重試功能
  final checks = [
    content.contains('_buildErrorCardWithRetry'),
    content.contains('TextButton'),
    content.contains('重試'),
    content.contains('ref.refresh'),
    content.contains('Icons.refresh'),
  ];
  
  final passed = checks.every((check) => check);
  
  if (passed) {
    print('   ✅ 重試按鈕已添加');
    print('   ✅ 錯誤卡片已改善');
    print('   ✅ Provider 刷新機制已實施');
  } else {
    print('   ❌ 部分修復項目缺失');
  }
  
  return passed;
}

/// 驗證測試覆蓋率
Future<bool> _verifyTestCoverage() async {
  print('🔧 檢查測試覆蓋率...');
  
  final testFile = File('test/features/product_mgmt/presentation/pages/product_form_page_test.dart');
  if (!testFile.existsSync()) {
    print('   ❌ 測試文件不存在');
    return false;
  }
  
  final content = await testFile.readAsString();
  
  // 檢查測試用例
  final checks = [
    content.contains('should show loading state'),
    content.contains('should show retry button'),
    content.contains('should show timeout error'),
    content.contains('should successfully load grids'),
    content.contains('retry button should refresh'),
  ];
  
  final passed = checks.every((check) => check);
  
  if (passed) {
    print('   ✅ 載入狀態測試已添加');
    print('   ✅ 錯誤處理測試已添加');
    print('   ✅ 重試功能測試已添加');
    print('   ✅ 超時測試已添加');
  } else {
    print('   ❌ 部分測試用例缺失');
  }
  
  return passed;
}
