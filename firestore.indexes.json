{"indexes": [{"collectionGroup": "sales", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "active", "order": "ASCENDING"}, {"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "sales", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "active", "order": "ASCENDING"}, {"fieldPath": "stock", "order": "ASCENDING"}, {"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "active", "order": "ASCENDING"}, {"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}], "fieldOverrides": []}