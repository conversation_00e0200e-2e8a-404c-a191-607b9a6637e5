#how to run?: /opt/homebrew/bin/python3 list.py ./lib/

import os
import argparse
import sys

def generate_markdown_structure_recursive(current_path, depth=0):
    """
    Recursively generates a list of Markdown strings representing the directory structure.
    Files are only listed for the deepest directories.
    """
    markdown_lines = []
    indent = "  " * depth  # Two spaces per depth level for Markdown list indentation

    try:
        # Get and sort items for consistent order
        items = sorted(os.listdir(current_path))
    except PermissionError:
        markdown_lines.append(f"{indent}- [Permission Denied to access {os.path.basename(current_path)}]")
        return markdown_lines
    except FileNotFoundError:
        markdown_lines.append(f"{indent}- [Directory Not Found: {os.path.basename(current_path)}]")
        return markdown_lines

    sub_dir_names = []
    file_names = []

    # Separate directories and files
    for item_name in items:
        item_full_path = os.path.join(current_path, item_name)
        if os.path.isdir(item_full_path):
            sub_dir_names.append(item_name)
        elif os.path.isfile(item_full_path):
            file_names.append(item_name)

    # If there are subdirectories, process them first
    if sub_dir_names:
        for dir_name in sub_dir_names:
            markdown_lines.append(f"{indent}- {dir_name}/")
            # Recursively call for subdirectories, incrementing depth
            markdown_lines.extend(generate_markdown_structure_recursive(os.path.join(current_path, dir_name), depth + 1))
        # Files at this level are not listed if subdirectories exist,
        # adhering to the "files only in deepest directories" rule.
    # Else (no subdirectories), this is a leaf directory, so list its files
    elif file_names: # Only list files if it's a leaf directory and has files
        for file_name in file_names:
            markdown_lines.append(f"{indent}- {file_name}")
    # If a directory is empty (no subdirectories and no files),
    # it will have been listed by its parent, and this function will return an empty list for it.

    return markdown_lines

def main():
    parser = argparse.ArgumentParser(
        description="List directory structure in Markdown format, showing files only in the deepest directories."
    )
    parser.add_argument("directory", nargs='?', default=".",
                        help="The directory to scan (default: current directory)")
    args = parser.parse_args()

    start_path = os.path.abspath(args.directory)

    if not os.path.isdir(start_path):
        print(f"Error: '{start_path}' is not a valid directory.", file=sys.stderr)
        sys.exit(1) # Exit with an error code if the path is invalid

    # Start the Markdown output with the root directory
    # The first level of items inside the target directory will have depth=1 for indentation
    markdown_output_lines = [f"- {os.path.basename(start_path)}/"]
    markdown_output_lines.extend(generate_markdown_structure_recursive(start_path, depth=1))

    # Print the final Markdown output, wrapped in a code block
    print("```markdown")
    print("\n".join(markdown_output_lines))
    print("```")

if __name__ == "__main__":
    main()
