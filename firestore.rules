rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isCurrentUser(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isTenant(tenantDocId) {
      return isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'tenant' &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.tenantId == tenantDocId;
    }
    
    function isCashier(storeId) {
      return isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'cashier' &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.storeId == storeId;
    }

    // User collection rules
    match /users/{userId} {
      // Allow users to read their own data
      // Allow admins to read all user data
      allow read: if isCurrentUser(userId) || isAdmin();
      
      // Allow users to create their own document with restricted role
      allow create: if isCurrentUser(userId) &&
        request.resource.data.role == 'pending_approval' &&
        request.resource.data.email == request.auth.token.email;
      
      // Allow users to update their own document but not change role
      allow update: if isCurrentUser(userId) && 
        request.resource.data.role == resource.data.role;
        
      // Allow admins to update any user document including role
      allow update: if isAdmin();
    }
    
    // Store collection rules
    match /stores/{storeId} {
      // Allow authenticated users to read store data
      allow read: if isAuthenticated();
      
      // Allow admin to create, update, delete store data
      allow create, update, delete: if isAdmin();
      
      // Tenant management rules
      match /tenants/{tenantId} {
        // Allow admins to read all tenant data
        // Allow tenants to read their own data
        allow read: if isAdmin() || isTenant(tenantId);
        
        // Only admins can create, update, or delete tenants
        allow create, update, delete: if isAdmin();
        
        // Tenant's products sub-collection (adding structure for future)
        match /products/{productId} {
          // Allow read if user is authenticated and product is active,
          // or if user is admin or the tenant owner
          allow read: if (isAuthenticated() && 
                          (resource.data.active == true || resource.data.active == null)) ||
                         isAdmin() ||
                         isTenant(tenantId);
                         
          // Allow create/update/delete if user is admin or the tenant owner
          allow create, update, delete: if isAdmin() || isTenant(tenantId);
          
          // Data validation for create and update operations
          allow create, update: if (isAdmin() || isTenant(tenantId)) &&
            // Ensure required fields exist and have valid values
            request.resource.data.name.size() > 0 &&
            request.resource.data.price >= 0 &&
            request.resource.data.stock >= 0 &&
            request.resource.data.lowStockLevel >= 0 &&
            request.resource.data.gridId.size() > 0 &&
            // Verify storeId and tenantId match the document path
            request.resource.data.storeId == storeId &&
            request.resource.data.tenantId == tenantId &&
            // Ensure SKU and barcode are not empty
            request.resource.data.sku.size() > 0 &&
            request.resource.data.barcode.size() > 0;
          
          // Locks sub-collection for product checkout
          match /locks/{lockId} {
            // Any authenticated user can check if a product is locked
            allow read: if isAuthenticated();
            
            // Users can create locks for products they are checking out
            allow create: if isAuthenticated() && 
                          request.resource.data.byUserId == request.auth.uid &&
                          request.resource.data.expire is timestamp;
            
            // Only the user who created the lock can delete it
            allow delete: if isAuthenticated() && resource.data.byUserId == request.auth.uid;
          }
        }
      }
      
      // Grid management rules
      match /grids/{gridId} {
        // Allow authenticated users to read grid data
        allow read: if isAuthenticated();
        // Allow admin to create, update, delete grid data
        allow create, update, delete: if isAdmin();
      }
      
      // Sales collection rules
      match /sales/{saleId} {
        // Read permissions for sales data
        allow read: if isAdmin() ||
                      (isTenant(resource.data.tenantId)) ||
                      (isCashier(storeId) && (resource.data.cashierId == request.auth.uid || isAdmin()));
                      
        // Create permissions with data validation
        allow create: if ((isCashier(storeId) && request.resource.data.cashierId == request.auth.uid) || 
                           isAdmin()) &&
                        // Ensure required fields exist and have valid values
                        request.resource.data.storeId == storeId &&
                        request.resource.data.totalAmount >= 0 &&
                        request.resource.data.items is list &&
                        request.resource.data.items.size() > 0 &&
                        request.resource.data.status in ['completed', 'cancelled'] &&
                        request.resource.data.paymentType is string &&
                        request.resource.data.paymentType.size() > 0;
                        
        // Update permissions with restrictions
        allow update: if ((isCashier(storeId) && request.resource.data.cashierId == request.auth.uid) || 
                           isAdmin()) &&
                        // Only allow status updates to 'cancelled' (no field removal)
                        request.resource.data.status == 'cancelled' &&
                        // Ensure no change to other important fields
                        request.resource.data.storeId == resource.data.storeId &&
                        request.resource.data.tenantId == resource.data.tenantId &&
                        request.resource.data.cashierId == resource.data.cashierId &&
                        request.resource.data.totalAmount == resource.data.totalAmount &&
                        request.resource.data.items.size() == resource.data.items.size() &&
                        request.resource.data.paymentType == resource.data.paymentType;
      }
    }
    
    // Daily summaries collection rules (adding structure for future)
    match /daily_summaries/{docId} {
      allow read: if isAdmin();
      allow write: if isAdmin();
    }
    
    // Global counters collection rules (adding structure for future)
    match /global_counters/{counterName}/shards/{shardId} {
      allow read, write: if isAuthenticated();
    }
    
    // Default deny
    match /{document=**} {
      allow read, write: if false;
    }
  }
}