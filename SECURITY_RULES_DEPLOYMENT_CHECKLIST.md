# Firestore 安全規則部署檢查清單

## 部署前檢查

### 1. 代碼審查 ✅
- [ ] 安全規則語法正確
- [ ] 所有新增功能的路徑都有對應的安全規則
- [ ] 權限設置符合最小權限原則
- [ ] 沒有意外的開放權限

### 2. 功能覆蓋檢查 ✅
- [ ] 用戶管理 (users collection)
- [ ] 店鋪管理 (stores collection)
- [ ] 租戶管理 (tenants subcollection)
- [ ] 商品管理 (products subcollection)
- [ ] 格位管理 (grids subcollection)
- [ ] 銷售管理 (sales subcollection)
- [ ] 每日摘要 (daily_summaries collection) - **新增**
- [ ] 商品鎖定 (locks subcollection)

### 3. 新增功能安全規則檢查 ✅

#### 3.1 每日摘要 (daily_summaries)
- [ ] 只有管理員可以讀取
- [ ] 只有管理員可以創建/更新
- [ ] 文檔 ID 格式驗證 (yyyyMMdd_storeId)
- [ ] 必填字段驗證
- [ ] 數據類型驗證

#### 3.2 儀表板數據訪問
- [ ] 管理員可以訪問所有店鋪數據
- [ ] 租戶只能訪問自己店鋪的銷售數據
- [ ] 租戶只能訪問自己的商品數據
- [ ] 收銀員可以訪問自己創建的銷售數據

#### 3.3 PDF 報告生成
- [ ] 管理員可以生成所有報告
- [ ] 租戶只能生成自己的庫存報告
- [ ] 租戶不能生成銷售報告

### 4. Rules Playground 測試 ⏳
- [ ] 執行所有測試用例 (參考 SECURITY_RULES_TEST_CASES.md)
- [ ] 驗證正面測試用例 (應該允許的操作)
- [ ] 驗證負面測試用例 (應該拒絕的操作)
- [ ] 記錄測試結果

### 5. 性能考量檢查 ✅
- [ ] 檢查 `get()` 操作的使用頻率
- [ ] 確保沒有潛在的性能瓶頸
- [ ] 考慮規則複雜度對查詢性能的影響

## 部署步驟

### 1. 備份當前規則
```bash
# 導出當前規則
firebase firestore:rules:get > firestore.rules.backup.$(date +%Y%m%d_%H%M%S)
```

### 2. 部署新規則
```bash
# 部署規則
firebase deploy --only firestore:rules

# 或者使用特定專案
firebase deploy --only firestore:rules --project your-project-id
```

### 3. 驗證部署
- [ ] 檢查 Firebase Console 中規則是否正確更新
- [ ] 驗證規則版本和時間戳
- [ ] 確認沒有語法錯誤

## 部署後測試

### 1. 功能測試 ⏳
使用實際應用程序測試以下功能：

#### 1.1 管理員功能
- [ ] 登錄管理員帳戶
- [ ] 訪問管理員儀表板
- [ ] 查看所有店鋪數據
- [ ] 生成每日摘要
- [ ] 生成 PDF 報告 (銷售和庫存)
- [ ] 管理用戶、店鋪、租戶

#### 1.2 租戶功能
- [ ] 登錄租戶帳戶
- [ ] 訪問租戶儀表板
- [ ] 查看自己的銷售數據
- [ ] 查看自己的商品庫存
- [ ] 查看低庫存商品列表
- [ ] 生成庫存 PDF 報告
- [ ] 管理自己的商品
- [ ] 嘗試訪問其他租戶數據 (應該失敗)

#### 1.3 收銀員功能
- [ ] 登錄收銀員帳戶
- [ ] 創建銷售記錄
- [ ] 查看自己創建的銷售記錄
- [ ] 嘗試訪問其他收銀員的銷售記錄 (應該失敗)

#### 1.4 未授權用戶
- [ ] 登錄 pending_approval 用戶
- [ ] 確認無法訪問敏感數據
- [ ] 確認只能更新自己的基本資料

### 2. 錯誤處理測試 ⏳
- [ ] 測試無效的數據格式
- [ ] 測試權限不足的操作
- [ ] 測試網絡錯誤情況
- [ ] 驗證錯誤訊息是否適當

### 3. 性能測試 ⏳
- [ ] 測試大量數據查詢的性能
- [ ] 監控 Firestore 使用量
- [ ] 檢查是否有異常的讀寫操作

## 監控和維護

### 1. 部署後監控 ⏳
- [ ] 監控 Firebase Console 中的錯誤日誌
- [ ] 檢查應用程序錯誤報告
- [ ] 監控用戶反饋

### 2. 定期審查 📅
- [ ] 每月審查安全規則
- [ ] 檢查是否有新的安全需求
- [ ] 更新測試用例

### 3. 文檔更新 ✅
- [ ] 更新安全規則文檔
- [ ] 更新 API 文檔
- [ ] 更新開發者指南

## 回滾計劃

如果部署後發現問題：

### 1. 立即回滾
```bash
# 恢復備份的規則
firebase firestore:rules:set firestore.rules.backup.YYYYMMDD_HHMMSS
```

### 2. 問題分析
- [ ] 分析錯誤日誌
- [ ] 識別問題根源
- [ ] 制定修復計劃

### 3. 修復和重新部署
- [ ] 修復安全規則
- [ ] 重新測試
- [ ] 重新部署

## 簽核

### 開發團隊審查
- [ ] 開發者: _________________ 日期: _________
- [ ] 技術主管: ______________ 日期: _________

### 測試團隊驗證
- [ ] 測試工程師: ____________ 日期: _________
- [ ] QA 主管: _______________ 日期: _________

### 部署授權
- [ ] 專案經理: ______________ 日期: _________
- [ ] 系統管理員: ____________ 日期: _________

## 部署記錄

- **部署日期**: _______________
- **部署人員**: _______________
- **規則版本**: _______________
- **備份文件**: _______________
- **部署結果**: _______________
- **測試結果**: _______________
- **備註**: ___________________

---

**注意**: 此檢查清單應該在每次安全規則更新時使用，確保部署的安全性和可靠性。
