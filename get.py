import os

def merge_ts_files(
    folder_path: str = 'lib',
    output_file: str = 'allcode.txt',
    recursive: bool = True,
    include_node_modules: bool = False
):
    """
    合并所有TypeScript文件内容到单个文件
    
    Args:
        folder_path: 要搜索的文件夹路径
        output_file: 输出文件名
        recursive: 是否递归搜索子文件夹
        include_node_modules: 是否包含node_modules文件夹
    """
    if not os.path.exists(folder_path):
        raise FileNotFoundError(f"文件夹 '{folder_path}' 不存在")
    
    # 使用 with 语句确保文件正确关闭
    with open(output_file, 'w', encoding='utf-8') as outfile:
        file_count = 0
        
        # 遍历文件夹
        for root, dirs, files in os.walk(folder_path):
            # 跳过 node_modules
            if not include_node_modules and 'node_modules' in dirs:
                dirs.remove('node_modules')
            
            # 只处理 .ts 文件    
            for file in files:
                if file.endswith('.dart'):
                    file_path = os.path.join(root, file)
                    try:
                        # 写入文件路径作为注释
                        outfile.write(f'\n// File: {file_path}\n')
                        outfile.write('// ' + '=' * 80 + '\n\n')
                        
                        # 读取并写入文件内容
                        with open(file_path, 'r', encoding='utf-8') as infile:
                            outfile.write(infile.read())
                            outfile.write('\n')
                        
                        file_count += 1
                        print(f'已处理: {file_path}')
                        
                    except Exception as e:
                        print(f'处理文件 {file_path} 时出错: {e}')
            
            # 如果不递归,处理完当前文件夹就退出
            if not recursive:
                break
    
    print(f'\n完成! 共合并了 {file_count} 个dart文件到 {output_file}')

if __name__ == '__main__':
    try:
        merge_ts_files(
            folder_path='./lib',
            recursive=True,
            include_node_modules=False
        )
    except Exception as e:
        print(f"发生错误: {e}")