{"flutter": {"platforms": {"android": {"default": {"projectId": "grid-pos", "appId": "1:517919260870:android:ba277fd092fbfb5f2cd7c7", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "grid-pos", "appId": "1:517919260870:ios:613668485818d4a82cd7c7", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "grid-pos", "appId": "1:517919260870:ios:613668485818d4a82cd7c7", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "grid-pos", "configurations": {"android": "1:517919260870:android:ba277fd092fbfb5f2cd7c7", "ios": "1:517919260870:ios:613668485818d4a82cd7c7", "macos": "1:517919260870:ios:613668485818d4a82cd7c7", "web": "1:517919260870:web:615c3cac3a85e0512cd7c7", "windows": "1:517919260870:web:fcc38f3cf267bab82cd7c7"}}}}}, "hosting": {"source": ".", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "frameworksBackend": {"region": "asia-east1"}}}