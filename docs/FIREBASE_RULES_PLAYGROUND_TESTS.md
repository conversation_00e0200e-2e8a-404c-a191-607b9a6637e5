# Firebase Rules Playground 測試指南

## 測試數據設置

在 Firebase Console > Firestore Database > Rules > Playground 的 "Firestore data" 部分，添加以下測試數據：

```json
{
  "users": {
    "test_admin_001": {
      "email": "<EMAIL>",
      "role": "admin",
      "displayName": "Test Admin"
    },
    "test_tenant_001": {
      "email": "<EMAIL>",
      "role": "tenant",
      "tenantId": "test_tenant_001",
      "storeId": "test_store_001",
      "displayName": "Test Tenant"
    },
    "test_cashier_001": {
      "email": "<EMAIL>",
      "role": "cashier",
      "storeId": "test_store_001",
      "displayName": "Test Cashier"
    }
  },
  "stores": {
    "test_store_001": {
      "name": "Test Store",
      "address": "Test Address",
      "active": true,
      "tenants": {
        "test_tenant_001": {
          "name": "Test Tenant",
          "contactEmail": "<EMAIL>",
          "active": true,
          "products": {
            "test_product_001": {
              "name": "Test Product",
              "sku": "TEST001",
              "barcode": "BAR001",
              "price": 10.0,
              "stock": 5,
              "lowStockLevel": 3,
              "gridId": "test_grid_001",
              "active": true,
              "tenantId": "test_tenant_001",
              "storeId": "test_store_001"
            }
          }
        }
      },
      "sales": {
        "test_sale_001": {
          "storeId": "test_store_001",
          "cashierId": "test_cashier_001",
          "totalAmount": 50.0,
          "status": "completed",
          "paymentType": "cash",
          "items": [
            {
              "productId": "test_product_001",
              "name": "Test Product",
              "qty": 2,
              "price": 10.0,
              "tenantId": "test_tenant_001"
            }
          ]
        }
      }
    }
  },
  "daily_summaries": {
    "20241201_test_store_001": {
      "date": "2024-12-01",
      "storeId": "test_store_001",
      "totalSales": 100.0,
      "transactionsCount": 5,
      "lowStockProductsCount": 2,
      "createdAt": "2024-12-01T10:00:00Z",
      "updatedAt": "2024-12-01T10:00:00Z"
    }
  }
}
```

## 核心測試用例

### 1. 用戶管理測試

#### 測試 1.1: 管理員讀取用戶數據 ✅
- **Authentication**: `{"uid": "test_admin_001", "token": {"email": "<EMAIL>"}}`
- **Operation**: `get`
- **Path**: `/databases/(default)/documents/users/test_tenant_001`
- **Expected**: `ALLOW`

#### 測試 1.2: 用戶讀取自己的數據 ✅
- **Authentication**: `{"uid": "test_tenant_001", "token": {"email": "<EMAIL>"}}`
- **Operation**: `get`
- **Path**: `/databases/(default)/documents/users/test_tenant_001`
- **Expected**: `ALLOW`

#### 測試 1.3: 用戶讀取他人數據 ❌
- **Authentication**: `{"uid": "test_tenant_001", "token": {"email": "<EMAIL>"}}`
- **Operation**: `get`
- **Path**: `/databases/(default)/documents/users/test_admin_001`
- **Expected**: `DENY`

### 2. 商品管理測試

#### 測試 2.1: 租戶讀取自己的商品 ✅
- **Authentication**: `{"uid": "test_tenant_001", "token": {"email": "<EMAIL>"}}`
- **Operation**: `get`
- **Path**: `/databases/(default)/documents/stores/test_store_001/tenants/test_tenant_001/products/test_product_001`
- **Expected**: `ALLOW`

#### 測試 2.2: 租戶創建商品 ✅
- **Authentication**: `{"uid": "test_tenant_001", "token": {"email": "<EMAIL>"}}`
- **Operation**: `create`
- **Path**: `/databases/(default)/documents/stores/test_store_001/tenants/test_tenant_001/products/new_product`
- **Data**:
```json
{
  "name": "New Product",
  "sku": "NEW001",
  "barcode": "NEWBAR001",
  "price": 15.0,
  "stock": 10,
  "lowStockLevel": 5,
  "gridId": "test_grid_001",
  "active": true,
  "tenantId": "test_tenant_001",
  "storeId": "test_store_001"
}
```
- **Expected**: `ALLOW`

#### 測試 2.3: 租戶創建商品但 tenantId 不匹配 ❌
- **Authentication**: `{"uid": "test_tenant_001", "token": {"email": "<EMAIL>"}}`
- **Operation**: `create`
- **Path**: `/databases/(default)/documents/stores/test_store_001/tenants/test_tenant_001/products/invalid_product`
- **Data**:
```json
{
  "name": "Invalid Product",
  "sku": "INV001",
  "barcode": "INVBAR001",
  "price": 15.0,
  "stock": 10,
  "lowStockLevel": 5,
  "gridId": "test_grid_001",
  "active": true,
  "tenantId": "other_tenant",
  "storeId": "test_store_001"
}
```
- **Expected**: `DENY`

### 3. 銷售數據測試

#### 測試 3.1: 管理員讀取銷售數據 ✅
- **Authentication**: `{"uid": "test_admin_001", "token": {"email": "<EMAIL>"}}`
- **Operation**: `get`
- **Path**: `/databases/(default)/documents/stores/test_store_001/sales/test_sale_001`
- **Expected**: `ALLOW`

#### 測試 3.2: 收銀員讀取自己創建的銷售數據 ✅
- **Authentication**: `{"uid": "test_cashier_001", "token": {"email": "<EMAIL>"}}`
- **Operation**: `get`
- **Path**: `/databases/(default)/documents/stores/test_store_001/sales/test_sale_001`
- **Expected**: `ALLOW`

#### 測試 3.3: 租戶讀取店鋪銷售數據 ✅
- **Authentication**: `{"uid": "test_tenant_001", "token": {"email": "<EMAIL>"}}`
- **Operation**: `get`
- **Path**: `/databases/(default)/documents/stores/test_store_001/sales/test_sale_001`
- **Expected**: `ALLOW`

#### 測試 3.4: 收銀員創建銷售記錄 ✅
- **Authentication**: `{"uid": "test_cashier_001", "token": {"email": "<EMAIL>"}}`
- **Operation**: `create`
- **Path**: `/databases/(default)/documents/stores/test_store_001/sales/new_sale`
- **Data**:
```json
{
  "storeId": "test_store_001",
  "cashierId": "test_cashier_001",
  "totalAmount": 25.0,
  "status": "completed",
  "paymentType": "card",
  "items": [
    {
      "productId": "test_product_001",
      "name": "Test Product",
      "qty": 1,
      "price": 25.0,
      "tenantId": "test_tenant_001"
    }
  ]
}
```
- **Expected**: `ALLOW`

### 4. 每日摘要測試

#### 測試 4.1: 管理員讀取每日摘要 ✅
- **Authentication**: `{"uid": "test_admin_001", "token": {"email": "<EMAIL>"}}`
- **Operation**: `get`
- **Path**: `/databases/(default)/documents/daily_summaries/20241201_test_store_001`
- **Expected**: `ALLOW`

#### 測試 4.2: 租戶讀取每日摘要 ❌
- **Authentication**: `{"uid": "test_tenant_001", "token": {"email": "<EMAIL>"}}`
- **Operation**: `get`
- **Path**: `/databases/(default)/documents/daily_summaries/20241201_test_store_001`
- **Expected**: `DENY`

#### 測試 4.3: 管理員創建每日摘要 ✅
- **Authentication**: `{"uid": "test_admin_001", "token": {"email": "<EMAIL>"}}`
- **Operation**: `create`
- **Path**: `/databases/(default)/documents/daily_summaries/20241202_test_store_001`
- **Data**:
```json
{
  "date": "2024-12-02",
  "storeId": "test_store_001",
  "totalSales": 150.0,
  "transactionsCount": 8,
  "lowStockProductsCount": 1,
  "createdAt": "2024-12-02T10:00:00Z",
  "updatedAt": "2024-12-02T10:00:00Z"
}
```
- **Expected**: `ALLOW`

#### 測試 4.4: 管理員創建格式錯誤的每日摘要 ❌
- **Authentication**: `{"uid": "test_admin_001", "token": {"email": "<EMAIL>"}}`
- **Operation**: `create`
- **Path**: `/databases/(default)/documents/daily_summaries/invalid_format`
- **Data**:
```json
{
  "date": "2024-12-02",
  "storeId": "test_store_001",
  "totalSales": 150.0,
  "transactionsCount": 8,
  "lowStockProductsCount": 1,
  "createdAt": "2024-12-02T10:00:00Z",
  "updatedAt": "2024-12-02T10:00:00Z"
}
```
- **Expected**: `DENY` (因為文檔 ID 格式不正確)

## 執行步驟

1. **設置測試數據**: 將上面的 JSON 數據複製到 Playground 的 "Firestore data" 部分
2. **選擇測試用例**: 從上面選擇一個測試用例
3. **設置認證**: 在 "Authentication" 部分設置對應的用戶信息
4. **設置操作**: 選擇操作類型 (get, create, update, delete)
5. **設置路徑**: 輸入文檔路徑
6. **設置數據**: 如果是寫操作，輸入數據
7. **執行測試**: 點擊 "Run" 按鈕
8. **驗證結果**: 檢查結果是否符合預期

## 測試結果記錄

| 測試用例 | 預期結果 | 實際結果 | 狀態 | 備註 |
|---------|---------|---------|------|------|
| 1.1 | ALLOW | [待測試] | ⏳ | |
| 1.2 | ALLOW | [待測試] | ⏳ | |
| 1.3 | DENY | [待測試] | ⏳ | |
| 2.1 | ALLOW | [待測試] | ⏳ | |
| 2.2 | ALLOW | [待測試] | ⏳ | |
| 2.3 | DENY | [待測試] | ⏳ | |
| 3.1 | ALLOW | [待測試] | ⏳ | |
| 3.2 | ALLOW | [待測試] | ⏳ | |
| 3.3 | ALLOW | [待測試] | ⏳ | |
| 3.4 | ALLOW | [待測試] | ⏳ | |
| 4.1 | ALLOW | [待測試] | ⏳ | |
| 4.2 | DENY | [待測試] | ⏳ | |
| 4.3 | ALLOW | [待測試] | ⏳ | |
| 4.4 | DENY | [待測試] | ⏳ | |

## 注意事項

1. **時間戳格式**: 在測試數據中使用 ISO 8601 格式的時間戳
2. **數據類型**: 確保數據類型與規則中的驗證一致
3. **路徑格式**: 使用完整的 Firestore 文檔路徑
4. **認證信息**: 確保認證信息與測試數據中的用戶匹配
