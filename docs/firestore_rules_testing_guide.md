# Testing Firestore Security Rules in Production

This document provides guidelines for validating the Firestore security rules after they've been deployed to the production project **grid-pos**.

## Prerequisites

1. Access to the Firebase Console for the **grid-pos** project
2. Multiple test accounts with different roles:
   - Admin user
   - Tenant user
   - Regular user (no special role or pending_approval)

## Testing Method 1: Using the Rules Playground

The Firebase Console provides a Rules Playground that allows you to simulate database operations with different authentication states.

1. Log in to the [Firebase Console](https://console.firebase.google.com/)
2. Select the **grid-pos** project
3. Navigate to **Firestore Database** in the left menu
4. Click on the **Rules** tab
5. Click on the **Rules Playground** button in the top-right corner

### Test Cases for Tenant Management

#### Admin User

1. **Get all tenants**

   - Set Authentication to Admin User
   - Operation: **get**
   - Path: `stores/{storeId}/tenants`
   - Expected: **Allow**

2. **Create a new tenant**

   - Set Authentication to Admin User
   - Operation: **create**
   - Path: `stores/{storeId}/tenants/{newTenantId}`
   - Data: `{ "name": "Test Tenant", "contact": { "phone": "**********", "email": "<EMAIL>" }, "active": true, "contract": { "start": [timestamp], "end": [timestamp], "rent": 1000 }, "grids": [], "createdAt": [timestamp], "updatedAt": [timestamp] }`
   - Expected: **Allow**

3. **Update a tenant**

   - Set Authentication to Admin User
   - Operation: **update**
   - Path: `stores/{storeId}/tenants/{existingTenantId}`
   - Data: `{ "name": "Updated Tenant", "contact": { "phone": "**********", "email": "<EMAIL>" }, "active": true, "contract": { "start": [timestamp], "end": [timestamp], "rent": 1000 }, "grids": [], "updatedAt": [timestamp] }`
   - Expected: **Allow**

4. **Delete a tenant**
   - Set Authentication to Admin User
   - Operation: **delete**
   - Path: `stores/{storeId}/tenants/{existingTenantId}`
   - Expected: **Allow**

#### Tenant User

1. **Get own tenant data**
   - Set Authentication to Tenant User
   - Operation: **get**
   - Path: `stores/{storeId}/tenants/{ownTenantId}`
   - Expected: **Allow**
2. **Get another tenant's data**

   - Set Authentication to Tenant User
   - Operation: **get**
   - Path: `stores/{storeId}/tenants/{otherTenantId}`
   - Expected: **Deny**

3. **Create/update/delete any tenant**
   - Set Authentication to Tenant User
   - Operation: **create/update/delete**
   - Path: `stores/{storeId}/tenants/{anyTenantId}`
   - Expected: **Deny**

#### Regular User

1. **Any operation on tenants**
   - Set Authentication to Regular User
   - Operation: **get/create/update/delete**
   - Path: `stores/{storeId}/tenants/{anyTenantId}`
   - Expected: **Deny**

### Test Cases for Grid Management

#### Admin User

1. **Get all grids**

   - Set Authentication to Admin User
   - Operation: **get**
   - Path: `stores/{storeId}/grids`
   - Expected: **Allow**

2. **Create a new grid**

   - Set Authentication to Admin User
   - Operation: **create**
   - Path: `stores/{storeId}/grids/{newGridId}`
   - Data: `{ "code": "A1", "size": "M", "tenantId": null, "createdAt": [timestamp], "updatedAt": [timestamp] }`
   - Expected: **Allow**

3. **Update a grid**

   - Set Authentication to Admin User
   - Operation: **update**
   - Path: `stores/{storeId}/grids/{existingGridId}`
   - Data: `{ "code": "A2", "size": "L", "tenantId": null, "updatedAt": [timestamp] }`
   - Expected: **Allow**

4. **Delete a grid**
   - Set Authentication to Admin User
   - Operation: **delete**
   - Path: `stores/{storeId}/grids/{existingGridId}`
   - Expected: **Allow**

#### Tenant User and Regular User

1. **Read grid data**

   - Set Authentication to Tenant/Regular User
   - Operation: **get**
   - Path: `stores/{storeId}/grids/{anyGridId}`
   - Expected: **Allow**

2. **Create/update/delete any grid**
   - Set Authentication to Tenant/Regular User
   - Operation: **create/update/delete**
   - Path: `stores/{storeId}/grids/{anyGridId}`
   - Expected: **Deny**

## Testing Method 2: Using the Actual Application

Testing with the actual application provides real-world validation of the security rules.

### Admin User Test

1. Log in with an admin account
2. Navigate to tenant management
3. Verify that you can:
   - View all tenants
   - Create a new tenant
   - Edit an existing tenant
   - Delete a tenant
4. Navigate to grid management
5. Verify that you can:
   - View all grids
   - Create a new grid
   - Edit an existing grid
   - Delete an unassigned grid
   - Verify you cannot delete a grid assigned to a tenant

### Tenant User Test

1. Log in with a tenant account
2. Navigate to dashboard
3. Verify that you can:
   - View only your own tenant details
   - Cannot access other tenants' data
   - Cannot create, edit, or delete tenants
4. Navigate to grid view (if available)
5. Verify that you can:
   - View grid information
   - Cannot create, edit, or delete grids

### Regular User Test

1. Log in with a regular user account
2. Verify that you cannot access tenant or grid management features

## Troubleshooting

If the rules don't work as expected:

1. Check the Firebase console logs for security rule evaluation details
2. Verify that the deployed rules match your intended rules
3. Ensure that the user accounts have the correct role data in Firestore `users` collection
4. Check that the tenant ID in the user document matches the tenant document ID for tenant access tests

## Deployment Verification

After deploying rules, always perform a subset of these tests to ensure basic functionality works as expected.

Remember that changes to security rules take effect immediately and affect all users, so be thorough in testing before deploying to production.
