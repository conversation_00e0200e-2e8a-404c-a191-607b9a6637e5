# Firestore 安全規則測試用例

## 概述
本文檔記錄了 Grid POS 系統的 Firestore 安全規則測試用例，涵蓋所有用戶角色和主要功能場景。

## 測試環境設置
- Firebase 專案：grid-pos
- 測試工具：Firebase Rules Playground
- 測試數據：使用測試前綴避免影響生產數據

## 用戶角色定義

### 1. 管理員 (Admin)
- 用戶 ID: `test_admin_001`
- 角色: `admin`
- 權限: 完整的系統管理權限

### 2. 租戶 (Tenant)
- 用戶 ID: `test_tenant_001`
- 角色: `tenant`
- 租戶 ID: `test_tenant_001`
- 店鋪 ID: `test_store_001`

### 3. 收銀員 (Cashier)
- 用戶 ID: `test_cashier_001`
- 角色: `cashier`
- 店鋪 ID: `test_store_001`

### 4. 未授權用戶 (Unauthorized)
- 用戶 ID: `test_user_001`
- 角色: `pending_approval`

## 測試用例

### A. 用戶管理 (/users/{userId})

#### A1. 讀取權限測試
- ✅ **管理員讀取任何用戶數據** - 應該成功
- ✅ **用戶讀取自己的數據** - 應該成功
- ❌ **用戶讀取他人數據** - 應該失敗
- ❌ **未認證用戶讀取數據** - 應該失敗

#### A2. 創建權限測試
- ✅ **新用戶創建自己的文檔（pending_approval）** - 應該成功
- ❌ **用戶創建他人文檔** - 應該失敗
- ❌ **用戶創建時設置非 pending_approval 角色** - 應該失敗

#### A3. 更新權限測試
- ✅ **管理員更新任何用戶角色** - 應該成功
- ✅ **用戶更新自己的資料（不改變角色）** - 應該成功
- ❌ **用戶嘗試更改自己的角色** - 應該失敗

### B. 店鋪管理 (/stores/{storeId})

#### B1. 讀取權限測試
- ✅ **任何認證用戶讀取店鋪數據** - 應該成功
- ❌ **未認證用戶讀取店鋪數據** - 應該失敗

#### B2. 寫入權限測試
- ✅ **管理員創建/更新/刪除店鋪** - 應該成功
- ❌ **非管理員創建/更新/刪除店鋪** - 應該失敗

### C. 租戶管理 (/stores/{storeId}/tenants/{tenantId})

#### C1. 讀取權限測試
- ✅ **管理員讀取任何租戶數據** - 應該成功
- ✅ **租戶讀取自己的數據** - 應該成功
- ❌ **租戶讀取其他租戶數據** - 應該失敗

#### C2. 寫入權限測試
- ✅ **管理員創建/更新/刪除租戶** - 應該成功
- ❌ **租戶創建/更新/刪除租戶數據** - 應該失敗

### D. 商品管理 (/stores/{storeId}/tenants/{tenantId}/products/{productId})

#### D1. 讀取權限測試
- ✅ **管理員讀取任何商品** - 應該成功
- ✅ **租戶讀取自己的商品** - 應該成功
- ✅ **認證用戶讀取活躍商品** - 應該成功
- ❌ **租戶讀取其他租戶的商品** - 應該失敗

#### D2. 寫入權限測試
- ✅ **管理員創建/更新/刪除任何商品** - 應該成功
- ✅ **租戶創建/更新/刪除自己的商品** - 應該成功
- ❌ **租戶操作其他租戶的商品** - 應該失敗

#### D3. 數據驗證測試
- ✅ **創建商品時所有必填字段正確** - 應該成功
- ❌ **創建商品時缺少必填字段** - 應該失敗
- ❌ **創建商品時 storeId/tenantId 不匹配路徑** - 應該失敗

### E. 格位管理 (/stores/{storeId}/grids/{gridId})

#### E1. 讀取權限測試
- ✅ **任何認證用戶讀取格位數據** - 應該成功
- ❌ **未認證用戶讀取格位數據** - 應該失敗

#### E2. 寫入權限測試
- ✅ **管理員創建/更新/刪除格位** - 應該成功
- ❌ **非管理員創建/更新/刪除格位** - 應該失敗

### F. 銷售管理 (/stores/{storeId}/sales/{saleId})

#### F1. 讀取權限測試
- ✅ **管理員讀取任何銷售記錄** - 應該成功
- ✅ **收銀員讀取自己創建的銷售記錄** - 應該成功
- ✅ **租戶讀取店鋪內的銷售記錄（用於儀表板）** - 應該成功
- ❌ **收銀員讀取他人創建的銷售記錄** - 應該失敗
- ❌ **其他店鋪的用戶讀取銷售記錄** - 應該失敗

#### F2. 創建權限測試
- ✅ **收銀員創建銷售記錄** - 應該成功
- ✅ **管理員創建銷售記錄** - 應該成功
- ❌ **租戶創建銷售記錄** - 應該失敗

#### F3. 更新權限測試
- ✅ **收銀員取消自己的銷售記錄** - 應該成功
- ✅ **管理員取消任何銷售記錄** - 應該成功
- ❌ **修改銷售記錄的其他字段** - 應該失敗

### G. 每日摘要 (/daily_summaries/{docId})

#### G1. 讀取權限測試
- ✅ **管理員讀取每日摘要** - 應該成功
- ❌ **非管理員讀取每日摘要** - 應該失敗

#### G2. 寫入權限測試
- ✅ **管理員創建/更新每日摘要** - 應該成功
- ❌ **非管理員創建/更新每日摘要** - 應該失敗

#### G3. 數據驗證測試
- ✅ **文檔 ID 符合 yyyyMMdd_storeId 格式** - 應該成功
- ❌ **文檔 ID 格式不正確** - 應該失敗
- ❌ **缺少必填字段** - 應該失敗

### H. 商品鎖定 (/stores/{storeId}/tenants/{tenantId}/products/{productId}/locks/{lockId})

#### H1. 讀取權限測試
- ✅ **任何認證用戶檢查商品鎖定狀態** - 應該成功

#### H2. 創建權限測試
- ✅ **認證用戶創建自己的鎖定** - 應該成功
- ❌ **用戶創建他人的鎖定** - 應該失敗

#### H3. 刪除權限測試
- ✅ **用戶刪除自己的鎖定** - 應該成功
- ❌ **用戶刪除他人的鎖定** - 應該失敗

## 儀表板和報告功能測試

### I. 儀表板數據訪問

#### I1. 管理員儀表板
- ✅ **管理員訪問所有店鋪銷售數據** - 應該成功
- ✅ **管理員訪問所有商品庫存數據** - 應該成功
- ✅ **管理員生成每日摘要** - 應該成功

#### I2. 租戶儀表板
- ✅ **租戶訪問店鋪銷售數據（過濾自己的商品）** - 應該成功
- ✅ **租戶訪問自己的商品庫存數據** - 應該成功
- ❌ **租戶訪問其他租戶的商品數據** - 應該失敗

### J. PDF 報告生成

#### J1. 銷售報告
- ✅ **管理員生成任何日期的銷售報告** - 應該成功
- ❌ **租戶生成銷售報告** - 應該失敗

#### J2. 庫存報告
- ✅ **管理員生成全店庫存報告** - 應該成功
- ✅ **租戶生成自己的庫存報告** - 應該成功
- ❌ **租戶生成其他租戶的庫存報告** - 應該失敗

## 測試執行記錄

### 執行日期：[待填入]
### 執行人員：[待填入]
### 測試結果：[待填入]

| 測試用例 | 預期結果 | 實際結果 | 狀態 | 備註 |
|---------|---------|---------|------|------|
| A1 | 通過 | [待測試] | ⏳ | |
| A2 | 通過 | [待測試] | ⏳ | |
| ... | ... | ... | ... | |

## 已知問題和限制

1. **租戶銷售數據訪問**：目前允許租戶讀取店鋪內所有銷售數據，未來可能需要更精細的過濾
2. **商品鎖定機制**：需要定期清理過期的鎖定記錄
3. **性能考量**：安全規則中的 `get()` 操作可能影響性能，需要監控

## 建議改進

1. 考慮實施更精細的租戶銷售數據過濾
2. 添加審計日誌功能
3. 實施速率限制防止濫用
4. 定期審查和更新安全規則
