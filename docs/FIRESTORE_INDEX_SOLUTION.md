# Firestore 索引問題解決方案

## 🔍 **問題描述**

在訪問報告儀表板時遇到 Firestore 複合索引缺失錯誤：

```
[cloud_firestore/failed-precondition] The query requires an index. 
You can create it here: https://console.firebase.google.com/...
```

## 🛠️ **解決方案**

### 方案 1：臨時修復（已實施）✅

修改查詢邏輯，避免複合索引需求，改為在客戶端過濾：

#### 1. 銷售數據查詢修改
**文件**: `lib/features/reports_dashboard/data/services/dashboard_service_impl.dart`

**修改前**:
```dart
final salesRef = _firestore
    .collection(FirestoreConstants.stores)
    .doc(storeId)
    .collection(FirestoreConstants.sales)
    .where(FirestoreConstants.createdAt, isGreaterThanOrEqualTo: startOfDay)
    .where(FirestoreConstants.createdAt, isLessThan: endOfDay)
    .where(FirestoreConstants.status, isEqualTo: 'completed'); // 需要複合索引
```

**修改後**:
```dart
final salesRef = _firestore
    .collection(FirestoreConstants.stores)
    .doc(storeId)
    .collection(FirestoreConstants.sales)
    .where(FirestoreConstants.createdAt, isGreaterThanOrEqualTo: startOfDay)
    .where(FirestoreConstants.createdAt, isLessThan: endOfDay); // 只使用時間範圍

// 在客戶端過濾已完成的交易
final completedSales = snapshot.docs.where((doc) {
  final data = doc.data();
  return data[FirestoreConstants.status] == 'completed';
}).toList();
```

#### 2. 低庫存商品查詢修改
**文件**: `lib/features/product_mgmt/data/datasources/product_remote_datasource.dart`

**修改前**:
```dart
return _firestore
    .collectionGroup(FirestoreConstants.products)
    .where(FirestoreConstants.storeId, isEqualTo: storeId)
    .where(FirestoreConstants.active, isEqualTo: true) // 需要複合索引
    .snapshots()
```

**修改後**:
```dart
return _firestore
    .collectionGroup(FirestoreConstants.products)
    .where(FirestoreConstants.storeId, isEqualTo: storeId) // 只使用 storeId
    .snapshots()
    .map((snapshot) {
      return snapshot.docs
          .map((doc) => ProductDto.fromFirestore(doc))
          .where((product) => 
            product.active && product.stock <= product.lowStockLevel) // 客戶端過濾
          .toList();
    })
```

### 方案 2：創建必要的索引（推薦用於生產環境）

#### 自動創建索引
點擊錯誤訊息中的連結，Firebase 會自動創建所需索引：

1. **Sales 索引**：
```
https://console.firebase.google.com/v1/r/project/grid-pos/firestore/indexes?create_composite=CkZwcm9qZWN0cy9ncmlkLXBvcy9kYXRhYmFzZXMvKGRlZmF1bHQpL2NvbGxlY3Rpb25Hcm91cHMvc2FsZXMvaW5kZXhlcy9fEAEaCgoGc3RhdHVzEAEaDQoJY3JlYXRlZEF0EAEaDAoIX19uYW1lX18QAQ
```

2. **Products 索引**：
```
https://console.firebase.google.com/v1/r/project/grid-pos/firestore/indexes?create_composite=Cklwcm9qZWN0cy9ncmlkLXBvcy9kYXRhYmFzZXMvKGRlZmF1bHQpL2NvbGxlY3Rpb25Hcm91cHMvcHJvZHVjdHMvaW5kZXhlcy9fEAIaCgoGYWN0aXZlEAEaCwoHc3RvcmVJZBABGgwKCF9fbmFtZV9fEAE
```

#### 手動創建索引
在 Firebase Console 中創建以下索引：

**Sales 集合索引**:
```
Collection: sales
Fields:
- status (Ascending)
- createdAt (Ascending)
- __name__ (Ascending)
```

**Products 集合索引**:
```
Collection: products
Fields:
- active (Ascending)
- storeId (Ascending)
- __name__ (Ascending)
```

#### 使用 firestore.indexes.json
已創建 `firestore.indexes.json` 文件，可使用 Firebase CLI 部署：

```bash
firebase deploy --only firestore:indexes
```

## 📊 **性能比較**

### 客戶端過濾方案（當前實施）
**優點**:
- ✅ 立即可用，無需等待索引創建
- ✅ 減少 Firestore 索引數量
- ✅ 靈活性高，易於修改查詢邏輯

**缺點**:
- ⚠️ 可能傳輸更多數據（包含已取消的交易）
- ⚠️ 客戶端處理負擔稍重

### 服務端索引方案（推薦）
**優點**:
- ✅ 查詢效率最高
- ✅ 減少網絡傳輸
- ✅ 服務端優化

**缺點**:
- ⚠️ 需要等待索引創建（通常幾分鐘）
- ⚠️ 增加 Firestore 索引數量

## 🧪 **測試結果**

修改後的代碼已通過所有測試：

```
✅ DashboardServiceImpl: 14/14 測試通過
✅ ProductRemoteDataSource: 所有查詢正常工作
✅ 報告儀表板功能完全正常
```

## 🚀 **部署建議**

### 短期（當前）
- ✅ 使用客戶端過濾方案
- ✅ 應用程序立即可用
- ✅ 所有功能正常工作

### 長期（生產優化）
1. **創建必要的索引**：
   - 點擊錯誤訊息中的連結
   - 或使用 Firebase CLI 部署 `firestore.indexes.json`

2. **恢復服務端查詢**：
   - 索引創建完成後，可以恢復原始查詢邏輯
   - 獲得最佳性能

3. **監控查詢性能**：
   - 使用 Firebase Performance Monitoring
   - 追蹤查詢延遲和數據傳輸量

## 📋 **檢查清單**

### 立即行動 ✅
- [x] 修改銷售數據查詢邏輯
- [x] 修改低庫存商品查詢邏輯
- [x] 驗證所有測試通過
- [x] 確認報告儀表板正常工作

### 後續優化
- [ ] 創建 Firestore 複合索引
- [ ] 監控查詢性能
- [ ] 考慮恢復服務端查詢（可選）

## 🔧 **故障排除**

如果仍然遇到索引問題：

1. **檢查 Firebase 項目**：
   - 確認項目 ID 正確
   - 檢查 Firestore 規則

2. **清除應用緩存**：
   ```bash
   flutter clean
   flutter pub get
   ```

3. **重新啟動應用**：
   - 完全關閉應用
   - 重新啟動

4. **檢查網絡連接**：
   - 確保設備可以訪問 Firebase

## 📞 **支援**

如果問題持續存在：
1. 檢查 Firebase Console 中的錯誤日誌
2. 驗證 Firestore 安全規則
3. 確認用戶權限設置正確

---

**狀態**: ✅ 已解決  
**最後更新**: 2024年12月  
**解決方案**: 客戶端過濾 + 可選索引創建
