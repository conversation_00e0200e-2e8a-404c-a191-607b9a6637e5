## 項目規格書 v1.4 (Revised)

---

## 目錄

1.  項目概覽
2.  技術棧與工程標準
3.  Firebase 架構與安全
    3.1 Collection 佈局 (樹狀)
    3.2 詳細文檔字段
    3.3 客戶端與管理端邏輯 (取代雲函數流程)
    3.4 安全規則 (高級)
    3.5 複合索引與 TTL
    3.6 Firebase 專案環境、App Distribution 與手動部署流程
4.  應用層模塊分解
    4.1 項目概覽 (lib 目錄)
    4.2 core/
    4.3 shared/
    4.4 features/
    4.5 Riverpod 依賴鏈示例
    4.6 router/
    4.7 l10n/
    4.8 推薦測試目錄結構
    4.9 依賴管理提示
    4.10 文件名與命名約定
5.  開發階段規劃
6.  功能規格 (階段一)
    6.1 認證
    6.2 租戶管理
    6.3 商品管理
    6.4 POS
    6.5 儀錶板與報告
    6.6 格位管理 (Grid Management) - 管理員操作
7.  性能與並發策略
8.  UX / UI 指南
9.  DevOps 與測試矩陣 (簡化版 - 手動流程)
10. 路線圖 (階段二 / 三)

---

## 1 — 項目概覽

| 目標         | 描述                                                                                 |
| ------------ | ------------------------------------------------------------------------------------ |
| **項目名稱** | **grid-pos**                                                                         |
| **商業場景** | 多租戶實體「格仔舖」：每個店鋪 100+ 租戶，10+ 店鋪地點。                             |
| **核心功能** | 租戶與商品管理，格位管理，POS 銷售，條碼掃描，應用內生成 PDF 報告，藍牙打印。        |
| **關鍵需求** | **強制在線操作**，實時同步，管理員/租戶權限，並發控制以防止超賣，**Material 3 UI**。 |

---

## 2 — 技術棧與工程標準

| 層級         | 選擇                                                                                             | 關鍵點                                                                                          |
| ------------ | ------------------------------------------------------------------------------------------------ | ----------------------------------------------------------------------------------------------- |
| 前端         | **Flutter 3.29.3**                                                                               | iOS / Android / Web (PWA = 階段三)。                                                            |
| 狀態管理     | **Riverpod (2.6.1 或更高最新穩定版)**                                                            | 無 `BuildContext` 依賴，易於測試。                                                              |
| 數據模型     | **Freezed + JsonSerializable**                                                                   | 自動 `copyWith` / `toJson`。                                                                    |
| 可視化       | `fl_chart`                                                                                       | 折線圖、餅圖、條形圖。                                                                          |
| **網絡連接** | `connectivity_plus` **(6.1.4 或更高最新穩定版)**                                                 | **啟動時檢測網絡，無網絡則提示錯誤。**                                                          |
| 後端         | **Firebase** (Auth / Firestore / App Distribution)                                               | **直接使用 Firebase 生產/開發環境 (專案名: grid-pos)。** 雲函數將不由 Firebase Functions 執行。 |
| 標準         | Dart 格式化 (`dart format`), 基本 Linting (Flutter 默認)。 **不強制使用 `very_good_analysis`。** | 保持代碼整潔和可讀性。                                                                          |

_備註：請始終參考官方發布的最新穩定版本 (除 Flutter 版本已指定為 3.29.3)，並根據項目實際情況進行調整。_

---

## 3 — Firebase 架構與安全

### 3.1 Collection 佈局 (樹狀總結)

```
stores/{storeId}
 ├─ tenants/{tenantId}
 │    └─ products/{productId}
 │         └─ locks/{lockId} // lockId 可以是 <deviceId> 或 <checkoutAttemptId>
 ├─ grids/{gridId}
 └─ sales/{saleId}

users/{userId} // 用於存儲角色

daily_summaries/{yyyyMMdd}_{storeId} // 由管理員工具或按需客戶端計算填充
global_counters/{counterName}/shards/{shardId}
```

### 3.2 詳細文檔字段

```text
stores/{storeId}                   // 店鋪
  name            : string
  address         : string
  timezone        : string  // Asia/Taipei
  gridCount       : number
  createdAt       : Timestamp
  updatedAt       : Timestamp

stores/{storeId}/tenants/{tenantId}    // 租戶
  name            : string
  contact         : {phone,email}
  grids           : array<string>      // 已分配的格位
  active          : bool               // 合約是否生效
  contract        : {start,end,rent}   // 租賃條款及月租
  createdAt       : Timestamp
  updatedAt       : Timestamp

stores/{storeId}/tenants/{tenantId}/products/{productId}
  sku             : string             // 店鋪內租戶下唯一
  name            : string
  barcode         : string             // 店鋪內應唯一 (或全局唯一, 根據配置)
  price           : number             // 售價
  cost            : number?            // 成本價 (可選, 管理員/租戶可見)
  stock           : number
  gridId          : string             // 關聯的格位 ID
  lowStockLevel   : number             // 低庫存閾值, 例如默認為 5
  active          : bool               // 商品是否激活/上架, 默認為 true
  storeId         : string             // (冗餘字段) 所屬店鋪 ID, 方便查詢和規則
  tenantId        : string             // (冗餘字段) 所屬租戶 ID, 方便查詢和規則
  createdAt       : Timestamp
  updatedAt       : Timestamp

stores/{storeId}/tenants/{tenantId}/products/{productId}/locks/{lockId}
  byUserId        : string             // 執行結帳的已認證用戶 ID
  byDeviceId      : string             // 創建鎖的設備 ID
  expire          : Timestamp          // TTL 2 分鐘

stores/{storeId}/grids/{gridId}
  code            : string             // 格位編號顯示 (店鋪內應唯一)
  tenantId        : string?            // null = 空格位
  size            : string             // S / M / L
  createdAt       : Timestamp
  updatedAt       : Timestamp

stores/{storeId}/sales/{saleId}
  storeId         : string
  tenantId        : string             // 非規範化，方便租戶特定查詢
  cashierId       : string             // 執行銷售的用戶 Firebase Auth UID
  status          : string             // completed / cancelled
  totalAmount     : number
  items           : [{productId, tenantId, qty, price, name, sku}] // 非規範化更多信息以生成收據
  paymentType     : string             // cash / card ...
  printed         : bool
  createdAt       : Timestamp
  updatedAt       : Timestamp

daily_summaries/{date_store} // 此處數據實時性較低，由管理員操作更新或按需計算
  totalSales      : number
  transactions    : number
  lowStockCount   : number
  updatedAt       : Timestamp

global_counters/{counterName}/shards/{shardId}
  value           : number

users/{userId} // 新增 collection 存儲角色
  email           : string  // 方便識別
  role            : string  // 'admin', 'tenant', 'cashier'
  tenantId        : string? // 如果角色是 'tenant'，對應 stores/{storeId}/tenants/{tenantId}
  storeId         : string? // 如果角色是 'cashier' 或 'admin' (針對特定店鋪管理員)
  displayName     : string? // 用戶顯示名稱 (可選)
  createdAt       : Timestamp
  updatedAt       : Timestamp
```

> **設計亮點 (調整後)**
>
> -   報告讀取頻繁，寫入較少。`daily_summaries` 將由管理員觸發的應用內流程更新，或報告工具直接查詢原始銷售數據。
> -   `global_counters` 分片處理熱點寫入。
> -   悲觀鎖由 **客戶端應用** 在結帳時寫入，通過 **Firestore TTL** 自動清除過期鎖。
> -   **用戶角色** 存儲在 `users/{userId}` collection，由安全規則讀取。

---

### 3.3 客戶端與管理端邏輯 (取代雲函數流程)

| 觸發 / 操作                      | 負責方                           | 核心邏輯                                                                                                                                                                                                                   | 備註                                                                                     |
| -------------------------------- | -------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------- |
| **結帳 (創建銷售)**              | POS 客戶端應用                   | 1. **Firestore 事務 (強制在線):** <br/> a. 讀取購物車中商品的庫存。 <br/> b. 驗證庫存可用性。 <br/> c. 創建 `sales` 文檔。 <br/> d. 批量更新 (減少) 每個商品的 `products.stock`。 <br/> e. (可選) 更新 `daily_summaries`。 | 銷售和庫存扣減的原子性是關鍵。                                                           |
| **商品創建/更新**                | 租戶客戶端應用 / 管理員應用      | 1. 客戶端準備 `ProductEntity` 數據, **包含正確的 `storeId` 和 `tenantId` 冗餘字段**。<br/>2. 客戶端在保存前執行條碼和SKU唯一性檢查 (調用 `ProductRepository` 的相關方法)。<br/>3. 調用 `ProductRepository` 保存商品數據到 Firestore。 | SKU 可由客戶端服務生成或基於 `global_counters`。條碼唯一性主要由客戶端保證。             |
| **低庫存通知**                   | POS 客戶端應用 / 管理員應用      | 客戶端 UI 在查看商品或銷售後指示低庫存。管理員應用可以運行報告查找所有低庫存商品。                                                                                                                                         | **提醒為被動的 (UI 指示器) 或基於顯式檢查/報告。**                                       |
| **格位分配**                     | 管理員用戶 (通過應用)            | **Firestore 事務 (強制在線):** <br/> a. 更新 `grids/{gridId}.tenantId`。 <br/> b. 更新 `tenants/{tenantId}.grids` 數組。                                                                                                   | 需要管理員權限，由安全規則強制執行。                                                     |
| **格位 CRUD**                    | 管理員用戶 (通過應用)            | 管理員可通過應用內界面創建、讀取、更新 (編號、尺寸) 和刪除 (僅限未分配的) 格位。                                                                                                                                         | 需要管理員權限，由安全規則強制執行。                                                     |
| **用戶角色分配**                 | 管理員用戶 (通過應用) 或外部腳本 | 用戶註冊後 (或手動)：管理員使用應用內的界面寫入 `users/{userId}` 文檔以設置 `role`, `tenantId` 等。                                                                                                                        | Auth 觸發器不可用。角色在 Firestore 中管理。初始角色可能是 'pending_approval' 或默認值。 |
| **清除過期鎖**                   | Firestore TTL                    | Firestore 在 `products/{...}/locks/*` 中 `expire` 字段上的內建 TTL 策略將刪除過期文檔。                                                                                                                                    | 無需自定義調度器。依賴 Firestore 原生 TTL。                                              |
| **數據導出至 BigQuery (階段三)** | 外部腳本/流程                    | 在外部服務器/ cron 作業上運行的預定腳本將定期從 Firestore 讀取並寫入 BigQuery。                                                                                                                                            | 這不再是 Firebase 原生的自動化流程。需要管理外部流程。                                   |

---

### 3.4 安全規則 (高級簡明版 - 重度修改)

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{db}/documents {

    // 輔助函數：從 'users' collection 獲取用戶的角色數據
    function getUserRoleData(userId) {
      return get(/databases/$(db)/documents/users/$(userId)).data;
    }

    function isAdmin() {
      let userData = getUserRoleData(request.auth.uid);
      return request.auth != null && userData.role == 'admin';
    }
    function isTenant(tenantDocIdFromPath) { // tenantDocIdFromPath 是 tenants collection 的 ID
      let userData = getUserRoleData(request.auth.uid);
      return request.auth != null && userData.role == 'tenant' && userData.tenantId == tenantDocIdFromPath;
    }
    function isCashier(storeIdFromPath) { // 假設收銀員有 'cashier' 角色並與 storeId 關聯
        let userData = getUserRoleData(request.auth.uid);
        return request.auth != null && userData.role == 'cashier' && userData.storeId == storeIdFromPath;
    }

    match /users/{userId} {
      allow read: if request.auth != null && (request.auth.uid == userId || isAdmin());
      allow write: if isAdmin(); // 只有管理員可以更改角色 (包含創建和更新)
      // 允許用戶創建自己的初始 'pending_approval' 文檔
      // allow create: if request.auth != null && request.auth.uid == userId &&
      //                 request.resource.data.email == request.auth.token.email && // 確保 email 匹配
      //                 request.resource.data.role == 'pending_approval' &&
      //                 !exists(/databases/$(db)/documents/users/$(userId)); // 確保文檔不存在
    }

    match /stores/{storeId} {
      allow read: if request.auth != null;
      allow write: if isAdmin(); // 只有 admin 可以創建/更新/刪除 store 文檔本身

      match /tenants/{tenantId} {
        allow read: if isAdmin() || isTenant(tenantId); // isTenant 檢查 request.auth.uid 的 tenantId 是否與路徑中的 tenantId 匹配
        allow write: if isAdmin(); // 只有 admin 可以創建/更新/刪除 tenant

        match /products/{productId} {
          allow read: if (request.auth != null && (resource.data.active == true || resource.data.active == null) ) // 任何人都可以讀取 active 商品
                        || isAdmin()
                        || isTenant(tenantId); // 租戶可以讀取自己的所有商品

          // 租戶可以創建/更新/刪除自己的商品，管理員可以操作任何商品
          allow create, update, delete: if isAdmin() || isTenant(tenantId);

          // 數據校驗：確保寫入的數據符合業務邏輯
          // 注意：校驗應在 create 和 update 時都執行
          // 對於 create, resource.data 不存在, 要用 request.resource.data
          // 對於 update, resource.data 存在, 可以用它來比較或確保某些字段不變
          allow write: if (isAdmin() || isTenant(tenantId)) &&
                        request.resource.data.name.size() > 0 &&
                        request.resource.data.price >= 0 &&
                        request.resource.data.stock >= 0 &&
                        request.resource.data.lowStockLevel >= 0 &&
                        request.resource.data.gridId.size() > 0 &&
                        request.resource.data.storeId == storeId &&      // 驗證冗餘的 storeId
                        request.resource.data.tenantId == tenantId;     // 驗證冗餘的 tenantId
                        // 條碼/SKU唯一性很難在規則中可靠地強制執行，主要依賴客戶端檢查。

          match /locks/{lockId} {
            allow read: if request.auth != null; // 任何認證用戶都可以讀取鎖 (例如檢查商品是否被鎖定)
            // 只有鎖的創建者 (byUserId) 才能創建和刪除鎖
            allow create: if request.auth != null && request.resource.data.byUserId == request.auth.uid;
            allow delete: if request.auth != null && resource.data.byUserId == request.auth.uid; // resource.data.byUserId 指的是現有文檔的字段
          }
        }
      }

      match /grids/{gridId} {
        allow read: if request.auth != null; // 任何認證用戶都可以讀取格位信息
        // 只有管理員可以創建、更新、刪除格位。
        // 刪除格位時，客戶端應檢查 tenantId 是否為 null。
        // 安全規則也可以添加 resource.data.tenantId == null 的條件來強化 delete，但客戶端檢查更友好。
        allow create, update, delete: if isAdmin();
      }

      match /sales/{saleId} {
        allow read: if isAdmin() ||
                      (isTenant(resource.data.tenantId)) || // 租戶可以讀取自己商品的銷售記錄
                      (isCashier(storeId) && (resource.data.cashierId == request.auth.uid || isAdmin())); // 收銀員可以讀取自己處理的或所有銷售 (如果也是管理員)

        // 收銀員或管理員可以創建銷售記錄
        allow create: if ( isCashier(storeId) &&
                            request.resource.data.cashierId == request.auth.uid &&
                            request.resource.data.storeId == storeId ) ||
                        ( isAdmin() && // 管理員也可以創建銷售
                            request.resource.data.cashierId == request.auth.uid && // 假設管理員操作時 cashierId 也是自己
                            request.resource.data.storeId == storeId );

        // 收銀員或管理員可以更新銷售記錄 (例如修改狀態為 cancelled)
        allow update: if (isCashier(storeId) && request.resource.data.cashierId == request.auth.uid) || isAdmin();
        // 通常不允許刪除銷售記錄，但如果需要，可以添加 delete 規則
      }
    }

    match /daily_summaries/{docId} {
      allow read: if isAdmin();
      allow write: if isAdmin(); // 只有管理員可以寫入每日摘要
    }

    match /global_counters/{counterName}/shards/{shardId} {
        allow read, write: if request.auth != null; // 任何認證用戶都可以讀寫分片計數器 (例如用於生成 SKU)
    }
  }
}
```

**部署規則到 Firebase (生產環境 - 專案: grid-pos):**

```bash
# 直接將 firestore.rules 文件部署到你的 Firebase 生產專案 grid-pos
firebase deploy --only firestore:rules --project grid-pos
```

**重要：直接部署到生產環境意味著任何規則中的錯誤都可能立即影響你的線上用戶和數據安全。請務必謹慎操作，並在部署後進行徹底測試。**

---

### 3.5 複合索引與 TTL 策略

| 場景         | 查詢                                                                                                 | 推薦索引                                                                                                                                   |
| ------------ | ---------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------ |
| 低庫存列表   | `products` collection: `where storeId==A && tenantId==T && active==true && stock <= lowStockLevel orderBy updatedAt` | `storeId ASC, tenantId ASC, active ASC, stock ASC, updatedAt DESC` (or specific order based on UI needs for `lowStockLevel` filtering) |
| 租戶商品列表 | `products` collection: `where storeId==A && tenantId==T && active==true orderBy name ASC`               | `storeId ASC, tenantId ASC, active ASC, name ASC`                                                                                          |
| 條碼查找     | `products` collection: `where storeId==A && barcode==B` (for client-side uniqueness check)           | `storeId ASC, barcode ASC` (If barcode is unique per store)                                                                                |
| SKU 查找     | `products` collection: `where storeId==A && tenantId==T && sku==S` (for client-side uniqueness check) | `storeId ASC, tenantId ASC, sku ASC`                                                                                                       |
| 租戶銷售歷史 | `sales` collection: `where tenantId==B orderBy createdAt DESC limit 50`                              | `tenantId ASC, createdAt DESC`                                                                                               |
| 格位列表查詢 | `grids` collection: `where storeId==A orderBy code ASC`                                              | `storeId ASC, code ASC`                                                                                                      |
|              | `grids` collection: `where storeId==A && tenantId==null orderBy code ASC` (查可用格位)                | `storeId ASC, tenantId ASC, code ASC`                                                                                        |
| 悲觀鎖 TTL   | **Firestore TTL** 指向 `products/{...}/locks/*` 中的 `expire` 字段                                   | 自動清除過期文檔                                                                                                             |
| 用戶角色查詢 | `users` collection: `where email == '...'` (用於管理面板查找)                                        | `email ASC` (如果需要)                                                                                                       |

---

### 3.6 Firebase 專案環境、App Distribution 與手動部署流程

-   **開發環境**: 直接連接到你的 Firebase **生產專案 grid-pos**。
    -   使用 `flutterfire configure --project=grid-pos` 將 Flutter 應用連接到此 Firebase 專案。
-   **Firebase App Distribution**: 用於內部分發測試版本 (專案: grid-pos)。
-   **部署流程 (手動)**:
    -   應用程式構建 (APK/IPA) 和分發 (包括到 App Distribution) 將**手動執行**。
    -   Firebase 安全規則的部署應**手動執行**並極其謹慎處理，直接部署到生產專案 **grid-pos**。
    -   建議在團隊內部建立嚴格的審查和部署 SOP（標準作業程序）。
-   **核心 Firebase 服務**: Auth, Firestore, App Distribution (均在 **grid-pos** 專案下)。

---

## 4 — 應用層模塊分解

> **原則**
>
> -   **高度模塊化**: 每個功能可以獨立添加/移除而不影響其他模塊。
> -   **乾淨架構**: `data → domain → presentation` 三層；UI 不直接與 Firebase 交互。
> -   **Riverpod‑First**: 所有依賴 (Repo / UseCase / ViewModel) 通過 Providers 注入，方便測試時替換 mock。

---

### 4.1 項目概覽 (lib 目錄)

```text
lib/
 ├─ core/               # 全局工具與基礎設施
 │   ├─ constants/
 │   ├─ errors/
 │   ├─ utils/
 │   ├─ theme/
 │   ├─ firebase/       # Firebase 服務封裝 (FirestoreService, AuthService)
 │   └─ network/        # 網絡狀態檢測
 ├─ shared/             # 跨功能 UI / 輔助工具
 ├─ features/           # 按乾淨架構分層的單個業務功能
 │   ├─ auth/
 │   ├─ tenant_mgmt/    # 租戶管理與格位分配
 │   ├─ grid_mgmt/      # (新增) 格位本身的 CRUD 管理
 │   ├─ product_mgmt/
 │   ├─ pos/
 │   ├─ reports_dashboard/
 │   ├─ settings/
 │   └─ admin/          # (新增) 管理員特定功能, 例如用戶管理
 ├─ l10n/               # Flutter Intl (arb)
 ├─ router/             # GoRouter 配置
 └─ main.dart           # App 入口點, ProviderScope, 初始網絡檢測
```

---

### 4.2 core/ (純 Dart, 或最小 Flutter 依賴)

| 子文件夾     | 內容                                                 | 示例文件                 |
| ------------ | ---------------------------------------------------- | ------------------------ |
| `constants/` | API 路徑 (如果適用), 字符串鍵, 默認顏色              | `app_constants.dart`     |
| `errors/`    | 統一異常 (例如 `NetworkFailure`, `FirestoreFailure`) | `failure.dart`           |
| `utils/`     | DateTimeUtils, Validators, Logger                    | `date_time_utils.dart`   |
| `theme/`     | Material 3 `ColorScheme.fromSeed`                    | `theme.dart`             |
| `firebase/`  | 封裝第三方: `FirestoreService`, `AuthService`        | `firestore_service.dart` |
| `network/`   | `NetworkInfo` 類 (使用 `connectivity_plus`)          | `network_info.dart`      |

---

### 4.3 shared/ (跨功能 Flutter Widgets & Extensions)

| 類型       | 示例                                                               | 用途           |
| ---------- | ------------------------------------------------------------------ | -------------- |
| Widgets    | `AppButton`, `AppTextField`, `EmptyStateWidget`, `NoNetworkWidget` | 維持 UI 一致性 |
| Extensions | `context.showSnackbar()`, `num.currencyFormat()`                   | 簡潔代碼       |
| Mixins     | `FormMixin` (自動釋放 controller)                                  | 清理表單       |

---

### 4.4 features/ (每個功能包具有三層結構)

> **路徑示例**: `features/product_mgmt/` > `auth`, `tenant_mgmt`, `grid_mgmt`, `pos`, `reports_dashboard`, `settings`, `admin`… 等功能均複製此結構。
>
> **新增 `features/grid_mgmt/` 和 `features/admin/` 模塊**，也將遵循類似的三層結構。

```text
features/
 └─ product_mgmt/  // (示例，其他功能模塊類似)
     ├─ data/
     │   ├─ datasources/
     │   │    └─ product_remote_ds.dart  // 與 Firestore 交互
     │   ├─ models/                      // (更名 DTO -> models, 因為它們是數據層模型)
     │   │    └─ product_model.dart         # Freezed & JsonSerializable (對應 Firestore 結構)
     │   └─ repositories/
     │        └─ product_repo_impl.dart
     │
     ├─ domain/
     │   ├─ entities/
     │   │    └─ product.dart               # Freezed Entity (業務領域模型)
     │   ├─ repositories/
     │   │    └─ product_repository.dart    # abstract
     │   └─ usecases/                      # (可選, 如果業務邏輯複雜)
     │        ├─ get_products.dart
     │        └─ update_stock.dart
     │
     └─ presentation/
         ├─ providers/  # Riverpod Providers
         │    ├─ product_list_provider.dart # StateNotifier / Notifier
         │    └─ product_form_provider.dart
         ├─ pages/      # 頁面級 Widgets
         │    ├─ product_list_page.dart
         │    └─ product_form_page.dart
         └─ widgets/    # 頁面內可複用 Widgets
              ├─ product_card.dart
              └─ stock_badge.dart
```

**描述**

| 層級           | 關鍵點                                                                         |
| -------------- | ------------------------------------------------------------------------------ |
| `data`         | 僅在此層與 Firebase / REST / 緩存交互；可轉換為 **Data Models (DTOs)**。       |
| `domain`       | 純 Dart → 無外部框架依賴；`Entity` & `UseCase` 可被任何框架使用。              |
| `presentation` | Flutter Widgets；Riverpod `Notifier` 僅依賴 **UseCase** 或 **Repository**，不直接調用 Firebase。 |

---

### 4.5 Riverpod 依賴鏈示例

```dart
// core/firebase/firestore_service.dart
final firebaseFirestoreProvider = Provider<FirebaseFirestore>((ref) {
  return FirebaseFirestore.instance;
});

// features/product_mgmt/domain/entities/product.dart
// @freezed class Product with _$Product { ... }

// features/product_mgmt/domain/repositories/product_repository.dart
abstract class ProductRepository {
  Stream<List<Product>> watchAllProducts(String storeId, String tenantId);
  Future<void> updateStock(String storeId, String tenantId, String productId, int newStock);
}

// features/product_mgmt/data/models/product_model.dart
// @freezed class ProductModel with _$ProductModel { ... factory ProductModel.fromFirestore(...) ... Map<String, dynamic> toFirestore() ... }
// (當前使用 ProductDto 內嵌在 ProductEntity 或獨立的 Dto 文件)

// features/product_mgmt/data/datasources/product_remote_ds.dart
// abstract class ProductRemoteDataSource { ... }
// class ProductRemoteDataSourceImpl implements ProductRemoteDataSource {
//   final FirebaseFirestore _firestore;
//   ProductRemoteDataSourceImpl(this._firestore);
//   // ... implementation using ProductDto/Model for Firestore operations ...
// }

// features/product_mgmt/data/repositories/product_repo_impl.dart
class ProductRepoImpl implements ProductRepository {
  final ProductRemoteDataSource _remoteDataSource;
  ProductRepoImpl(this._remoteDataSource);
  // ... implementation ...
  // Methods will convert ProductModel/Dto from datasource to Product entity for domain
}

// features/product_mgmt/presentation/providers/product_providers.dart
final productRemoteDataSourceProvider = Provider<ProductRemoteDataSource>((ref) {
  return ProductRemoteDataSourceImpl(ref.read(firebaseFirestoreProvider));
});

final productRepositoryProvider = Provider<ProductRepository>((ref) {
  return ProductRepoImpl(ref.read(productRemoteDataSourceProvider));
});

final productListProvider =
    StreamProvider.autoDispose.family<List<Product>, ({String storeId, String tenantId})>((ref, ids) {
  return ref.watch(productRepositoryProvider).watchAllProducts(ids.storeId, ids.tenantId);
});
```

> 測試時，可 `overrideWithValue(MockProductRepository())`，UI 無需修改。

---

### 4.6 router/ (導航)

-   **GoRouter (15.1.2 或更高最新穩定版)**
-   每個功能模塊暴露其 `GoRoute` 列表 → 在 `router/app_router.dart` 中聚合。
-   深層鏈接 (例如 `/store/:id/product/:productId/edit`) 支持**嵌套導航**，方便平板雙窗格佈局。
-   路由守衛 (Route Guards) 可用於檢查認證狀態和網絡連接狀態。

_備註：請始終參考官方發布的最新穩定版本，並根據項目實際情況進行調整。_

---

### 4.7 l10n/ (國際化)

-   `app_en.arb`, `app_zh_Hant.arb`
-   字符串鍵使用 IntelliJ Flutter Intl 插件自動完成 (確保插件兼容最新 IDE 版本)。

---

### 4.8 推薦測試目錄結構

```
test/
 ├─ core/
 │   ├─ utils/
 │   └─ network/
 ├─ shared/
 │   └─ widgets/
 └─ features/
     └─ product_mgmt/ // (示例，其他功能模塊類似)
         ├─ data/
         │   ├─ models/ (或 dtos/)
         │   └─ repositories/
         ├─ domain/
         │   └─ usecases/
         └─ presentation/
             ├─ providers/
             └─ widgets/
```

-   **data layer**: 對於 Firestore 交互，可使用 `fake_cloud_firestore`。
-   **domain layer**: 純 Dart → 最早運行，最快。
-   **presentation layer**: `flutter_test` + `golden_toolkit` (可選) 用於截圖比較。

---

### 4.9 依賴管理提示

-   **僅在 `pubspec.yaml` 中引用上一層**: `presentation` 不能引用 `datasources`，以避免層次倒置。
-   保持 `pubspec.yaml` 簡潔，僅包含必要依賴。

---

### 4.10 文件名與命名約定

| 類型            | 示例                          | 規則                                                   |
| --------------- | ----------------------------- | ------------------------------------------------------ |
| Provider        | `product_list_provider.dart`  | `{subject}_{type}_provider.dart`                       |
| State / Entity  | `product.dart`, `tenant.dart` | 單數命名 (Domain Entities)                             |
| Data Model(DTO) | `product_model.dart` or `product_dto.dart` | Entity 名稱 + `_model` 或 `_dto` (Data Layer)                    |
| UseCase         | `get_products_usecase.dart`   | 動詞優先命名，以 `_usecase` 結尾                       |
| Repository Impl | `product_repo_impl.dart`      | Interface 名稱 + `_impl`                               |
| Datasource Impl | `product_remote_ds_impl.dart` | Interface 名稱 + `_impl` (如果抽象)                    |
| Widget          | `product_card.dart`           | 視覺組件 `_card`, `_tile`, `_form`, `_page`, `_dialog` |

---

> 通過將功能劃分為「功能包 + 三層結構 + Riverpod Provider」，並與 **core / shared** 協調以提供橫向支持，整個代碼庫可以同時滿足「大型團隊協作」和「個人快速迭代」的需求。

---

## 5 — 開發階段規劃 (路線圖式衝刺) - 已調整

| 衝刺                   | 里程碑 (可交付成果)                                           | 主要工作包 (已調整)                                                                                                                                                                                                                                                                 | 驗收標準                                                                                                                                            |
| ---------------------- | ------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------- |
| **0 — 啟動**           | 項目骨架 + **連接 Firebase 生產專案 grid-pos** + **網絡檢測** | Flutter 項目 (`grid-pos`) 已創建 (使用 Flutter 3.29.3)。<br/>Riverpod + Freezed 腳手架。<br/>Firebase CLI 初始化並連接到 **Firebase 專案 grid-pos** (Email Auth 和 Firestore 已啟用)。<br/>**手動部署**初始安全規則到生產專案 **grid-pos**。<br/>**實現啟動時網絡檢測與錯誤提示。** | `flutter analyze` & `flutter test` 通過。<br/> Android / iOS Debug 版本能連接 Firebase 專案 **grid-pos**。<br/>**無網絡時啟動應用會顯示錯誤提示。** |
| **1 — 認證與角色**     | 基本登錄流程 + Firestore 角色管理                             | Email/Password UI, `users` collection 存儲角色, 管理員 UI 設置角色, AuthProvider, **手動更新並部署**針對 `users` collection 的安全規則到生產專案 **grid-pos**。                                                                                                                     | 憑證錯誤顯示 snackbar。<br/> 管理員可在 Firestore 中設置租戶/收銀員角色, 安全規則使用此角色 (在生產環境驗證)。                                      |
| **2 — 租戶管理**       | 租戶 CRUD + 多格位分配 (管理員)                               | TenantForm + GridSelector (管理員 UI), 客戶端 Firestore 事務進行格位分配。安全規則已**手動部署到生產專案 grid-pos**。                                                                                                                                                               | 管理員創建/編輯/刪除/搜索租戶。<br/> 通過管理員應用事務同步 `grids.tenantId` & `tenants.grids`。                                                    |
| **2.5 — 格位管理**     | 管理員格位 CRUD                                               | Grid Entity/DTO (已部分完成), Grid CRUD UI (列表、表單), Grid Repository, Grid Notifier, 安全規則更新與部署。                                                                                                                                                              | 管理員可創建、查看、編輯 (編號、尺寸)、刪除 (僅未分配的) 格位。格位編號唯一性校驗。                                                                               |
| **3 — 商品管理**       | 商品 CRUD + 條碼 (租戶/管理員)                                | ProductForm, 條碼生成/掃描存根。安全規則允許租戶管理自己的商品，並已**手動部署到生產專案 grid-pos**。商品包含`active`, `storeId`, `tenantId`字段。                                                                                                                                  | 條碼唯一性驗證 (客戶端寫入前檢查)。<br/> 低庫存指示器 (UI)。                                                                                        |
| **4 — POS MVP**        | 掃描 → 購物車 → 結帳 (客戶端事務)                             | 客戶端 `runTransaction` 處理銷售+庫存, 悲觀鎖 (客戶端創建/刪除), 藍牙打印存根。`daily_summaries` 更新延遲/手動。相關安全規則已**手動部署到生產專案 grid-pos**。                                                                                                                     | 結帳事務後庫存自動扣減。<br/> **應用內生成 PDF 收據預覽。**<br/>銷售記錄已創建。                                                                    |
| **5 — 報告與儀錶板**   | `fl_chart` 卡片 + 英文 PDF (應用內生成)                       | 客戶端查詢生成每日銷售額, 庫存餅圖。`printing` 包。`daily_summaries` 可能由應用內管理員工具功能填充。                                                                                                                                                                               | 報告的日期範圍過濾器 (直接查詢銷售數據)。<br/> PDF 風格一致。管理員可觸發摘要計算。                                                                 |
| **6 — 階段一強化**     | 單元/Widget/E2E 測試 + QA                                     | **針對真實 Firebase 生產專案 grid-pos 進行 E2E 測試**，客戶端事務的性能壓力測試。**安全規則在生產環境手動驗證。**                                                                                                                                                                   | 測試覆蓋率 ≥ 70% (目標可略微調整)。<br/> 客戶端銷售事務 (例如 5 個商品) 可靠完成。                                                                  |

> ❇️ **節奏**: 每個衝刺約 1-2 週；結束時演示 + 回顧 → 下週完善積壓工作。

---

## 6 — 功能規格 (階段一完整列表) - 已調整

### 6.1 認證

-   Email/Password, 忘記密碼。
-   Riverpod `AuthNotifier` ⟶ 暴露 `currentUser`, 監聽 `users/{userId}` 以獲取 `role`。
-   登出時清除本地 `shared_preferences` 中的用戶特定數據。
-   **角色管理:** 管理員使用應用程序的一部分為用戶分配角色 (`admin`, `tenant`, `cashier`)，寫入 `users` collection。
-   **網絡檢測:** 應用啟動時，若無網絡連接，則顯示全屏錯誤提示，阻止用戶進一步操作，直到網絡恢復。操作過程中如果斷網，也應有適當提示並阻止敏感操作。

### 6.2 租戶管理

-   搜索、排序 (按名稱 / 合同到期日)。
-   多格位 Chip 顯示 (可點擊查看格位詳情)。
-   租戶活動狀態 (激活 / 暫停) 切換。
-   **格位分配**: 管理員可將已存在的格位分配給租戶，或取消分配。

### 6.3 商品管理

-   SKU 自動增量生成 (客戶端使用 `global_counters` 或基於時間戳)。
-   手動 / 掃描儀條碼輸入；**客戶端在提交前檢查條碼在店鋪內的唯一性，並給予用戶警告。注意：此客戶端檢查在極高並發下可能存在微小衝突概率，服務端唯一性強制為未來階段考慮。**
-   價格不得 < 0；庫存不得 < 0 (表單驗證 + 安全規則)。
-   **低庫存提示:** UI 指示器或管理員生成報告中的項目。
-   商品需關聯到一個已存在的格位。
-   商品包含 `active` 狀態，用於控制是否上架/可售。
-   商品文檔包含冗餘的 `storeId` 和 `tenantId` 字段。

### 6.4 POS

| 子流程         | 細節                                                                                                                                                                                        |
| -------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **掃描**       | 自動對焦掃描儀；如果重複掃描 → 增加數量 (使用如 `mobile_scanner` 的包)。                                                                                                                    |
| **購物車**     | Riverpod `StateNotifier`, `items[{productId,qty,price}]`。                                                                                                                                  |
| **結帳**       | **客戶端 `runTransaction` (強制在線)**: <br/>1. 讀取購物車商品的當前庫存。商品必須為 `active` 狀態。<br/>2. 如果庫存充足：創建 `sales` 文檔並減少每個商品的 `products.stock`。<br/>3. (可選) 更新 `daily_summaries`。 |
| **打印**       | `blue_thermal_printer` (或選定包) 存根；**由於強制在線，無需離線隊列。** 打印操作直接執行，失敗則提示用戶。                                                                                 |
| **取消與退貨** | 狀態從 `completed` 變為 `cancelled`。**由管理員/授權收銀員執行客戶端 `runTransaction`** 以還原庫存並更新銷售狀態。                                                                          |

### 6.5 儀錶板與報告

-   今日銷售額，昨日比較：客戶端通過查詢 `sales` collection 計算。
-   低庫存商品列表 (≤ `lowStockLevel`, 且 `active == true`)。
-   **應用內生成**英文 PDF：每日銷售報告 / 庫存報告 (使用如 `pdf` 和 `printing` 的包)。
-   `daily_summaries` collection：由應用內管理員觸發的流程更新，或報告直接查詢原始數據。實時性較低。

### 6.6 格位管理 (Grid Management) - 管理員操作

*   **創建格位 (Create Grid)**:
    *   管理員能夠爲指定店鋪創建新的格位。
    *   輸入字段：格位編號 (`code` - 店鋪內應唯一)、尺寸 (`size` - 如 S, M, L)。
    *   新創建的格位 `tenantId` 默認爲 `null` (未分配)。
*   **查看格位列表 (Read Grids)**:
    *   管理員能夠查看指定店鋪的所有格位列表。
    *   列表信息包含：格位編號、尺寸、當前分配的租戶 (如有)、創建/更新時間。
    *   支持按格位編號、尺寸、分配狀態進行篩選和排序。
*   **更新格位信息 (Update Grid)**:
    *   管理員能夠修改已存在格位的屬性。
    *   可修改字段：格位編號 (`code`)、尺寸 (`size`)。
    *   **注意**: 修改已分配格位的編號或尺寸時，應有明確提示，避免影響租戶。不直接在此處修改 `tenantId`，格位分配由租戶管理模塊的“格位分配”功能負責。
*   **刪除格位 (Delete Grid)**:
    *   管理員能夠刪除格位。
    *   **重要**: 僅允許刪除當前未分配給任何租戶 (`tenantId` 爲 `null`) 的格位。如果格位已被分配，需先在租戶管理中取消分配，才能刪除。刪除前應有確認提示。
*   **UI/UX**:
    *   提供清晰的格位列表展示界面和易用的格位創建/編輯表單。
    *   格位編號在店鋪內應進行唯一性校驗（客戶端創建/編輯時檢查）。

---

## 7 — 性能與並發策略 (階段一必做) - 已調整

1.  **客戶端事務**: 對於 POS 結帳，確保事務儘可能小。如果 `daily_summaries` 更新包含在內，會增加事務大小和失敗風險。
2.  **悲觀鎖**: 結帳時在 `products/{...}/locks/{deviceId}` 寫入鎖，包含 `expire` 時間戳 (例如 2 分鐘後)。Firestore TTL 策略自動清理過期鎖。
3.  **分片計數器**: 對於高頻更新的計數器 (例如 SKU 生成器、總銷售額)，如 `sales_total_{storeId}`，可考慮每店鋪設置約 20 個分片 (`global_counters/.../shards/{shardId}`)。客戶端隨機選擇一個分片進行遞增。
4.  **索引**: 為常用查詢添加複合索引，例如 `products` 的 `updatedAt DESC` 用於無限滾動分頁。確保索引支持包含 `active` 狀態的查詢。
5.  **安全規則中的 `get()`**: 安全規則中用於角色查找的 `get()` 調用會為每個受保護的讀/寫操作增加一次額外的文檔讀取。監控其對性能的影響，確保 `users` 文檔盡可能小。

---

## 8 — UX / UI 指南 (關鍵交互)

| 場景           | UX 要點                                                                                                          | 組件 / 包                            |
| -------------- | ---------------------------------------------------------------------------------------------------------------- | ------------------------------------ |
| **無網絡連接** | 應用啟動時，若無網絡，顯示全屏覆蓋的錯誤提示 `NoNetworkWidget`，包含「重試」按鈕。操作過程中斷網也應有適當提示。 | `Dialog`, `connectivity_plus`        |
| **掃描與結帳** | 單手操作：FAB 掃描 + 自動對焦；掃描成功後有觸感反饋。                                                            | `mobile_scanner`, `HapticFeedback`   |
| **低庫存警報** | ListTile 右側紅點 + Snackbar (客戶端檢查)。管理員應用有專門的「低庫存報告」視圖。                                | `Badge` (Material 3)                 |
| **表單驗證**   | 實時 ✕ / ✔️ 圖標；底部錯誤信息。                                                                                 | `TextFormField.validator` + Riverpod |
| **空狀態**     | 插圖 + 行動呼籲；例如，「暫無商品，立即添加一個吧」 (`EmptyStateWidget`)                                         | `Rive` / `Lottie` (可選)             |
| **平板雙窗格** | 左側 `NavigationRail`；右側內容。                                                                                | `LayoutBuilder` Breakpoint 840 dp    |

---

## 9 — DevOps 與測試矩陣 (簡化版 - 手動流程)

### 9.1 CI 工作流 (移除 - 採用手動部署)

-   項目將不採用自動化的 CI/CD 工具。
-   所有構建、測試和部署流程將手動執行。

### 9.2 測試級別

| 級別                    | 工具                                              | 目標覆蓋範圍 (已調整)                                                                                                            |
| ----------------------- | ------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------- |
| 單元                    | `flutter test`                                    | DataValidator, DateTimeUtils, Riverpod Notifiers (業務邏輯), `NetworkInfo`。                                                     |
| Widget                  | `flutter_test`                                    | POS 購物車, 租戶格位卡片, 表單等關鍵 UI 組件交互, `NoNetworkWidget` 顯示。                                                       |
| E2E                     | `integration_test`                                | **針對真實的 Firebase 生產專案 grid-pos 運行**。關鍵在線流程。測試網絡斷開和重連的場景。                                         |
| **規則 (生產環境測試)** | **Firebase 控制檯 / 手動應用測試**                | **在安全規則直接部署到生產專案 grid-pos 後，通過實際操作應用程序或在 Firebase 控制檯的規則演練場 (Rules Playground) 進行驗證。** |
| 性能                    | Flutter DevTools, Firebase Performance Monitoring | Firestore 讀取 < 200ms (監控規則中 `get()` 的影響)。客戶端事務成功率和耗時。                                                     |

**重要提示：** 在生產環境中直接測試安全規則具有高風險。任何錯誤都可能導致數據洩露、數據損壞或服務中斷。建議在部署前進行多次人工審查，並且在部署後立即進行小範圍、有針對性的功能驗證。如果可能，設立一個與生產環境配置相同的預備環境 (Staging Environment) 進行部署前的最終驗證，會是一個更安全的折衷方案。

---

## 10 — 路線圖 (階段二 / 三)

| 時間線      | 里程碑        | 關鍵可交付成果 (已調整)                                                                         |
| ----------- | ------------- | ----------------------------------------------------------------------------------------------- |
| **2025 Q2** | **階段一 GA** | 應用內部測試，功能完整 (基於在線)，測試覆蓋率 ≥ 70%。                                           |
| **2025 Q3** | **階段二**    | 條碼掃描器在真實設備上優化，正式藍牙打印功能，自動化月度報告 (可能由管理員應用觸發生成)。       |
| **2025 Q4** | **階段三**    | **用於 BigQuery 導出的外部腳本。** 漸進式 Web 應用 (PWA) 探索。應用商店 / Play 商店提交。       |
| **2026 Q1** | **增長階段**  | 智能補貨 (自動採購訂單 - 可能需要後端)，NFC 支付集成 (客戶端 SDK)，AI 銷售預測 (可能需要後端)。 |

> **里程碑檢查點** > _季度性_: 性能壓力測試 (例如 1k TPS 模擬), 安全規則審計 (人工審查並在生產驗證), 依賴包升級 (`flutter pub outdated --mode=null-safety`)

---

### ⚡ 鼓舞人心的話 / 提示 - 已調整

-   **衝刺回顧**仍然重要，以解決開發中的痛點。
-   **謹慎管理 Firebase 生產環境數據**。考慮定期備份或制定清晰的數據清理策略。
-   **安全規則是你的新後端**：它們需要極其詳盡。**由於直接部署到生產環境，規則的正確性至關重要，部署前務必多人審查。**
-   **強制在線簡化了某些狀態管理**，但需確保網絡錯誤處理友好且覆蓋所有關鍵操作。
-   **Firebase 成本**：直接使用生產環境意味著從一開始就會產生讀寫費用。密切關注用量並優化查詢 (Auth, Firestore)。
-   **網絡檢測的用戶體驗**：確保「重試」機制有效，並且在操作過程中斷網時，不會導致數據不一致或應用崩潰。
-   **手動部署的責任**：由於缺乏自動化流程，團隊需要建立嚴格的手動部署規程和檢查清單，以減少人為錯誤。

---
