// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBsN2PedJ9YISbDXLzxhvNniBt7mbArQuc',
    appId: '1:517919260870:web:615c3cac3a85e0512cd7c7',
    messagingSenderId: '517919260870',
    projectId: 'grid-pos',
    authDomain: 'grid-pos.firebaseapp.com',
    storageBucket: 'grid-pos.firebasestorage.app',
    measurementId: 'G-NNYK1C6YND',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyClPv0T7ooEl-3Qa591KmIWrCMZ_YMVxJM',
    appId: '1:517919260870:android:ba277fd092fbfb5f2cd7c7',
    messagingSenderId: '517919260870',
    projectId: 'grid-pos',
    storageBucket: 'grid-pos.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDmCqrxMpukRH5i_pOhcUOYE1W7_ba9bdM',
    appId: '1:517919260870:ios:613668485818d4a82cd7c7',
    messagingSenderId: '517919260870',
    projectId: 'grid-pos',
    storageBucket: 'grid-pos.firebasestorage.app',
    iosBundleId: 'com.example.gridPos',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDmCqrxMpukRH5i_pOhcUOYE1W7_ba9bdM',
    appId: '1:517919260870:ios:613668485818d4a82cd7c7',
    messagingSenderId: '517919260870',
    projectId: 'grid-pos',
    storageBucket: 'grid-pos.firebasestorage.app',
    iosBundleId: 'com.example.gridPos',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBsN2PedJ9YISbDXLzxhvNniBt7mbArQuc',
    appId: '1:517919260870:web:fcc38f3cf267bab82cd7c7',
    messagingSenderId: '517919260870',
    projectId: 'grid-pos',
    authDomain: 'grid-pos.firebaseapp.com',
    storageBucket: 'grid-pos.firebasestorage.app',
    measurementId: 'G-GNT04GX30X',
  );

}