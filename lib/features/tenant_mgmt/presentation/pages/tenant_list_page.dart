import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/tenant_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/tenant_providers.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/tenant_crud_provider.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/widgets/store_selector.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/widgets/tenant_card.dart';
import 'package:grid_pos/shared/widgets/custom_snackbar.dart';

/// Enum for tenant sorting options
enum TenantSortOption {
  /// Sort by name
  name('名稱', Icons.sort_by_alpha),

  /// Sort by contract end date
  contractEndDate('合約結束日', Icons.calendar_today),

  /// Sort by status (active first)
  status('狀態', Icons.toggle_on);

  /// Create a sort option
  const TenantSortOption(this.label, this.icon);

  /// The display label
  final String label;

  /// The icon to display
  final IconData icon;
}

/// A page that displays a list of tenants
class TenantListPage extends ConsumerStatefulWidget {
  /// Creates a tenant list page
  const TenantListPage({super.key});

  @override
  ConsumerState<TenantListPage> createState() => _TenantListPageState();
}

class _TenantListPageState extends ConsumerState<TenantListPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  TenantSortOption _sortOption = TenantSortOption.name;
  bool _isSearching = false;
  String? _previousStoreId;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final currentStoreId = ref.read(selectedStoreIdProvider);
    if (_previousStoreId != currentStoreId) {
      _previousStoreId = currentStoreId;
      // Force refresh providers when store changes
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.invalidate(tenantsProvider(currentStoreId));
        ref.invalidate(currentStoreTenantsProvider);
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final selectedStoreId = ref.watch(selectedStoreIdProvider);
    final tenantsAsync = ref.watch(currentStoreTenantsProvider);
    final tenantCrudState = ref.watch(tenantCrudNotifierProvider);

    // Listen for tenant CRUD state changes
    ref.listen<TenantCrudState>(tenantCrudNotifierProvider, (previous, current) {
      if (current.successMessage != null && previous?.successMessage != current.successMessage) {
        if (context.mounted) {
          showSuccessSnackBar(context, current.successMessage!);
        }
      }

      if (current.errorMessage != null && previous?.errorMessage != current.errorMessage) {
        if (context.mounted) {
          showErrorSnackBar(context, current.errorMessage!);
        }
      }
    });

    return Scaffold(
      key: ValueKey('tenant-list-$selectedStoreId'),
      backgroundColor: theme.colorScheme.surface,
      appBar: _buildAppBar(theme),
      body: tenantsAsync.when(
        data: (tenants) => _buildScrollableContent(tenants, theme),
        loading:
            () => const Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [CircularProgressIndicator(), SizedBox(height: 16), Text('載入租戶資料中...')],
              ),
            ),
        error: (error, stackTrace) => _buildErrorState(error, theme),
      ),
      floatingActionButton: tenantsAsync.maybeWhen(
        error:
            (error, _) =>
                error.toString().contains('No valid store selected')
                    ? null
                    : _buildAddTenantFab(context),
        orElse: () => _buildAddTenantFab(context),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    if (_isSearching) {
      return AppBar(
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        automaticallyImplyLeading: false,
        actions: [IconButton(icon: const Icon(Icons.close), onPressed: _exitSearchMode)],
        title: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
          ),
          child: TextField(
            controller: _searchController,
            autofocus: true,
            decoration: InputDecoration(
              hintText: '搜尋租戶名稱、電話或電子郵件...',
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(vertical: 12),
              hintStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant, fontSize: 16),
              prefixIcon: Icon(Icons.search, color: theme.colorScheme.onSurfaceVariant, size: 20),
              suffixIcon:
                  _searchQuery.isNotEmpty
                      ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: IconButton(
                          icon: Icon(Icons.clear, color: theme.colorScheme.onSurfaceVariant),
                          onPressed: () {
                            _searchController.clear();
                            setState(() => _searchQuery = '');
                          },
                        ),
                      )
                      : null,
            ),
            style: TextStyle(color: theme.colorScheme.onSurfaceVariant, fontSize: 16),
            onChanged: (value) => setState(() => _searchQuery = value.toLowerCase()),
          ),
        ),
      );
    }

    return AppBar(
      elevation: 0,
      backgroundColor: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      title: Text(
        '租戶管理',
        style: theme.textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.onSurface,
        ),
      ),
      actions: [
        // StoreSelector(),
        IconButton(
          icon: Stack(
            children: [
              const Icon(Icons.search),
              if (_hasActiveFilters())
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                  ),
                ),
            ],
          ),
          onPressed: _enterSearchMode,
        ),
      ],
    );
  }

  void _enterSearchMode() {
    setState(() => _isSearching = true);
  }

  void _exitSearchMode() {
    setState(() {
      _isSearching = false;
      _searchController.clear();
      _searchQuery = '';
    });
  }

  Widget _buildScrollableContent(List<TenantEntity> tenants, ThemeData theme) {
    final filteredTenants = _filterAndSortTenants(tenants);

    // 如果沒有租戶，顯示空狀態
    if (filteredTenants.isEmpty) {
      return RefreshIndicator(
        onRefresh: () async {
          final storeId = ref.read(selectedStoreIdProvider);
          ref.invalidate(tenantsProvider(storeId));
          ref.invalidate(currentStoreTenantsProvider);
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: _buildSearchAndStats(tenants, theme),
              ),
              SizedBox(height: MediaQuery.of(context).size.height * 0.2),
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _hasActiveFilters() ? Icons.search_off : Icons.person_search,
                      size: 64,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _hasActiveFilters() ? '沒有找到符合條件的租戶' : '沒有找到租戶',
                      style: theme.textTheme.titleMedium,
                    ),
                    if (_hasActiveFilters()) ...[
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _sortOption = TenantSortOption.name;
                          });
                          if (_isSearching) {
                            _exitSearchMode();
                          }
                        },
                        child: const Text('清除篩選'),
                      ),
                    ] else ...[
                      const SizedBox(height: 8),
                      Text(
                        '嘗試調整搜索或添加新租戶',
                        textAlign: TextAlign.center,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          height: 1.5,
                        ),
                      ),
                      const SizedBox(height: 32),
                      FilledButton.icon(
                        onPressed: () => _navigateToCreateTenant(),
                        icon: const Icon(Icons.add),
                        label: const Text('添加第一個租戶'),
                        style: FilledButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    // 有租戶時，使用統一的 ListView
    return RefreshIndicator(
      onRefresh: () async {
        final storeId = ref.read(selectedStoreIdProvider);
        ref.invalidate(tenantsProvider(storeId));
        ref.invalidate(currentStoreTenantsProvider);
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredTenants.length + 1, // +1 for the header
        itemBuilder: (context, index) {
          // 第一個項目是搜索和統計區域
          if (index == 0) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: _buildSearchAndStats(tenants, theme),
            );
          }

          // 其餘項目是租戶列表
          final tenantIndex = index - 1;
          final tenant = filteredTenants[tenantIndex];
          final tenantCrudState = ref.watch(tenantCrudNotifierProvider);
          final isProcessing =
              tenantCrudState.processingTenantId == tenant.id && tenantCrudState.isLoading;

          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: TenantCard(
              key: Key(tenant.id),
              tenant: tenant,
              onTap: () async {
                final result = await context.push<bool>(
                  '/tenants/${tenant.id}/edit',
                  extra: tenant,
                );
                if (context.mounted) {
                  final storeId = ref.read(selectedStoreIdProvider);
                  ref.invalidate(tenantsProvider(storeId));
                  ref.invalidate(currentStoreTenantsProvider);
                }
              },
              onEdit: () async {
                final result = await context.push<bool>(
                  '/tenants/${tenant.id}/edit',
                  extra: tenant,
                );
                if (context.mounted) {
                  final storeId = ref.read(selectedStoreIdProvider);
                  ref.invalidate(tenantsProvider(storeId));
                  ref.invalidate(currentStoreTenantsProvider);
                }
              },
              onDelete: isProcessing ? null : () => _showDeleteDialog(tenant),
              isProcessing: isProcessing,
            ),
          );
        },
      ),
    );
  }

  Widget _buildSearchAndStats(List<TenantEntity> tenants, ThemeData theme) {
    final filteredTenants = _filterAndSortTenants(tenants);
    final stats = _calculateTenantStats(tenants);

    return Column(
      children: [
        // Search result indicator when searching
        if (_isSearching && _searchQuery.isNotEmpty) ...[
          Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color:
                  filteredTenants.isEmpty
                      ? Colors.orange.withOpacity(0.1)
                      : Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color:
                    filteredTenants.isEmpty
                        ? Colors.orange.withOpacity(0.3)
                        : Colors.green.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  filteredTenants.isEmpty ? Icons.search_off : Icons.check_circle_outline,
                  color: filteredTenants.isEmpty ? Colors.orange : Colors.green,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    filteredTenants.isEmpty
                        ? '沒有找到符合 "$_searchQuery" 的租戶'
                        : '找到 ${filteredTenants.length} 個符合 "$_searchQuery" 的租戶',
                    style: TextStyle(
                      color:
                          filteredTenants.isEmpty ? Colors.orange.shade700 : Colors.green.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (filteredTenants.isNotEmpty) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${filteredTenants.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],

        // Stats Card with integrated sorting options
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    const Icon(Icons.people, size: 24),
                    const SizedBox(width: 8),
                    Text('租戶總數: ${tenants.length}', style: theme.textTheme.titleMedium),
                    if (filteredTenants.length != tenants.length) ...[
                      const Spacer(),
                      Text('顯示: ${filteredTenants.length}'),
                    ],
                  ],
                ),
                if (tenants.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  // Status stats row
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatItem('活躍租戶', stats['active'] ?? 0, Colors.green, theme),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatItem('非活躍', stats['inactive'] ?? 0, Colors.orange, theme),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatItem('即將到期', stats['expiring'] ?? 0, Colors.red, theme),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Sorting options
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.sort, size: 16, color: theme.colorScheme.onSurfaceVariant),
                            const SizedBox(width: 8),
                            Text(
                              '排序方式',
                              style: theme.textTheme.labelMedium?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children:
                              TenantSortOption.values.map((option) {
                                final isSelected = _sortOption == option;
                                return InkWell(
                                  borderRadius: BorderRadius.circular(8),
                                  onTap: () => setState(() => _sortOption = option),
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          isSelected
                                              ? theme.colorScheme.primary.withOpacity(0.1)
                                              : theme.colorScheme.surfaceContainerHighest,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color:
                                            isSelected
                                                ? theme.colorScheme.primary
                                                : theme.colorScheme.outline.withOpacity(0.3),
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          option.icon,
                                          size: 16,
                                          color:
                                              isSelected
                                                  ? theme.colorScheme.primary
                                                  : theme.colorScheme.onSurfaceVariant,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          option.label,
                                          style: theme.textTheme.bodySmall?.copyWith(
                                            color:
                                                isSelected
                                                    ? theme.colorScheme.primary
                                                    : theme.colorScheme.onSurfaceVariant,
                                            fontWeight: isSelected ? FontWeight.w600 : null,
                                          ),
                                        ),
                                        if (isSelected) ...[
                                          const SizedBox(width: 4),
                                          Icon(
                                            Icons.check,
                                            size: 14,
                                            color: theme.colorScheme.primary,
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                );
                              }).toList(),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, int count, Color color, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text('$count', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: color)),
          const SizedBox(height: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(color: color, fontWeight: FontWeight.w600),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object? error, ThemeData theme) {
    if (error.toString().contains('No valid store selected')) {
      return Center(
        child: Container(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: theme.colorScheme.errorContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(32),
                ),
                child: Icon(Icons.store_outlined, size: 64, color: theme.colorScheme.error),
              ),
              const SizedBox(height: 24),
              Text(
                '沒有可用的商店',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '請先創建商店再添加租戶',
                textAlign: TextAlign.center,
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 32),
              FilledButton.icon(
                onPressed: () => context.push('/admin/stores/new'),
                icon: const Icon(Icons.add_business),
                label: const Text('創建新商店'),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            '載入租戶時出錯: ${error.toString()}',
            style: const TextStyle(color: Colors.red),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              final selectedStoreId = ref.read(selectedStoreIdProvider);
              ref.invalidate(tenantsProvider(selectedStoreId));
              ref.invalidate(currentStoreTenantsProvider);
            },
            child: const Text('重試'),
          ),
        ],
      ),
    );
  }

  Widget _buildAddTenantFab(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: FloatingActionButton.extended(
        onPressed: () => _navigateToCreateTenant(),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 0,
        icon: const Icon(Icons.add),
        label: const Text('新增租戶'),
      ),
    );
  }

  void _navigateToCreateTenant() async {
    final result = await context.push<bool>('/tenants/new');
    if (context.mounted) {
      if (result == true) {
        debugPrint('[TenantListPage] Returning from successful tenant creation');
      }
      final storeId = ref.read(selectedStoreIdProvider);
      ref.invalidate(tenantsProvider(storeId));
      ref.invalidate(currentStoreTenantsProvider);
    }
  }

  void _showDeleteDialog(TenantEntity tenant) {
    final theme = Theme.of(context);
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            title: Row(
              children: [
                Icon(Icons.delete_forever, color: theme.colorScheme.error),
                const SizedBox(width: 12),
                const Text('刪除租戶'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('確定要刪除租戶 "${tenant.name}" 嗎？', style: theme.textTheme.bodyLarge),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.errorContainer.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.warning, size: 20, color: theme.colorScheme.error),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Text(
                          '這也將刪除任何關聯的用戶帳號',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('取消')),
              FilledButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  try {
                    final storeId = ref.read(selectedStoreIdProvider);
                    await ref
                        .read(tenantCrudNotifierProvider.notifier)
                        .deleteTenant(storeId, tenant.id);
                  } catch (e) {
                    // Error is handled in the notifier and listener
                  }
                },
                style: FilledButton.styleFrom(
                  backgroundColor: theme.colorScheme.error,
                  foregroundColor: theme.colorScheme.onError,
                ),
                child: const Text('刪除'),
              ),
            ],
          ),
    );
  }

  List<TenantEntity> _filterAndSortTenants(List<TenantEntity> tenants) {
    List<TenantEntity> filteredTenants = tenants;

    // Filter by search query if provided
    if (_searchQuery.isNotEmpty) {
      filteredTenants =
          tenants.where((tenant) {
            return tenant.name.toLowerCase().contains(_searchQuery) ||
                tenant.contact.phone.contains(_searchQuery) ||
                tenant.contact.email.toLowerCase().contains(_searchQuery);
          }).toList();
    }

    // Sort by selected option
    switch (_sortOption) {
      case TenantSortOption.name:
        filteredTenants.sort((a, b) => a.name.compareTo(b.name));
      case TenantSortOption.contractEndDate:
        filteredTenants.sort((a, b) => a.contract.end.compareTo(b.contract.end));
      case TenantSortOption.status:
        filteredTenants.sort((a, b) {
          if (a.active == b.active) {
            return a.name.compareTo(b.name);
          }
          return a.active ? -1 : 1; // Active tenants first
        });
    }

    return filteredTenants;
  }

  Map<String, int> _calculateTenantStats(List<TenantEntity> tenants) {
    final stats = <String, int>{};

    int active = 0;
    int inactive = 0;
    int expiring = 0;

    final now = DateTime.now();
    final thirtyDaysFromNow = now.add(const Duration(days: 30));

    for (final tenant in tenants) {
      if (tenant.active) {
        active++;
        // Check if contract is expiring within 30 days
        if (tenant.contract.end.isBefore(thirtyDaysFromNow)) {
          expiring++;
        }
      } else {
        inactive++;
      }
    }

    stats['active'] = active;
    stats['inactive'] = inactive;
    stats['expiring'] = expiring;

    return stats;
  }

  bool _hasActiveFilters() {
    return _searchQuery.isNotEmpty || _sortOption != TenantSortOption.name;
  }
}
