import "package:go_router/go_router.dart";
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/tenant_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/tenant_crud_provider.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/widgets/grid_picker.dart';
import 'package:grid_pos/shared/widgets/custom_snackbar.dart';
import 'package:intl/intl.dart';
import 'package:grid_pos/core/utils/logger.dart';

/// Form state providers
final tenantNameProvider = StateProvider<String>((ref) => '');
final tenantPhoneProvider = StateProvider<String>((ref) => '');
final tenantEmailProvider = StateProvider<String>((ref) => '');
final tenantPasswordProvider = StateProvider<String>((ref) => '');
final tenantConfirmPasswordProvider = StateProvider<String>((ref) => '');
final createUserAccountProvider = StateProvider<bool>((ref) => true);
final tenantActiveProvider = StateProvider<bool>((ref) => true);
final tenantContractStartProvider = StateProvider<DateTime>((ref) => DateTime.now());
final tenantContractEndProvider = StateProvider<DateTime>(
  (ref) => DateTime.now().add(const Duration(days: 365)),
);
final tenantRentProvider = StateProvider<double>((ref) => 0.0);
final tenantGridsProvider = StateProvider<List<String>>((ref) => []);

/// A page for creating or editing a tenant with improved UI/UX and search functionality
class TenantFormPage extends ConsumerStatefulWidget {
  /// The tenant to edit (null for creating a new tenant)
  final TenantEntity? tenant;

  /// Creates a tenant form page
  const TenantFormPage({this.tenant, super.key});

  @override
  ConsumerState<TenantFormPage> createState() => _TenantFormPageState();
}

class _TenantFormPageState extends ConsumerState<TenantFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _rentController = TextEditingController();
  bool _isEditing = false;
  bool _isNavigatingAway = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  final _dateFormat = DateFormat('yyyy年MM月dd日');

  // Search states
  String _gridSearchQuery = '';
  final _gridSearchController = TextEditingController();

  // Safe navigation method
  void _safeNavigateBack({bool success = false}) {
    if (!_isNavigatingAway && mounted && context.mounted) {
      Logger.debug('Navigating back from TenantFormPage with success: $success');
      setState(() {
        _isNavigatingAway = true;
      });
      if (context.canPop()) {
        context.pop(success);
      } else {
        context.go('/tenants');
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _isEditing = widget.tenant != null;

    if (_isEditing) {
      // Initialize controllers with existing tenant data
      _nameController.text = widget.tenant!.name;
      _phoneController.text = widget.tenant!.contact.phone;
      _emailController.text = widget.tenant!.contact.email;
      _rentController.text = widget.tenant!.contract.rent.toString();

      // Initialize providers with existing tenant data
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(tenantNameProvider.notifier).state = widget.tenant!.name;
        ref.read(tenantPhoneProvider.notifier).state = widget.tenant!.contact.phone;
        ref.read(tenantEmailProvider.notifier).state = widget.tenant!.contact.email;
        ref.read(tenantActiveProvider.notifier).state = widget.tenant!.active;
        ref.read(tenantContractStartProvider.notifier).state = widget.tenant!.contract.start;
        ref.read(tenantContractEndProvider.notifier).state = widget.tenant!.contract.end;
        ref.read(tenantRentProvider.notifier).state = widget.tenant!.contract.rent;
        ref.read(tenantGridsProvider.notifier).state = List<String>.from(widget.tenant!.grids);
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _rentController.dispose();
    _gridSearchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tenantName = ref.watch(tenantNameProvider);
    final tenantPhone = ref.watch(tenantPhoneProvider);
    final tenantEmail = ref.watch(tenantEmailProvider);
    final tenantPassword = ref.watch(tenantPasswordProvider);
    final tenantConfirmPassword = ref.watch(tenantConfirmPasswordProvider);
    final createUserAccount = ref.watch(createUserAccountProvider);
    final tenantActive = ref.watch(tenantActiveProvider);
    final tenantContractStart = ref.watch(tenantContractStartProvider);
    final tenantContractEnd = ref.watch(tenantContractEndProvider);
    final tenantRent = ref.watch(tenantRentProvider);
    final tenantGrids = ref.watch(tenantGridsProvider);
    final storeId = ref.watch(selectedStoreIdProvider);
    final tenantCrudState = ref.watch(tenantCrudNotifierProvider);
    final isLoading = tenantCrudState.isLoading;

    // Listen to CRUD state changes
    ref.listen<TenantCrudState>(tenantCrudNotifierProvider, (previous, current) {
      if (previous?.isLoading == true && !current.isLoading && current.successMessage != null) {
        if (context.mounted) {
          showSuccessSnackBar(context, current.successMessage!);
          _safeNavigateBack(success: true);
        }
      }

      if (current.errorMessage != null && previous?.errorMessage != current.errorMessage) {
        if (context.mounted) {
          showErrorSnackBar(context, current.errorMessage!);
        }
      }
    });

    return PopScope(
      canPop: true,
      onPopInvoked: (didPop) {
        if (didPop) {
          setState(() {
            _isNavigatingAway = true;
          });
        }
      },
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black87,
          title: Text(
            _isEditing ? '編輯租戶' : '創建租戶',
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () => _safeNavigateBack(),
          ),
          actions: [
            if (_isEditing)
              TextButton(
                onPressed: isLoading ? null : () => _safeNavigateBack(),
                child: const Text('取消', style: TextStyle(fontSize: 16)),
              ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: Column(
            children: [
              // Progress indicator
              if (isLoading)
                const LinearProgressIndicator(
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                ),

              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header section
                      _buildHeaderSection(context),
                      const SizedBox(height: 24),

                      // Basic Information Card
                      _buildBasicInfoCard(context),
                      const SizedBox(height: 20),

                      // Contact Information Card
                      _buildContactInfoCard(context),
                      const SizedBox(height: 20),

                      // Grid Assignment Card with Search
                      _buildGridAssignmentCard(context, storeId, tenantGrids),
                      const SizedBox(height: 20),

                      // User Account Card (only for new tenants)
                      if (!_isEditing) ...[
                        _buildUserAccountCard(context, createUserAccount),
                        const SizedBox(height: 20),
                      ],

                      // Contract Information Card
                      _buildContractInfoCard(
                        context,
                        tenantActive,
                        tenantContractStart,
                        tenantContractEnd,
                      ),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),

              // Bottom action bar
              _buildBottomActionBar(context, isLoading),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[600]!, Colors.blue[400]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(_isEditing ? Icons.edit : Icons.person_add, color: Colors.white, size: 28),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _isEditing ? '編輯租戶資料' : '創建新租戶',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _isEditing ? '更新下方的租戶詳細資料' : '填寫表單來創建新的租戶',
                  style: TextStyle(color: Colors.white.withOpacity(0.9), fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoCard(BuildContext context) {
    return _buildSectionCard(
      title: '基本資料',
      icon: Icons.store,
      child: Column(
        children: [
          _buildStyledTextField(
            controller: _nameController,
            label: '租戶名稱',
            hint: '請輸入租戶名稱',
            icon: Icons.business,
            onChanged: (value) => ref.read(tenantNameProvider.notifier).state = value,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '租戶名稱為必填項';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildContactInfoCard(BuildContext context) {
    final tenantEmail = ref.watch(tenantEmailProvider);
    final tenantPhone = ref.watch(tenantPhoneProvider);

    return _buildSectionCard(
      title: '聯繫資料',
      icon: Icons.contact_phone,
      child: Column(
        children: [
          _buildStyledTextField(
            controller: _phoneController,
            label: '聯繫電話',
            hint: '請輸入電話號碼',
            icon: Icons.phone,
            keyboardType: TextInputType.phone,
            onChanged: (value) => ref.read(tenantPhoneProvider.notifier).state = value,
            validator: (value) {
              if ((value == null || value.isEmpty) && tenantEmail.isEmpty) {
                return '電話或電子郵箱至少提供一項';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _buildStyledTextField(
            controller: _emailController,
            label: '電子郵箱',
            hint: _isEditing ? null : '請輸入電子郵箱地址',
            icon: Icons.email,
            keyboardType: TextInputType.emailAddress,
            enabled: !_isEditing,
            onChanged: (value) => ref.read(tenantEmailProvider.notifier).state = value,
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final emailRegExp = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                if (!emailRegExp.hasMatch(value)) {
                  return '請輸入有效的電子郵箱地址';
                }
              } else if (tenantPhone.isEmpty) {
                return '電話或電子郵箱至少提供一項';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildUserAccountCard(BuildContext context, bool createUserAccount) {
    final tenantPassword = ref.watch(tenantPasswordProvider);

    return _buildSectionCard(
      title: '用戶帳號',
      icon: Icons.account_circle,
      child: Column(
        children: [
          _buildStyledSwitchTile(
            title: '創建用戶帳號',
            subtitle: '為租戶創建可登錄系統的帳號',
            value: createUserAccount,
            onChanged: (value) => ref.read(createUserAccountProvider.notifier).state = value,
          ),
          if (createUserAccount) ...[
            const SizedBox(height: 16),
            _buildStyledTextField(
              controller: _passwordController,
              label: '密碼',
              hint: '請輸入密碼',
              icon: Icons.lock,
              obscureText: _obscurePassword,
              suffixIcon: IconButton(
                icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                onPressed: () => setState(() => _obscurePassword = !_obscurePassword),
              ),
              onChanged: (value) => ref.read(tenantPasswordProvider.notifier).state = value,
              validator: (value) {
                if (createUserAccount) {
                  if (value == null || value.isEmpty) {
                    return '創建用戶帳號時密碼為必填項';
                  }
                  if (value.length < 6) {
                    return '密碼必須至少有6個字符';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            _buildStyledTextField(
              controller: _confirmPasswordController,
              label: '確認密碼',
              hint: '請再次輸入密碼',
              icon: Icons.lock_outline,
              obscureText: _obscureConfirmPassword,
              suffixIcon: IconButton(
                icon: Icon(_obscureConfirmPassword ? Icons.visibility : Icons.visibility_off),
                onPressed: () => setState(() => _obscureConfirmPassword = !_obscureConfirmPassword),
              ),
              onChanged: (value) => ref.read(tenantConfirmPasswordProvider.notifier).state = value,
              validator: (value) {
                if (createUserAccount) {
                  if (value == null || value.isEmpty) {
                    return '請確認密碼';
                  }
                  if (value != tenantPassword) {
                    return '密碼不匹配';
                  }
                }
                return null;
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildContractInfoCard(
    BuildContext context,
    bool tenantActive,
    DateTime contractStart,
    DateTime contractEnd,
  ) {
    return _buildSectionCard(
      title: '合約資料',
      icon: Icons.description,
      child: Column(
        children: [
          _buildStyledSwitchTile(
            title: '租戶狀態',
            subtitle: '設置租戶為活躍狀態',
            value: tenantActive,
            onChanged: (value) => ref.read(tenantActiveProvider.notifier).state = value,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDateField(
                  label: '合約開始',
                  date: contractStart,
                  icon: Icons.event_available,
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: contractStart,
                      firstDate: DateTime(2020),
                      lastDate: DateTime(2100),
                    );
                    if (date != null) {
                      ref.read(tenantContractStartProvider.notifier).state = date;
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDateField(
                  label: '合約結束',
                  date: contractEnd,
                  icon: Icons.event_busy,
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: contractEnd,
                      firstDate: DateTime(2020),
                      lastDate: DateTime(2100),
                    );
                    if (date != null) {
                      ref.read(tenantContractEndProvider.notifier).state = date;
                    }
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildStyledTextField(
            controller: _rentController,
            label: '月租金額',
            hint: '請輸入月租金額',
            icon: Icons.attach_money,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            onChanged:
                (value) =>
                    ref.read(tenantRentProvider.notifier).state = double.tryParse(value) ?? 0.0,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '月租為必填項';
              }
              final rent = double.tryParse(value);
              if (rent == null) {
                return '請輸入有效的金額';
              }
              if (rent < 0) {
                return '月租不能為負數';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildGridAssignmentCard(BuildContext context, String storeId, List<String> tenantGrids) {
    return _buildSectionCard(
      title: '格位分配',
      icon: Icons.grid_view,
      hasSearch: true,
      searchHint: '搜索格位編號或尺寸...',
      onSearchChanged: (value) {
        setState(() {
          _gridSearchQuery = value.toLowerCase();
        });
      },
      onSearchClear: () {
        _gridSearchController.clear();
        setState(() {
          _gridSearchQuery = '';
        });
      },
      child: _buildFilteredGridPicker(storeId, tenantGrids),
    );
  }

  Widget _buildFilteredGridPicker(String storeId, List<String> tenantGrids) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_gridSearchQuery.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.filter_list, color: Colors.blue[600], size: 16),
                const SizedBox(width: 8),
                Text(
                  '正在搜索: "$_gridSearchQuery"',
                  style: TextStyle(
                    color: Colors.blue[700],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: () {
                    _gridSearchController.clear();
                    setState(() {
                      _gridSearchQuery = '';
                    });
                  },
                  child: Icon(Icons.close, color: Colors.blue[600], size: 16),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],
        GridPicker(
          storeId: storeId,
          selectedGridIds: tenantGrids,
          searchQuery: _gridSearchQuery,
          onChanged: (selectedGrids) {
            ref.read(tenantGridsProvider.notifier).state = selectedGrids;
          },
        ),
      ],
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required Widget child,
    bool hasSearch = false,
    String? searchHint,
    Function(String)? onSearchChanged,
    VoidCallback? onSearchClear,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(icon, color: Colors.blue[700], size: 20),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Padding(padding: const EdgeInsets.all(20), child: child),
        ],
      ),
    );
  }

  Widget _buildStyledTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    required IconData icon,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    bool enabled = true,
    required Function(String) onChanged,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: obscureText,
      enabled: enabled,
      onChanged: onChanged,
      validator: validator,
      style: const TextStyle(fontSize: 16),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.grey[600]),
        suffixIcon: suffixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.blue[600]!, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: enabled ? Colors.grey[50] : Colors.grey[100],
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
    );
  }

  Widget _buildStyledSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: SwitchListTile(
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
        subtitle: Text(subtitle, style: TextStyle(color: Colors.grey[600])),
        value: value,
        onChanged: onChanged,
        activeColor: Colors.blue[600],
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      ),
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime date,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.grey[600], size: 20),
                const SizedBox(width: 8),
                Text(
                  label,
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _dateFormat.format(date),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActionBar(BuildContext context, bool isLoading) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: isLoading ? null : _saveTenant,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            child:
                isLoading
                    ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(_isEditing ? Icons.update : Icons.add),
                        const SizedBox(width: 8),
                        Text(_isEditing ? '更新租戶' : '創建租戶'),
                      ],
                    ),
          ),
        ),
      ),
    );
  }

  bool _validateDates(DateTime start, DateTime end, {bool showError = true}) {
    if (end.isBefore(start)) {
      if (showError && context.mounted) {
        showErrorSnackBar(context, '合約結束日期必須在開始日期之後');
      }
      return false;
    }
    return true;
  }

  void _saveTenant() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    final contractStart = ref.read(tenantContractStartProvider);
    final contractEnd = ref.read(tenantContractEndProvider);
    if (!_validateDates(contractStart, contractEnd)) {
      return;
    }

    final name = ref.read(tenantNameProvider);
    final phone = ref.read(tenantPhoneProvider);
    final email = ref.read(tenantEmailProvider);
    final createUserAccount = ref.read(createUserAccountProvider);
    final password = ref.read(tenantPasswordProvider);
    final active = ref.read(tenantActiveProvider);
    final rent = ref.read(tenantRentProvider);
    final grids = ref.read(tenantGridsProvider);
    final storeId = ref.read(selectedStoreIdProvider);
    final now = DateTime.now();

    Logger.debug('[TenantFormPage] Saving tenant with grid IDs: $grids');

    try {
      if (_isEditing) {
        final updatedTenant = widget.tenant!.copyWith(
          name: name,
          contact: ContactInfo(phone: phone, email: email),
          active: active,
          contract: ContractEntity(start: contractStart, end: contractEnd, rent: rent),
          grids: grids,
          updatedAt: now,
        );

        Logger.debug(
          '[TenantFormPage] Updating tenant ${widget.tenant!.id} with data: ${updatedTenant.toString()}',
        );
        await ref
            .read(tenantCrudNotifierProvider.notifier)
            .updateTenant(storeId, widget.tenant!.id, updatedTenant);
      } else {
        final newTenant = TenantEntity(
          id: '',
          name: name,
          contact: ContactInfo(phone: phone, email: email),
          active: active,
          contract: ContractEntity(start: contractStart, end: contractEnd, rent: rent),
          grids: grids,
          createdAt: now,
          updatedAt: now,
        );

        Logger.debug('[TenantFormPage] Creating new tenant with data: ${newTenant.toString()}');

        if (createUserAccount && email.isNotEmpty && password.isNotEmpty) {
          Logger.debug('[TenantFormPage] Creating tenant with user account for email: $email');
          await ref
              .read(tenantCrudNotifierProvider.notifier)
              .createTenantWithUser(storeId, newTenant, email: email, password: password);
        } else {
          await ref.read(tenantCrudNotifierProvider.notifier).createTenant(storeId, newTenant);
        }
      }
    } catch (e) {
      Logger.error('[TenantFormPage] Error saving tenant', e);
    }
  }
}
