import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:grid_pos/core/constants/grid_constants.dart';
import 'package:grid_pos/core/utils/debouncer.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/grid_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/grid_providers.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart';
import 'package:grid_pos/shared/widgets/app_button.dart';
import 'package:grid_pos/shared/widgets/custom_snackbar.dart';

/// Form state provider for the grid code
final gridCodeProvider = StateProvider<String>((ref) => '');

/// Form state provider for the grid size
final gridSizeProvider = StateProvider<String>((ref) => GridConstants.gridSizes.first);

/// Service provider for grid code validation without modifying other providers
final gridCodeValidationServiceProvider =
    Provider<Future<bool> Function(String, String, {String? excludeGridId})>((ref) {
      final repository = ref.watch(gridRepositoryProvider);

      return (String storeId, String code, {String? excludeGridId}) async {
        if (code.isEmpty) return true;

        try {
          final grids = await repository.watchGrids(storeId).first;
          final exists = grids.any(
            (grid) =>
                grid.code.toLowerCase() == code.toLowerCase() &&
                (excludeGridId == null || grid.id != excludeGridId),
          );
          return !exists;
        } catch (e) {
          Logger.error('Error checking grid code uniqueness', e);
          return false;
        }
      };
    });

/// Provider for the code validation state
final codeValidationProvider = FutureProvider.autoDispose
    .family<bool, ({String storeId, String code, String? excludeGridId})>((ref, params) async {
      if (params.code.isEmpty) {
        return true;
      }

      final validateCode = ref.watch(gridCodeValidationServiceProvider);
      return validateCode(params.storeId, params.code, excludeGridId: params.excludeGridId);
    });

/// Provider to load a grid by ID
final gridByIdProvider = FutureProvider.autoDispose
    .family<GridEntity?, ({String storeId, String gridId})>((ref, params) async {
      try {
        Logger.debug(
          '[gridByIdProvider] Loading grid ${params.gridId} for store ${params.storeId}',
        );
        final repository = ref.watch(gridRepositoryProvider);
        return await repository.watchGrid(params.storeId, params.gridId).first;
      } catch (e) {
        Logger.error('[gridByIdProvider] Error loading grid', e);
        return null;
      }
    });

/// A page for creating or editing a grid entity with improved UI/UX
class GridFormPage extends ConsumerStatefulWidget {
  /// The grid to edit (null for creating a new grid)
  final GridEntity? grid;

  /// The ID of the grid to edit (used when grid object is not available)
  final String? gridId;

  /// Creates a grid form page for a new grid
  const GridFormPage({super.key}) : grid = null, gridId = null;

  /// Creates a grid form page for editing an existing grid
  const GridFormPage.edit({this.grid, this.gridId, super.key});

  @override
  ConsumerState<GridFormPage> createState() => _GridFormPageState();
}

class _GridFormPageState extends ConsumerState<GridFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _codeController = TextEditingController();
  bool _isEditing = false;
  String? _originalCode;
  bool _isNavigatingAway = false;
  GridEntity? _loadedGrid;

  // Debouncer for code validation
  final _debouncer = Debouncer(delay: const Duration(milliseconds: 500));

  @override
  void initState() {
    super.initState();
    _isEditing = widget.grid != null || widget.gridId != null;

    if (widget.grid != null) {
      _initializeFromGrid(widget.grid!);
    } else if (!_isEditing) {
      _clearInputFields();
    }
  }

  void _initializeFromGrid(GridEntity grid) {
    _loadedGrid = grid;
    _codeController.text = grid.code;
    _originalCode = grid.code;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ref.read(gridCodeProvider.notifier).state = grid.code;
        ref.read(gridSizeProvider.notifier).state = grid.size;
      }
    });
  }

  @override
  void dispose() {
    _codeController.dispose();
    _debouncer.dispose();
    super.dispose();
  }

  void _safeNavigateBack({bool success = false}) {
    if (!_isNavigatingAway && mounted && context.mounted) {
      Logger.debug('Navigating back from GridFormPage with success: $success');
      setState(() {
        _isNavigatingAway = true;
      });

      try {
        if (context.canPop()) {
          context.pop(success);
        } else {
          context.go('/grids');
        }
      } catch (e) {
        Logger.error('Navigation error in GridFormPage: $e');
        context.go('/grids');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final gridCode = ref.watch(gridCodeProvider);
    final gridSize = ref.watch(gridSizeProvider);
    final storeId = ref.watch(selectedStoreIdProvider);
    final gridCrudState = ref.watch(gridCrudNotifierProvider);
    final isLoading = gridCrudState.isLoading;

    // Handle loading grid by ID
    if (_isEditing && widget.grid == null && widget.gridId != null && _loadedGrid == null) {
      final gridAsync = ref.watch(gridByIdProvider((storeId: storeId, gridId: widget.gridId!)));

      return gridAsync.when(
        data: (grid) {
          if (grid != null) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                _initializeFromGrid(grid);
                setState(() {});
              }
            });
          }
          return _buildLoadingScreen();
        },
        loading: () => _buildLoadingScreen(),
        error: (error, stack) => _buildErrorScreen(error),
      );
    }

    final currentGrid = _loadedGrid ?? widget.grid;
    final validationParams = (
      storeId: storeId,
      code: gridCode,
      excludeGridId: _isEditing && currentGrid != null ? currentGrid.id : null,
    );
    final codeValidation = ref.watch(codeValidationProvider(validationParams));

    // Listen to CRUD state changes
    ref.listen<GridCrudState>(gridCrudNotifierProvider, (previous, current) {
      if (!mounted) return;

      if (previous?.isLoading == true && !current.isLoading && current.successMessage != null) {
        if (context.mounted) {
          showSuccessSnackBar(context, current.successMessage!);
          _safeNavigateBack(success: true);

          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted) {
              ref.read(gridCrudNotifierProvider.notifier).clearMessages();
            }
          });
        }
      }

      if (current.errorMessage != null && previous?.errorMessage != current.errorMessage) {
        if (context.mounted) {
          showErrorSnackBar(context, current.errorMessage!);
        }
      }
    });

    return PopScope(
      canPop: true,
      onPopInvoked: (didPop) {
        if (didPop) {
          setState(() {
            _isNavigatingAway = true;
          });
        }
      },
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black87,
          title: Text(
            _isEditing ? '編輯格位' : '創建格位',
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () => _safeNavigateBack(),
          ),
          actions: [
            if (_isEditing)
              TextButton(
                onPressed: isLoading ? null : () => _safeNavigateBack(),
                child: const Text('取消', style: TextStyle(fontSize: 16)),
              ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: Column(
            children: [
              // Progress indicator
              if (isLoading)
                const LinearProgressIndicator(
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                ),

              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header section
                      _buildHeaderSection(context),
                      const SizedBox(height: 24),

                      // Form card
                      _buildFormCard(context, gridCode, gridSize, codeValidation, validationParams),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),

              // Bottom action bar
              _buildBottomActionBar(context, isLoading, codeValidation),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        title: const Text('載入中...'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => _safeNavigateBack(),
        ),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(width: 48, height: 48, child: CircularProgressIndicator(strokeWidth: 3)),
            SizedBox(height: 24),
            Text('載入格位詳情...', style: TextStyle(fontSize: 16, color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen(Object error) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        title: const Text('載入錯誤'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => _safeNavigateBack(),
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
              ),
              const SizedBox(height: 24),
              Text(
                '載入格位時發生錯誤',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '$error',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => _safeNavigateBack(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  ),
                  child: const Text('返回'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.purple[600]!, Colors.purple[400]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(_isEditing ? Icons.edit : Icons.grid_4x4, color: Colors.white, size: 28),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _isEditing ? '編輯格位資料' : '創建新格位',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _isEditing ? '更新下方的格位詳細資料' : '填寫表單來創建新的格位',
                  style: TextStyle(color: Colors.white.withOpacity(0.9), fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormCard(
    BuildContext context,
    String gridCode,
    String gridSize,
    AsyncValue<bool> codeValidation,
    ({String storeId, String code, String? excludeGridId}) validationParams,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.purple[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.settings, color: Colors.purple[700], size: 20),
                ),
                const SizedBox(width: 12),
                const Text(
                  '格位詳情',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // Grid Code field
                _buildStyledTextField(
                  controller: _codeController,
                  label: '格位編號',
                  hint: '輸入唯一編號（例如 A1, B2）',
                  icon: Icons.grid_on,
                  errorText: _getCodeErrorText(codeValidation),
                  suffixIcon:
                      codeValidation.isLoading
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: Padding(
                              padding: EdgeInsets.all(8.0),
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          )
                          : _getValidationIcon(codeValidation),
                  onChanged: (value) {
                    ref.read(gridCodeProvider.notifier).state = value;

                    if (value.isNotEmpty) {
                      _debouncer.run(() {
                        ref.invalidate(codeValidationProvider(validationParams));
                      });
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '格位編號為必填項';
                    }

                    if (codeValidation.hasValue && !codeValidation.value!) {
                      return '格位編號必須在商店內唯一';
                    }

                    return null;
                  },
                ),
                const SizedBox(height: 20),

                // Grid Size field
                _buildStyledDropdown(
                  label: '格位尺寸',
                  hint: '選擇格位尺寸',
                  icon: Icons.aspect_ratio,
                  value: gridSize,
                  items: GridConstants.gridSizes,
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      ref.read(gridSizeProvider.notifier).state = newValue;
                    }
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStyledTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    required IconData icon,
    String? errorText,
    Widget? suffixIcon,
    required Function(String) onChanged,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      onChanged: onChanged,
      validator: validator,
      style: const TextStyle(fontSize: 16),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        errorText: errorText,
        prefixIcon: Icon(icon, color: Colors.grey[600]),
        suffixIcon: suffixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.purple[600]!, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey[50],
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
    );
  }

  Widget _buildStyledDropdown({
    required String label,
    String? hint,
    required IconData icon,
    required String value,
    required List<String> items,
    required Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.grey[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.purple[600]!, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey[50],
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
      value: value,
      items:
          items.map<DropdownMenuItem<String>>((String size) {
            return DropdownMenuItem<String>(
              value: size,
              child: Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: _getSizeColor(size),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(size, style: const TextStyle(fontSize: 16)),
                ],
              ),
            );
          }).toList(),
      onChanged: onChanged,
    );
  }

  Color _getSizeColor(String size) {
    switch (size.toUpperCase()) {
      case 'S':
        return Colors.green;
      case 'M':
        return Colors.orange;
      case 'L':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget? _getValidationIcon(AsyncValue<bool> validation) {
    if (validation.isLoading) return null;

    if (validation.hasValue) {
      return Icon(
        validation.value! ? Icons.check_circle : Icons.error,
        color: validation.value! ? Colors.green : Colors.red,
        size: 20,
      );
    }

    return null;
  }

  Widget _buildBottomActionBar(
    BuildContext context,
    bool isLoading,
    AsyncValue<bool> codeValidation,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed:
                isLoading
                    ? null
                    : () {
                      if (_formKey.currentState!.validate() && codeValidation.value == true) {
                        _saveGrid();
                      } else if (codeValidation.value == false) {
                        showErrorSnackBar(context, '格位編號已被使用，請使用另一個編號');
                      }
                    },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple[600],
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            child:
                isLoading
                    ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(_isEditing ? Icons.update : Icons.add),
                        const SizedBox(width: 8),
                        Text(_isEditing ? '更新格位' : '創建格位'),
                      ],
                    ),
          ),
        ),
      ),
    );
  }

  String? _getCodeErrorText(AsyncValue<bool> validation) {
    if (validation.isLoading) {
      return null;
    }

    if (validation.hasError) {
      return '驗證時發生錯誤，請重試';
    }

    return validation.value == false ? '此格位編號已被使用' : null;
  }

  void _saveGrid() async {
    if (!mounted) return;

    if (_formKey.currentState?.validate() != true) {
      return;
    }

    final code = ref.read(gridCodeProvider);
    final size = ref.read(gridSizeProvider);
    final storeId = ref.read(selectedStoreIdProvider);
    final now = DateTime.now();

    final currentGrid = _loadedGrid ?? widget.grid;

    try {
      if (_isEditing && currentGrid != null) {
        final updatedGrid = currentGrid.copyWith(code: code, size: size, updatedAt: now);
        await ref
            .read(gridCrudNotifierProvider.notifier)
            .updateGrid(storeId, currentGrid.id, updatedGrid);
      } else {
        final newGrid = GridEntity(
          id: '',
          code: code,
          size: size,
          tenantId: null,
          createdAt: now,
          updatedAt: now,
        );

        await ref.read(gridCrudNotifierProvider.notifier).createGrid(storeId, newGrid);
        _clearInputFields();
      }
    } catch (e) {
      if (mounted && context.mounted) {
        showErrorSnackBar(context, '錯誤: ${e.toString()}');
      }
    }
  }

  void _clearInputFields() {
    _codeController.clear();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ref.read(gridCodeProvider.notifier).state = '';
      }
    });
  }
}
