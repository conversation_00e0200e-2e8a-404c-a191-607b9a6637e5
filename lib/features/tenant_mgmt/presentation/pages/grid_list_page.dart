import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/grid_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/tenant_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/grid_providers.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/tenant_providers.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/widgets/store_selector.dart';
import 'package:go_router/go_router.dart';

/// 格位列表頁面
class GridListPage extends ConsumerStatefulWidget {
  /// 建構子
  const GridListPage({super.key});

  @override
  ConsumerState<GridListPage> createState() => _GridListPageState();
}

class _GridListPageState extends ConsumerState<GridListPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedSize = '';
  String _assignmentFilter = 'all'; // 'all', 'assigned', 'unassigned'
  bool _isSearching = false;

  final List<String> _gridSizes = ['S', 'M', 'L']; // 格位尺寸選項

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final storeId = ref.watch(selectedStoreIdProvider);
    final gridsAsync = ref.watch(currentStoreGridsProvider);
    final tenants = ref.watch(tenantsProvider(storeId ?? ''));

    return Scaffold(
      appBar: _buildAppBar(theme),
      body: gridsAsync.when(
        data: (grids) => _buildScrollableContent(grids, tenants.valueOrNull ?? [], theme),
        loading:
            () => const Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [CircularProgressIndicator(), SizedBox(height: 16), Text('載入格位資料中...')],
              ),
            ),
        error: (error, stackTrace) {
          Logger.error('Error loading grids', error, stackTrace);
          return Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text('載入格位失敗: ${error.toString()}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref.refresh(currentStoreGridsProvider),
                  child: const Text('重試'),
                ),
              ],
            ),
          );
        },
      ),
      floatingActionButton: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: theme.colorScheme.primary.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: FloatingActionButton.extended(
          onPressed: () {
            final storeId = ref.read(selectedStoreIdProvider);
            context.push('/grid-form', extra: {'storeId': storeId});
          },
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          elevation: 0,
          icon: const Icon(Icons.add),
          label: const Text('新增格位'),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    if (_isSearching) {
      return AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        automaticallyImplyLeading: false,
        actions: [IconButton(icon: const Icon(Icons.close), onPressed: _exitSearchMode)],
        title: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
          ),
          child: TextField(
            controller: _searchController,
            autofocus: true,
            decoration: InputDecoration(
              hintText: '搜尋格位編號...',
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(vertical: 12),
              hintStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant, fontSize: 16),
              prefixIcon: Icon(Icons.search, color: theme.colorScheme.onSurfaceVariant, size: 20),
              suffixIcon:
                  _searchQuery.isNotEmpty
                      ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: IconButton(
                          icon: Icon(Icons.clear, color: theme.colorScheme.onSurfaceVariant),
                          onPressed: () {
                            _searchController.clear();
                            setState(() => _searchQuery = '');
                          },
                        ),
                      )
                      : null,
            ),
            style: TextStyle(color: theme.colorScheme.onSurfaceVariant, fontSize: 16),
            onChanged: (value) => setState(() => _searchQuery = value.toLowerCase()),
          ),
        ),
      );
    }

    return AppBar(
      title: const Text('格位列表', style: TextStyle(fontWeight: FontWeight.bold)),
      backgroundColor: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      actions: [
        // StoreSelector(),
        IconButton(
          icon: Stack(
            children: [
              const Icon(Icons.search),
              if (_searchQuery.isNotEmpty || _selectedSize.isNotEmpty || _assignmentFilter != 'all')
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                  ),
                ),
            ],
          ),
          onPressed: _enterSearchMode,
        ),
      ],
    );
  }

  void _enterSearchMode() {
    setState(() => _isSearching = true);
  }

  void _exitSearchMode() {
    setState(() {
      _isSearching = false;
      _searchController.clear();
      _searchQuery = '';
    });
  }

  Widget _buildScrollableContent(
    List<GridEntity> grids,
    List<TenantEntity> tenants,
    ThemeData theme,
  ) {
    final filteredGrids = _filterGrids(grids);

    // 如果沒有格位，顯示空狀態
    if (filteredGrids.isEmpty) {
      return RefreshIndicator(
        onRefresh: () async => ref.refresh(currentStoreGridsProvider),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: _buildSearchAndStats(grids, tenants, theme),
              ),
              SizedBox(height: MediaQuery.of(context).size.height * 0.2),
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _hasActiveFilters() ? Icons.search_off : Icons.grid_view_outlined,
                      size: 64,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _hasActiveFilters() ? '沒有找到符合條件的格位' : '還沒有格位',
                      style: theme.textTheme.titleMedium,
                    ),
                    if (_hasActiveFilters()) ...[
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _selectedSize = '';
                            _assignmentFilter = 'all';
                          });
                          if (_isSearching) {
                            _exitSearchMode();
                          }
                        },
                        child: const Text('清除篩選'),
                      ),
                    ] else ...[
                      const SizedBox(height: 8),
                      Text(
                        '開始建立您的第一個格位，\n管理您的店鋪空間',
                        textAlign: TextAlign.center,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          height: 1.5,
                        ),
                      ),
                      const SizedBox(height: 32),
                      FilledButton.icon(
                        onPressed: () {
                          final storeId = ref.read(selectedStoreIdProvider);
                          context.push('/grid-form', extra: {'storeId': storeId});
                        },
                        icon: const Icon(Icons.add),
                        label: const Text('建立第一個格位'),
                        style: FilledButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    // 有格位時，使用統一的 ListView
    return RefreshIndicator(
      onRefresh: () async => ref.refresh(currentStoreGridsProvider),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredGrids.length + 1, // +1 for the header
        itemBuilder: (context, index) {
          // 第一個項目是搜索和統計區域
          if (index == 0) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: _buildSearchAndStats(grids, tenants, theme),
            );
          }

          // 其餘項目是格位列表
          final gridIndex = index - 1;
          final grid = filteredGrids[gridIndex];

          String? tenantName;
          if (grid.tenantId != null) {
            final foundTenants = tenants.where((tenant) => tenant.id == grid.tenantId).toList();
            if (foundTenants.isNotEmpty) {
              tenantName = foundTenants.first.name;
            }
          }

          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _GridCard(
              grid: grid,
              storeId: ref.read(selectedStoreIdProvider) ?? '',
              tenantName: tenantName,
              onEdit: () {
                final storeId = ref.read(selectedStoreIdProvider);
                context.push('/grid-form', extra: {'gridId': grid.id, 'storeId': storeId});
              },
              onDelete: () => _showDeleteConfirmation(grid),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSearchAndStats(List<GridEntity> grids, List<TenantEntity> tenants, ThemeData theme) {
    final filteredGrids = _filterGrids(grids);
    final stats = _calculateGridStats(grids);

    return Column(
      children: [
        // Search result indicator when searching
        if (_isSearching && _searchQuery.isNotEmpty) ...[
          Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color:
                  filteredGrids.isEmpty
                      ? Colors.orange.withOpacity(0.1)
                      : Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color:
                    filteredGrids.isEmpty
                        ? Colors.orange.withOpacity(0.3)
                        : Colors.green.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  filteredGrids.isEmpty ? Icons.search_off : Icons.check_circle_outline,
                  color: filteredGrids.isEmpty ? Colors.orange : Colors.green,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    filteredGrids.isEmpty
                        ? '沒有找到符合 "$_searchQuery" 的格位'
                        : '找到 ${filteredGrids.length} 個符合 "$_searchQuery" 的格位',
                    style: TextStyle(
                      color: filteredGrids.isEmpty ? Colors.orange.shade700 : Colors.green.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (filteredGrids.isNotEmpty) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${filteredGrids.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],

        // Stats Card with integrated filters
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    const Icon(Icons.grid_view, size: 24),
                    const SizedBox(width: 8),
                    Text('格位總數: ${grids.length}', style: theme.textTheme.titleMedium),
                    if (filteredGrids.length != grids.length) ...[
                      const Spacer(),
                      Text('顯示: ${filteredGrids.length}'),
                    ],
                  ],
                ),
                if (grids.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  // Assignment stats row
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatFilterItem(
                          '全部',
                          grids.length,
                          Colors.grey,
                          'all',
                          _assignmentFilter == 'all',
                          () => setState(
                            () => _assignmentFilter = _assignmentFilter == 'all' ? 'all' : 'all',
                          ),
                        ),
                      ),
                      Expanded(
                        child: _buildStatFilterItem(
                          '已分配',
                          stats['assigned'] ?? 0,
                          Colors.green,
                          'assigned',
                          _assignmentFilter == 'assigned',
                          () => setState(
                            () =>
                                _assignmentFilter =
                                    _assignmentFilter == 'assigned' ? 'all' : 'assigned',
                          ),
                        ),
                      ),
                      Expanded(
                        child: _buildStatFilterItem(
                          '未分配',
                          stats['unassigned'] ?? 0,
                          Colors.orange,
                          'unassigned',
                          _assignmentFilter == 'unassigned',
                          () => setState(
                            () =>
                                _assignmentFilter =
                                    _assignmentFilter == 'unassigned' ? 'all' : 'unassigned',
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Size stats row
                  Row(
                    children:
                        _gridSizes.map((size) {
                          final count = stats[size] ?? 0;
                          final isSelected = _selectedSize == size;
                          return Expanded(
                            child: _buildStatFilterItem(
                              '尺寸 $size',
                              count,
                              _getSizeColor(size),
                              size,
                              isSelected,
                              () => setState(() => _selectedSize = isSelected ? '' : size),
                            ),
                          );
                        }).toList(),
                  ),
                  // Active filter indicator
                  if (_selectedSize.isNotEmpty || _assignmentFilter != 'all') ...[
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (_selectedSize.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: Chip(
                              label: Text('尺寸: $_selectedSize'),
                              onDeleted: () => setState(() => _selectedSize = ''),
                              deleteIcon: const Icon(Icons.close, size: 18),
                            ),
                          ),
                        if (_assignmentFilter != 'all')
                          Chip(
                            label: Text(
                              '狀態: ${_getAssignmentFilterDisplayName(_assignmentFilter)}',
                            ),
                            onDeleted: () => setState(() => _assignmentFilter = 'all'),
                            deleteIcon: const Icon(Icons.close, size: 18),
                          ),
                      ],
                    ),
                  ],
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatFilterItem(
    String label,
    int count,
    Color color,
    String filterValue,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: isSelected ? Border.all(color: color, width: 2) : null,
            color: isSelected ? color.withOpacity(0.1) : null,
          ),
          child: Column(
            children: [
              Text(
                '$count',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isSelected ? color : color,
                ),
              ),
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: isSelected ? color : null,
                  fontWeight: isSelected ? FontWeight.w600 : null,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getSizeColor(String size) {
    switch (size) {
      case 'S':
        return Colors.blue;
      case 'M':
        return Colors.orange;
      case 'L':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  bool _hasActiveFilters() {
    return _searchQuery.isNotEmpty || _selectedSize.isNotEmpty || _assignmentFilter != 'all';
  }

  String _getAssignmentFilterDisplayName(String filter) {
    switch (filter) {
      case 'assigned':
        return '已分配';
      case 'unassigned':
        return '未分配';
      default:
        return '全部';
    }
  }

  List<GridEntity> _filterGrids(List<GridEntity> grids) {
    return grids.where((grid) {
      // 搜索過濾
      if (_searchQuery.isNotEmpty && !grid.code.toLowerCase().contains(_searchQuery)) {
        return false;
      }

      // 尺寸過濾
      if (_selectedSize.isNotEmpty && grid.size != _selectedSize) {
        return false;
      }

      // 分配狀態過濾
      if (_assignmentFilter == 'assigned' && (grid.tenantId == null || grid.tenantId!.isEmpty)) {
        return false;
      }
      if (_assignmentFilter == 'unassigned' &&
          (grid.tenantId != null && grid.tenantId!.isNotEmpty)) {
        return false;
      }

      return true;
    }).toList();
  }

  Map<String, int> _calculateGridStats(List<GridEntity> grids) {
    final stats = <String, int>{};

    // Assignment stats
    int assigned = 0;
    int unassigned = 0;

    // Size stats
    for (String size in _gridSizes) {
      stats[size] = 0;
    }

    for (final grid in grids) {
      // Count assignment status
      if (grid.tenantId != null && grid.tenantId!.isNotEmpty) {
        assigned++;
      } else {
        unassigned++;
      }

      // Count sizes
      stats[grid.size] = (stats[grid.size] ?? 0) + 1;
    }

    stats['assigned'] = assigned;
    stats['unassigned'] = unassigned;

    return stats;
  }

  void _showDeleteConfirmation(GridEntity grid) {
    final theme = Theme.of(context);

    if (grid.tenantId != null && grid.tenantId!.isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('無法刪除已分配給租戶的格位')));
      return;
    }

    showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title: Row(
            children: [
              Icon(Icons.delete_forever, color: theme.colorScheme.error),
              const SizedBox(width: 12),
              const Text('刪除格位'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('確定要刪除格位 "${grid.code}" 嗎？', style: theme.textTheme.bodyLarge),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.errorContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, size: 20, color: theme.colorScheme.error),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text('此操作無法撤銷', style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('取消')),
            FilledButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteGrid(grid.id);
              },
              style: FilledButton.styleFrom(
                backgroundColor: theme.colorScheme.error,
                foregroundColor: theme.colorScheme.onError,
              ),
              child: const Text('刪除'),
            ),
          ],
        );
      },
    );
  }

  void _deleteGrid(String gridId) async {
    final storeId = ref.read(selectedStoreIdProvider);
    if (storeId == null || storeId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('未選擇店鋪')));
      return;
    }

    try {
      await ref.read(gridCrudNotifierProvider.notifier).deleteGrid(storeId, gridId);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('刪除格位失敗: ${e.toString()}')));
      }
    }
  }
}

/// 格位卡片組件
class _GridCard extends StatelessWidget {
  final GridEntity grid;
  final String storeId;
  final String? tenantName;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const _GridCard({
    required this.grid,
    required this.storeId,
    this.tenantName,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');

    return Card(
      elevation: 0,
      color: theme.colorScheme.surfaceContainerLow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: theme.colorScheme.outline.withOpacity(0.1)),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onEdit,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with grid info and actions
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _getSizeColor(grid.size).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: _getSizeColor(grid.size).withOpacity(0.3)),
                    ),
                    child: Icon(Icons.grid_view, size: 24, color: _getSizeColor(grid.size)),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              grid.code,
                              style: theme.textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: _getSizeColor(grid.size),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '尺寸 ${grid.size}',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Action buttons
                  _buildActionButtons(context, theme),
                ],
              ),

              const SizedBox(height: 20),

              // Grid information
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    // Tenant status
                    _buildInfoRow(
                      context,
                      Icons.person,
                      '租戶狀態',
                      tenantName != null ? '已分配給: $tenantName' : '未分配',
                      theme,
                      statusColor: tenantName != null ? Colors.green : Colors.grey,
                    ),
                    const SizedBox(height: 12),
                    // Dates
                    Row(
                      children: [
                        Expanded(
                          child: _buildInfoRow(
                            context,
                            Icons.calendar_today,
                            '建立時間',
                            dateFormat.format(grid.createdAt),
                            theme,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildInfoRow(
                            context,
                            Icons.update,
                            '更新時間',
                            dateFormat.format(grid.updatedAt),
                            theme,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, ThemeData theme) {
    final canDelete = grid.tenantId == null || grid.tenantId!.isEmpty;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer.withOpacity(0.5),
            borderRadius: BorderRadius.circular(8),
          ),
          child: IconButton(
            icon: Icon(Icons.edit_outlined, color: theme.colorScheme.primary),
            tooltip: '編輯格位',
            onPressed: onEdit,
          ),
        ),
        const SizedBox(width: 8),
        Container(
          decoration: BoxDecoration(
            color:
                canDelete
                    ? theme.colorScheme.errorContainer.withOpacity(0.5)
                    : theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8),
          ),
          child: IconButton(
            icon: Icon(
              Icons.delete_outline,
              color: canDelete ? theme.colorScheme.error : theme.colorScheme.onSurfaceVariant,
            ),
            tooltip: canDelete ? '刪除格位' : '無法刪除已分配的格位',
            onPressed: canDelete ? onDelete : null,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    IconData icon,
    String label,
    String value,
    ThemeData theme, {
    Color? statusColor,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 18, color: theme.colorScheme.onSurfaceVariant),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: statusColor ?? theme.colorScheme.onSurface,
                  fontWeight: statusColor != null ? FontWeight.w600 : null,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getSizeColor(String size) {
    switch (size) {
      case 'S':
        return Colors.blue;
      case 'M':
        return Colors.orange;
      case 'L':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }
}
