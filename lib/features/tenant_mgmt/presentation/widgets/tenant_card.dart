import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/grid_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/tenant_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/grid_providers.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart';
import 'package:intl/intl.dart';

/// A card widget that displays tenant information with enhanced UI/UX
class TenantCard extends ConsumerWidget {
  /// The tenant entity to display
  final TenantEntity tenant;

  /// Callback when card is tapped
  final VoidCallback? onTap;

  /// Callback when edit button is pressed
  final VoidCallback? onEdit;

  /// Callback when delete button is pressed
  final VoidCallback? onDelete;

  /// Whether this tenant is currently being processed (loading state)
  final bool isProcessing;

  /// Creates a tenant card
  const TenantCard({
    super.key,
    required this.tenant,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.isProcessing = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final storeId = ref.watch(selectedStoreIdProvider);

    // Watch grids assigned to this tenant
    final tenantGridsAsync = ref.watch(
      tenantGridsProvider((storeId: storeId, tenantId: tenant.id)),
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        elevation: 2,
        shadowColor: Colors.black.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2), width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header section with name, status, and actions
                  _buildHeader(context, theme),

                  const SizedBox(height: 16),

                  // Info sections
                  _buildInfoSection(context, theme),

                  const SizedBox(height: 16),

                  // Grid assignments
                  _buildGridSection(context, theme, tenantGridsAsync),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, ThemeData theme) {
    return Row(
      children: [
        // Avatar with tenant initial
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: _getTenantColor(),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(
              tenant.name.isNotEmpty ? tenant.name[0].toUpperCase() : '?',
              style: theme.textTheme.titleLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),

        const SizedBox(width: 16),

        // Tenant name and status
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                tenant.name,
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              _buildStatusChip(context, theme),
            ],
          ),
        ),

        const SizedBox(width: 8),

        // Action buttons
        _buildActionButtons(context, theme),
      ],
    );
  }

  Widget _buildInfoSection(BuildContext context, ThemeData theme) {
    final dateFormat = DateFormat('MMM dd, yyyy');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Contact information
          if (tenant.contact.phone.isNotEmpty || tenant.contact.email.isNotEmpty)
            _buildInfoRow(
              context,
              icon: Icons.contact_phone_outlined,
              label: '聯絡方式',
              value: tenant.contact.phone.isNotEmpty ? tenant.contact.phone : tenant.contact.email,
              theme: theme,
            ),

          if (tenant.contact.phone.isNotEmpty || tenant.contact.email.isNotEmpty)
            const SizedBox(height: 12),

          // Contract period
          _buildInfoRow(
            context,
            icon: Icons.calendar_month_outlined,
            label: '合約期間',
            value:
                '${dateFormat.format(tenant.contract.start)} - ${dateFormat.format(tenant.contract.end)}',
            theme: theme,
          ),

          const SizedBox(height: 12),

          // Monthly rent with emphasis
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withOpacity(0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.payments_outlined, size: 20, color: theme.colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  '月租',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Text(
                  'HK\$ ${tenant.contract.rent.toStringAsFixed(2)}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required ThemeData theme,
  }) {
    return Row(
      children: [
        Icon(icon, size: 20, color: theme.colorScheme.onSurfaceVariant),
        const SizedBox(width: 12),
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurface),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildGridSection(
    BuildContext context,
    ThemeData theme,
    AsyncValue<List<GridEntity>> tenantGridsAsync,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.grid_view_outlined, size: 20, color: theme.colorScheme.onSurfaceVariant),
            const SizedBox(width: 8),
            Text(
              '分配格位',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _buildGridAssignments(context, theme, tenantGridsAsync),
      ],
    );
  }

  Widget _buildStatusChip(BuildContext context, ThemeData theme) {
    final isActive = tenant.active;
    final color = isActive ? Colors.green : Colors.orange;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          ),
          const SizedBox(width: 6),
          Text(
            isActive ? '有效' : '無效',
            style: TextStyle(fontSize: 12, color: color.shade700, fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, ThemeData theme) {
    if (isProcessing) {
      return Container(
        padding: const EdgeInsets.all(8),
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          ),
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (onEdit != null)
          Container(
            margin: const EdgeInsets.only(right: 4),
            child: IconButton.outlined(
              icon: const Icon(Icons.edit_outlined, size: 18),
              onPressed: onEdit,
              tooltip: '編輯租戶',
              style: IconButton.styleFrom(
                backgroundColor: theme.colorScheme.surface,
                foregroundColor: theme.colorScheme.primary,
                side: BorderSide(color: theme.colorScheme.outline.withOpacity(0.5)),
                minimumSize: const Size(40, 40),
              ),
            ),
          ),
        if (onDelete != null)
          IconButton.outlined(
            icon: const Icon(Icons.delete_outline, size: 18),
            onPressed: onDelete,
            tooltip: '刪除租戶',
            style: IconButton.styleFrom(
              backgroundColor: theme.colorScheme.surface,
              foregroundColor: theme.colorScheme.error,
              side: BorderSide(color: theme.colorScheme.outline.withOpacity(0.5)),
              minimumSize: const Size(40, 40),
            ),
          ),
      ],
    );
  }

  Widget _buildGridAssignments(
    BuildContext context,
    ThemeData theme,
    AsyncValue<List<GridEntity>> tenantGridsAsync,
  ) {
    // Log tenant grid IDs for debugging
    Logger.debug('[TenantCard] Tenant ${tenant.id} has grid IDs in entity: ${tenant.grids}');

    return tenantGridsAsync.when(
      data: (grids) {
        Logger.debug(
          '[TenantCard] Provider returned ${grids.length} grids for tenant ${tenant.id}',
        );
        if (grids.isNotEmpty) {
          Logger.debug(
            '[TenantCard] First grid: ${grids.first.id}, code: ${grids.first.code}, tenantId: ${grids.first.tenantId}',
          );
        }

        if (grids.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.2),
                style: BorderStyle.solid,
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, size: 16, color: theme.colorScheme.onSurfaceVariant),
                const SizedBox(width: 8),
                Text(
                  '未分配格位',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontStyle: FontStyle.italic,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          );
        }

        return Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              grids.map((grid) {
                final backgroundColor = _getGridSizeColor(grid.size);
                final textColor =
                    backgroundColor.computeLuminance() > 0.5 ? Colors.black87 : Colors.white;

                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: backgroundColor.withOpacity(0.3), width: 1),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        grid.code,
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                          color: textColor,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                        decoration: BoxDecoration(
                          color: textColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          grid.size.toUpperCase(),
                          style: TextStyle(
                            fontSize: 9,
                            fontWeight: FontWeight.bold,
                            color: textColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
        );
      },
      loading:
          () => Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '載入格位中...',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
      error: (error, stackTrace) {
        Logger.error('[TenantCard] Error loading grids: $error', error, stackTrace);
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.errorContainer.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(Icons.error_outline, size: 16, color: theme.colorScheme.error),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '載入格位時出錯',
                  style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.error),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getGridSizeColor(String size) {
    switch (size.toUpperCase()) {
      case 'S':
        return Colors.blue.shade100;
      case 'M':
        return Colors.green.shade100;
      case 'L':
        return Colors.orange.shade100;
      case 'XL':
        return Colors.purple.shade100;
      default:
        return Colors.grey.shade100;
    }
  }

  Color _getTenantColor() {
    // Generate a consistent color based on tenant name
    final colors = [
      Colors.blue.shade600,
      Colors.green.shade600,
      Colors.orange.shade600,
      Colors.purple.shade600,
      Colors.teal.shade600,
      Colors.indigo.shade600,
    ];

    final hash = tenant.name.hashCode;
    return colors[hash.abs() % colors.length];
  }
}
