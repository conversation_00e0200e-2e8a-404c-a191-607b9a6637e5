import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/store_mgmt/domain/entities/store_entity.dart';
import 'package:grid_pos/features/store_mgmt/presentation/providers/store_crud_providers.dart'
    as store_crud;
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart';
import 'package:grid_pos/core/utils/logger.dart'; // Import Logger

/// A dropdown widget that allows selecting from available stores
class StoreSelector extends ConsumerWidget {
  /// Creates a store selector dropdown
  const StoreSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final storesAsync = ref.watch(store_crud.availableStoresProvider);
    final selectedStoreId = ref.watch(
      selectedStoreIdProvider,
    ); // This ID should now be maintained by StoreIdNotifier

    Logger.debug('StoreSelector build: selectedStoreId = $selectedStoreId');

    return storesAsync.when(
      data: (stores) {
        Logger.debug('StoreSelector build: stores loaded, count = ${stores.length}');
        if (stores.isEmpty) {
          Logger.debug('StoreSelector: No stores available, showing disabled dropdown.');
          return DropdownButton<String>(
            hint: const Text('沒有可用商店'),
            disabledHint: const Text('沒有可用商店'),
            onChanged: null,
            items: const [],
          );
        }

        // Determine dropdown value.
        // If selectedStoreId exists in the stores list, use it.
        // Otherwise, let value be null so DropdownButton will show the hint.
        // StoreIdNotifier should be responsible for updating selectedStoreId if it's invalid.
        String? dropdownValue =
            stores.any((store) => store.id == selectedStoreId) ? selectedStoreId : null;

        if (dropdownValue == null && stores.isNotEmpty) {
          Logger.warning(
            'StoreSelector: selectedStoreId "$selectedStoreId" not in available stores. Dropdown will show hint. StoreIdNotifier should correct this.',
          );
          // In this case, ideally StoreIdNotifier would detect this and update the selectedStoreId
          // so we don't need to do additional handling to force a change to selectedStoreIdProvider
        }

        return DropdownButton<String>(
          value: dropdownValue, // If selectedStoreId is not in items, will be null and show hint
          hint: const Text("選擇商店"), // Shown when value is null
          icon: const Icon(Icons.arrow_drop_down),
          elevation: 16,
          underline: Container(height: 2, color: Theme.of(context).colorScheme.primary),
          onChanged: (String? newStoreId) {
            if (newStoreId != null && newStoreId != selectedStoreId) {
              Logger.debug('StoreSelector: User selected new store "$newStoreId"');
              // Use the notifier to update and persist the selected store
              ref.read(selectedStoreIdProvider.notifier).selectStore(newStoreId);
            } else if (newStoreId != null && newStoreId == selectedStoreId) {
              Logger.debug(
                'StoreSelector: User re-selected the same store "$newStoreId". No action taken.',
              );
            }
          },
          items:
              stores.map<DropdownMenuItem<String>>((StoreEntity store) {
                return DropdownMenuItem<String>(
                  value: store.id,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.store, size: 16),
                      const SizedBox(width: 8),
                      Text(store.name),
                    ],
                  ),
                );
              }).toList(),
        );
      },
      loading: () {
        Logger.debug('StoreSelector: Stores loading...');
        return const SizedBox(
          height: 24,
          width: 24,
          child: CircularProgressIndicator(strokeWidth: 2),
        );
      },
      error: (error, stackTrace) {
        Logger.error('StoreSelector: Error loading stores: $error', error, stackTrace);
        return Tooltip(
          message: '載入商店時出錯: $error',
          child: TextButton.icon(
            icon: const Icon(Icons.error_outline, color: Colors.red, size: 16),
            label: const Text('錯誤', style: TextStyle(color: Colors.red)),
            onPressed: () => ref.refresh(store_crud.availableStoresProvider),
            style: TextButton.styleFrom(padding: const EdgeInsets.symmetric(horizontal: 8)),
          ),
        );
      },
    );
  }
}
