import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/grid_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/grid_providers.dart';

/// A widget to display and pick grids with multiple selection support and search functionality
class GridPicker extends ConsumerStatefulWidget {
  /// List of selected grid IDs
  final List<String> selectedGridIds;

  /// Callback when a grid is selected
  final Function(List<String>) onChanged;

  /// The store ID
  final String storeId;

  /// Search query to filter grids
  final String? searchQuery;

  /// Search change callback
  final Function(String)? onSearchChanged;

  /// Search clear callback
  final VoidCallback? onSearchClear;

  /// Search controller
  final TextEditingController? searchController;

  /// Creates a grid picker
  const GridPicker({
    super.key, 
    required this.selectedGridIds, 
    required this.onChanged, 
    required this.storeId,
    this.searchQuery,
    this.onSearchChanged,
    this.onSearchClear,
    this.searchController,
  });

  @override
  ConsumerState<GridPicker> createState() => _GridPickerState();
}

class _GridPickerState extends ConsumerState<GridPicker> {
  // 儲存目前多選模式下選中的格位
  final Set<String> _pendingSelections = <String>{};

  // Filter grids based on search query
  List<GridEntity> _filterGrids(List<GridEntity> grids) {
    if (widget.searchQuery == null || widget.searchQuery!.isEmpty) {
      return grids;
    }
    
    final query = widget.searchQuery!.toLowerCase();
    return grids.where((grid) {
      return grid.code.toLowerCase().contains(query) ||
             grid.size.toLowerCase().contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    // Watch all grids
    final allGridsAsync = ref.watch(gridsByStoreIdProvider(widget.storeId));

    return allGridsAsync.when(
      data: (grids) {
        // Filter grids based on search query
        final filteredGrids = _filterGrids(grids);
        
        // Split grids into assigned and unassigned (to this tenant)
        final assignedGrids = filteredGrids.where((grid) => widget.selectedGridIds.contains(grid.id)).toList();
        final availableGrids = filteredGrids.where((grid) => grid.tenantId == null || widget.selectedGridIds.contains(grid.id)).toList();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Selected grids display
            _buildSelectedGridsSection(context, assignedGrids),
            
            const SizedBox(height: 16),

            // Available grids section
            _buildAvailableGridsSection(context, availableGrids),
            
            // Show search results info
            if (widget.searchQuery != null && widget.searchQuery!.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildSearchResultsInfo(grids.length, filteredGrids.length),
            ],
          ],
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Text('載入格位時出錯: $error'),
    );
  }

  Widget _buildSearchResultsInfo(int totalGrids, int filteredGrids) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: Colors.grey[600], size: 16),
          const SizedBox(width: 8),
          Text(
            '顯示 $filteredGrids / $totalGrids 個格位',
            style: TextStyle(
              color: Colors.grey[700],
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedGridsSection(BuildContext context, List<GridEntity> assignedGrids) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('已選格位 (${assignedGrids.length})', 
                 style: Theme.of(context).textTheme.titleMedium),
            if (assignedGrids.isNotEmpty)
              TextButton.icon(
                onPressed: () => _removeAllSelectedGrids(),
                icon: const Icon(Icons.clear_all, size: 18),
                label: const Text('全部移除'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red[600],
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        if (assignedGrids.isEmpty)
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0), 
            child: Text('尚未選擇格位', style: TextStyle(fontStyle: FontStyle.italic))
          )
        else
          _buildSelectedGridsChips(context, assignedGrids),
      ],
    );
  }

  Widget _buildAvailableGridsSection(BuildContext context, List<GridEntity> availableGrids) {
    final unselectedGrids = availableGrids.where((grid) => !widget.selectedGridIds.contains(grid.id)).toList();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('可用格位', style: Theme.of(context).textTheme.titleMedium),
            if (unselectedGrids.isNotEmpty)
              TextButton.icon(
                onPressed: () => _selectAllAvailableGrids(unselectedGrids),
                icon: const Icon(Icons.select_all, size: 18),
                label: const Text('全選'),
              ),
          ],
        ),
        const SizedBox(height: 12),
        
        // Search field
        _buildSearchField(),
        const SizedBox(height: 12),
        
        // Search status indicator
        if (widget.searchQuery != null && widget.searchQuery!.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.filter_list, color: Colors.blue[600], size: 16),
                const SizedBox(width: 8),
                Text(
                  '正在搜索: "${widget.searchQuery}"',
                  style: TextStyle(
                    color: Colors.blue[700],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: widget.onSearchClear,
                  child: Icon(Icons.close, color: Colors.blue[600], size: 16),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
        ],
        
        if (unselectedGrids.isEmpty) ...[
          if (widget.searchQuery != null && widget.searchQuery!.isNotEmpty)
            _buildNoSearchResultsCard()
          else
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 8.0), 
              child: Text('沒有可用格位', style: TextStyle(fontStyle: FontStyle.italic))
            ),
        ] else
          _buildMultiSelectGridList(context, unselectedGrids),

        // 操作按鈕
        if (_pendingSelections.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildMultiSelectActions(context),
        ],
      ],
    );
  }

  Widget _buildSearchField() {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: TextField(
        controller: widget.searchController,
        onChanged: widget.onSearchChanged,
        style: const TextStyle(fontSize: 14),
        decoration: InputDecoration(
          hintText: '搜索格位編號或尺寸...',
          hintStyle: TextStyle(color: Colors.grey[500], fontSize: 14),
          prefixIcon: Icon(Icons.search, color: Colors.grey[500], size: 18),
          suffixIcon: widget.onSearchClear != null
              ? GestureDetector(
                  onTap: widget.onSearchClear,
                  child: Icon(Icons.clear, color: Colors.grey[500], size: 18),
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
      ),
    );
  }

  Widget _buildNoSearchResultsCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.amber[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.amber[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.search_off, color: Colors.amber[700], size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '沒有找到匹配的格位',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.amber[800],
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '請嘗試其他搜索關鍵字',
                  style: TextStyle(
                    color: Colors.amber[700],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedGridsChips(BuildContext context, List<GridEntity> assignedGrids) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: assignedGrids.map((grid) {
        return InputChip(
          label: Text('${grid.code} (${grid.size})'),
          backgroundColor: _getGridSizeColor(grid.size),
          deleteIcon: const Icon(Icons.cancel, size: 18),
          onDeleted: () {
            final newSelection = List<String>.from(widget.selectedGridIds);
            newSelection.remove(grid.id);
            widget.onChanged(newSelection);
          },
        );
      }).toList(),
    );
  }

  Widget _buildMultiSelectGridList(BuildContext context, List<GridEntity> availableGrids) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  '勾選要添加的格位',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
          ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: 300),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: availableGrids.length,
              itemBuilder: (context, index) {
                final grid = availableGrids[index];
                final isSelected = _pendingSelections.contains(grid.id);
                
                return CheckboxListTile(
                  value: isSelected,
                  onChanged: (bool? value) {
                    setState(() {
                      if (value == true) {
                        _pendingSelections.add(grid.id);
                      } else {
                        _pendingSelections.remove(grid.id);
                      }
                    });
                  },
                  title: Text('${grid.code} (${grid.size})'),
                  subtitle: Text('格位大小: ${grid.size}'),
                  secondary: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: _getGridSizeColor(grid.size),
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                  ),
                  dense: true,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMultiSelectActions(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _addSelectedGrids(),
            icon: const Icon(Icons.add),
            label: Text('添加選中格位 (${_pendingSelections.length})'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[600],
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: 12),
        OutlinedButton.icon(
          onPressed: () {
            setState(() {
              _pendingSelections.clear();
            });
          },
          icon: const Icon(Icons.clear),
          label: const Text('清除選擇'),
        ),
      ],
    );
  }

  void _addSelectedGrids() {
    if (_pendingSelections.isNotEmpty) {
      final selectedCount = _pendingSelections.length;
      final newSelection = List<String>.from(widget.selectedGridIds);
      newSelection.addAll(_pendingSelections);
      widget.onChanged(newSelection);
      
      setState(() {
        _pendingSelections.clear();
      });
      
      // 顯示成功訊息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('成功添加 $selectedCount 個格位'),
          backgroundColor: Colors.green[600],
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _selectAllAvailableGrids(List<GridEntity> availableGrids) {
    setState(() {
      _pendingSelections.clear();
      _pendingSelections.addAll(availableGrids.map((grid) => grid.id));
    });
  }

  void _removeAllSelectedGrids() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('確認移除'),
          content: const Text('確定要移除所有已選格位嗎？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                widget.onChanged([]);
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('已移除所有格位'),
                    backgroundColor: Colors.orange,
                    duration: Duration(seconds: 2),
                  ),
                );
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('確認移除'),
            ),
          ],
        );
      },
    );
  }

  Color _getGridSizeColor(String size) {
    switch (size.toUpperCase()) {
      case 'S':
        return Colors.blue.shade100;
      case 'M':
        return Colors.green.shade100;
      case 'L':
        return Colors.orange.shade100;
      default:
        return Colors.grey.shade100;
    }
  }
}