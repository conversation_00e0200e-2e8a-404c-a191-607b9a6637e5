import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:grid_pos/features/store_mgmt/domain/entities/store_entity.dart';
import 'package:grid_pos/features/store_mgmt/presentation/providers/store_crud_providers.dart'
    as store_crud;
import 'package:grid_pos/core/utils/logger.dart'; // Import Logger

/// Provider for shared preferences instance
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError(
    'SharedPreferences should be overridden in ProviderScope',
  );
});

/// Key for storing the selected store ID in shared preferences
const String kSelectedStoreIdKey = 'selected_store_id';

/// Default store ID when none is selected or available
const String kDefaultStoreId =
    'store1'; // Or define a more explicit "no selection" ID if your business logic allows

/// Constant for representing that no valid store is selected
const String kNoValidStoreSelectedId = 'NO_STORE_SELECTED';

/// Provider for the currently selected store ID
/// This is a global state that will be used by other providers
final selectedStoreIdProvider = StateNotifierProvider<StoreIdNotifier, String>((
  ref,
) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return StoreIdNotifier(prefs, ref);
});

/// Notifier for managing the selected store ID with persistence
class StoreIdNotifier extends StateNotifier<String> {
  final SharedPreferences _prefs;
  final Ref _ref;
  bool _isInitialized = false; // 新增標誌位，表示監聽器是否已完成首次檢查

  /// Creates a store ID notifier
  StoreIdNotifier(this._prefs, this._ref)
    : super(_prefs.getString(kSelectedStoreIdKey) ?? kDefaultStoreId) {
    // 延遲執行，確保 Notifier 構造完成
    Future.microtask(() {
      _listenToAvailableStores();
    });
  }

  void _listenToAvailableStores() {
    _ref.listen<
      AsyncValue<List<StoreEntity>>
    >(store_crud.availableStoresProvider, (previous, next) {
      next.whenData((stores) {
        Logger.debug(
          'StoreIdNotifier._listenToAvailableStores: Stores updated (count: ${stores.length}). Current Notifier State: "$state", Initialized: $_isInitialized',
        );
        String newEffectiveStoreId = state; // 假設當前 state 是有效的起點

        if (stores.isNotEmpty) {
          final currentStateIsValidInNewList = stores.any(
            (store) => store.id == state,
          );
          if (!currentStateIsValidInNewList) {
            // 如果當前 state 不在新的店鋪列表中，選擇第一個作為新的有效 ID
            newEffectiveStoreId = stores.first.id;
            Logger.info(
              'StoreIdNotifier._listenToAvailableStores: State "$state" is invalid or not in new list. Auto-selecting: "$newEffectiveStoreId"',
            );
          } else {
            // 當前 state 在新列表中有效
            Logger.debug(
              'StoreIdNotifier._listenToAvailableStores: State "$state" is valid and present in stores.',
            );
            newEffectiveStoreId = state; // 維持當前 state
          }
        } else {
          // 如果店鋪列表為空
          Logger.info(
            'StoreIdNotifier._listenToAvailableStores: Store list is empty. Current state: "$state"',
          );
          newEffectiveStoreId = kNoValidStoreSelectedId;
          if (state != kNoValidStoreSelectedId) {
            Logger.info(
              'StoreIdNotifier._listenToAvailableStores: Resetting to kNoValidStoreSelectedId.',
            );
          }
        }

        // 只有當計算出的有效 newEffectiveStoreId 與當前 state 不同時，才更新
        // 或者在首次初始化完成時，強制更新一次以確保同步
        if (newEffectiveStoreId != state || !_isInitialized) {
          _updateStateAndPrefs(
            newEffectiveStoreId,
            fromListener: true,
            isInitialization: !_isInitialized,
          );
        }
        _isInitialized = true; // 標記初始化完成
      });
    }, fireImmediately: true);
  }

  // Helper method to update state and SharedPreferences
  Future<void> _updateStateAndPrefs(
    String newStoreId, {
    bool fromListener = false,
    bool isInitialization = false,
  }) async {
    Logger.debug(
      'StoreIdNotifier._updateStateAndPrefs: Attempting to update to "$newStoreId". From listener: $fromListener, Is init: $isInitialization, Current state: "$state"',
    );

    // 核心改動：只有當 newStoreId 與當前 state 不同時才真正更新 state 和 SharedPreferences
    if (state == newStoreId && !isInitialization) {
      // 如果是初始化，即使值相同也可能需要寫入 SharedPreferences
      Logger.debug(
        'StoreIdNotifier._updateStateAndPrefs: newStoreId is same as current state and not initialization. No actual state/prefs update needed.',
      );
      // 如果需要確保 SharedPreferences 與 state 一致，即使值相同也可以在這裡寫一次
      // 但如果 _prefs.getString(kSelectedStoreIdKey) 已經是 newStoreId，則無需重寫
      if (_prefs.getString(kSelectedStoreIdKey) != newStoreId &&
          newStoreId != kNoValidStoreSelectedId) {
        await _prefs.setString(kSelectedStoreIdKey, newStoreId);
        Logger.debug(
          'StoreIdNotifier._updateStateAndPrefs: Persisted $newStoreId to SharedPreferences (value was different).',
        );
      }
      return;
    }

    if (newStoreId != kNoValidStoreSelectedId) {
      // Only persist valid store IDs, not the special "no store selected" value
      await _prefs.setString(kSelectedStoreIdKey, newStoreId);
      Logger.debug(
        'StoreIdNotifier._updateStateAndPrefs: Persisted $newStoreId to SharedPreferences.',
      );
    } else {
      // If we're setting to "no store selected", remove any previously saved value
      await _prefs.remove(kSelectedStoreIdKey);
      Logger.debug(
        'StoreIdNotifier._updateStateAndPrefs: Removed store ID from SharedPreferences.',
      );
    }

    if (mounted) {
      if (state != newStoreId) {
        // 再次確認，只有不同才更新
        Logger.debug(
          'StoreIdNotifier._updateStateAndPrefs: State changing from "$state" to "$newStoreId"',
        );
        state = newStoreId;
      } else {
        Logger.debug(
          'StoreIdNotifier._updateStateAndPrefs: State already "$newStoreId", not changing (but prefs might have been updated if isInitialization).',
        );
      }
    } else {
      Logger.warning(
        'StoreIdNotifier._updateStateAndPrefs: Notifier not mounted, cannot set state to "$newStoreId"',
      );
    }
  }

  /// Change the selected store ID and persist it
  Future<void> selectStore(String storeId) async {
    Logger.debug(
      'StoreIdNotifier.selectStore: User explicitly selected "$storeId". Current state: "$state"',
    );
    if (state == storeId) {
      Logger.debug(
        'StoreIdNotifier.selectStore: New storeId is the same as current. No update needed.',
      );
      return;
    }
    // User's explicit selection takes priority
    await _updateStateAndPrefs(storeId);
  }
}

/// Provider that maps StoreEntity list to StoreInfo list for backwards compatibility
final storeInfoListProvider = Provider<List<StoreInfo>>((ref) {
  final storesAsync = ref.watch(store_crud.availableStoresProvider);

  return storesAsync.whenOrNull(
        data:
            (stores) =>
                stores
                    .map(
                      (store) => StoreInfo(
                        id: store.id,
                        name: store.name,
                        address: store.address,
                      ),
                    )
                    .toList(),
      ) ??
      [
        // Default mock data if stores are loading or there's an error
        // Consider if this mock data is still needed if StoreIdNotifier handles empty lists
        StoreInfo(
          id: kDefaultStoreId,
          name: 'Main Store (Default)',
          address: 'Central, Hong Kong',
        ),
      ];
});

/// Legacy provider for available stores - redirects to the new storeInfoListProvider
/// This maintains compatibility with existing code that expects a List<StoreInfo>
@Deprecated(
  'Use storeInfoListProvider or store_crud.availableStoresProvider directly',
)
final availableStoresProvider = Provider<List<StoreInfo>>((ref) {
  return ref.watch(storeInfoListProvider);
});

/// Store information model
class StoreInfo {
  /// Store ID
  final String id;

  /// Store name
  final String name;

  /// Store address
  final String address;

  /// Creates store info
  StoreInfo({required this.id, required this.name, required this.address});
}
