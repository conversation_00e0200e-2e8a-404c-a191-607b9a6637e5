import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/tenant_mgmt/data/datasources/grid_remote_ds.dart';
import 'package:grid_pos/features/tenant_mgmt/data/repositories/grid_repository_impl.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/grid_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/repositories/grid_repository.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/tenant_providers.dart';

/// Provider for the grid remote data source
final gridRemoteDataSourceProvider = Provider<GridRemoteDataSource>((ref) {
  return GridRemoteDataSourceImpl();
});

/// Provider for the grid repository
final gridRepositoryProvider = Provider<GridRepository>((ref) {
  final gridRemoteDS = ref.watch(gridRemoteDataSourceProvider);
  return GridRepositoryImpl(gridRemoteDS: gridRemoteDS);
});

/// Provider for all grids in a specific store.
/// This provider will be kept alive as long as it's watched.
final gridsByStoreIdProvider = StreamProvider.family<List<GridEntity>, String>((
  ref,
  storeId,
) {
  final repository = ref.watch(gridRepositoryProvider);
  Logger.debug(
    '[gridsByStoreIdProvider] Creating/Fetching for storeId: "$storeId"',
  );

  // 檢查 storeId 是否有效
  if (storeId == kNoValidStoreSelectedId || storeId.isEmpty) {
    Logger.warning(
      '[gridsByStoreIdProvider] Invalid storeId ("$storeId") provided. Returning empty stream or error.',
    );
    return Stream.error(
      Exception('Invalid store ID provided to gridsByStoreIdProvider.'),
    );
  }
  return repository.watchGrids(storeId);
});

/// Provider for all grids in the *currently selected* store.
/// This provider directly watches `gridsByStoreIdProvider` with the selected store ID.
final currentStoreGridsProvider = StreamProvider<List<GridEntity>>((ref) {
  final selectedStoreId = ref.watch(selectedStoreIdProvider);
  Logger.debug(
    '[currentStoreGridsProvider] Evaluating for selectedStoreId: "$selectedStoreId"',
  );

  if (selectedStoreId == kNoValidStoreSelectedId || selectedStoreId.isEmpty) {
    Logger.warning(
      '[currentStoreGridsProvider] No valid storeId ("$selectedStoreId") selected. Returning empty list or error stream.',
    );
    // 返回一個空的數據流或錯誤流，而不是讓依賴它的 Provider 出錯
    return Stream.value(
      [],
    ); // 或者 Stream.error(Exception('No valid store selected'));
  }

  // 直接 watch family provider 的 AsyncValue
  // Riverpod 會處理 loading/error 狀態的傳播
  final gridsAsyncValue = ref.watch(gridsByStoreIdProvider(selectedStoreId));

  // 將 AsyncValue<List<GridEntity>> 轉換為 Stream<List<GridEntity>>
  // 這一步是為了保持 StreamProvider 的簽名，但更簡單的方式是讓下游直接 watch AsyncValue
  return gridsAsyncValue.when(
    data: (data) {
      Logger.debug(
        '[currentStoreGridsProvider] Data received for "$selectedStoreId": ${data.length} grids.',
      );
      return Stream.value(data);
    },
    loading: () {
      Logger.debug(
        '[currentStoreGridsProvider] Upstream gridsByStoreIdProvider is loading for "$selectedStoreId".',
      );
      // 返回一個不會發出數據的 Stream，讓 StreamProvider 保持 loading 狀態
      return Stream.empty(); // 或者使用 Completer 來控制
    },
    error: (err, stack) {
      Logger.error(
        '[currentStoreGridsProvider] Error from upstream for "$selectedStoreId": $err',
        err,
        stack,
      );
      return Stream.error(err, stack);
    },
  );
});

/// Provider for available grids in a store (not assigned to any tenant)
/// Usage: ref.watch(availableGridsProvider('storeId'))
final availableGridsProvider = StreamProvider.family<List<GridEntity>, String>((
  ref,
  storeId,
) {
  final repository = ref.watch(gridRepositoryProvider);
  return repository.watchAvailableGrids(storeId);
});

/// Provider for available grids in the currently selected store
final currentStoreAvailableGridsProvider = StreamProvider<List<GridEntity>>((
  ref,
) {
  final storeId = ref.watch(selectedStoreIdProvider);

  if (storeId == kNoValidStoreSelectedId || storeId.isEmpty) {
    Logger.warning(
      '[currentStoreAvailableGridsProvider] No valid storeId ("$storeId") selected. Returning error stream.',
    );
    return Stream.error(
      Exception('No valid store selected to fetch available grids.'),
    );
  }

  return ref.watch(availableGridsProvider(storeId).stream);
});

/// Provider for grids assigned to a specific tenant
/// Usage: ref.watch(tenantGridsProvider({'storeId': 'storeId', 'tenantId': 'tenantId'}))
final tenantGridsProvider = StreamProvider.family<
  List<GridEntity>,
  ({String storeId, String tenantId})
>((ref, params) {
  final repository = ref.watch(gridRepositoryProvider);
  return repository.watchGridsByTenant(params.storeId, params.tenantId);
});

/// Provider for grids by size
/// Usage: ref.watch(gridsBySizeProvider({'storeId': 'storeId', 'size': 'S'}))
final gridsBySizeProvider =
    StreamProvider.family<List<GridEntity>, ({String storeId, String size})>((
      ref,
      params,
    ) {
      final repository = ref.watch(gridRepositoryProvider);
      return repository.watchGridsBySize(params.storeId, params.size);
    });

/// Provider for a single grid
/// Usage: ref.watch(gridProvider({'storeId': 'storeId', 'gridId': 'gridId'}))
final gridProvider =
    StreamProvider.family<GridEntity?, ({String storeId, String gridId})>((
      ref,
      params,
    ) {
      final repository = ref.watch(gridRepositoryProvider);
      return repository.watchGrid(params.storeId, params.gridId);
    });

/// Provider for grids of the selected tenant
final selectedTenantGridsProvider = StreamProvider<List<GridEntity>>((ref) {
  final selectedTenantId = ref.watch(selectedTenantIdProvider);
  final currentStoreId = ref.watch(selectedStoreIdProvider);

  if (selectedTenantId == null) {
    return Stream.value([]);
  }

  return ref.watch(
    tenantGridsProvider((
      storeId: currentStoreId,
      tenantId: selectedTenantId,
    )).stream,
  );
});

/// State for grid CRUD operations
class GridCrudState {
  /// The grid being created or edited
  final GridEntity? grid;

  /// Whether an operation is in progress
  final bool isLoading;

  /// Error message if any
  final String? errorMessage;

  /// Success message after an operation completes
  final String? successMessage;

  /// ID of the grid being processed
  final String? processingGridId;

  /// Constructor
  const GridCrudState({
    this.grid,
    this.isLoading = false,
    this.errorMessage,
    this.successMessage,
    this.processingGridId,
  });

  /// Create a copy with new values
  GridCrudState copyWith({
    GridEntity? grid,
    bool? isLoading,
    String? errorMessage,
    String? successMessage,
    String? processingGridId,
    bool clearGrid = false,
    bool clearErrorMessage = false,
    bool clearSuccessMessage = false,
    bool clearProcessingGridId = false,
  }) {
    return GridCrudState(
      grid: clearGrid ? null : grid ?? this.grid,
      isLoading: isLoading ?? this.isLoading,
      errorMessage:
          clearErrorMessage ? null : errorMessage ?? this.errorMessage,
      successMessage:
          clearSuccessMessage ? null : successMessage ?? this.successMessage,
      processingGridId:
          clearProcessingGridId
              ? null
              : processingGridId ?? this.processingGridId,
    );
  }
}

/// Notifier for grid CRUD operations
class GridCrudNotifier extends StateNotifier<GridCrudState> {
  final GridRepository _repository;
  final Ref _ref; // For accessing other providers if needed

  /// Constructor
  GridCrudNotifier(this._repository, this._ref) : super(const GridCrudState());

  /// Create a new grid
  Future<void> createGrid(String storeId, GridEntity grid) async {
    try {
      state = state.copyWith(
        isLoading: true,
        clearErrorMessage: true,
        clearSuccessMessage: true,
        grid: grid,
      );

      final gridId = await _repository.addGrid(storeId, grid);

      state = state.copyWith(
        isLoading: false,
        clearGrid: true,
        successMessage: 'Grid created successfully',
        processingGridId: gridId,
      );
      _autoClearStateMessagesAfterDelay();

      // Clear the processing grid ID after a short delay
      await Future.delayed(const Duration(milliseconds: 300));
      state = state.copyWith(clearProcessingGridId: true);
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to create grid: ${error.toString()}',
        clearProcessingGridId: true,
      );
      _autoClearStateMessagesAfterDelay();
      rethrow;
    }
  }

  /// Update an existing grid
  Future<void> updateGrid(
    String storeId,
    String gridId,
    GridEntity grid,
  ) async {
    try {
      state = state.copyWith(
        isLoading: true,
        clearErrorMessage: true,
        clearSuccessMessage: true,
        grid: grid,
        processingGridId: gridId,
      );

      await _repository.updateGridDetails(storeId, gridId, grid);

      state = state.copyWith(
        isLoading: false,
        clearGrid: true,
        successMessage: 'Grid updated successfully',
        clearProcessingGridId: true,
      );
      _autoClearStateMessagesAfterDelay();
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to update grid: ${error.toString()}',
        clearProcessingGridId: true,
      );
      _autoClearStateMessagesAfterDelay();
      rethrow;
    }
  }

  /// Delete a grid
  Future<void> deleteGrid(String storeId, String gridId) async {
    try {
      state = state.copyWith(
        isLoading: true,
        clearErrorMessage: true,
        clearSuccessMessage: true,
        processingGridId: gridId,
      );

      // First get the grid to check if it has a tenant assigned
      final grid = await _repository.watchGrid(storeId, gridId).first;

      // Validation: Cannot delete a grid that is assigned to a tenant directly
      if (grid != null && grid.tenantId != null) {
        throw Exception(
          'Cannot delete grid that is assigned to a tenant. Please unassign the tenant first.',
        );
      }

      // Additional check: verify no tenants have this grid in their grids array
      final tenantRepository = _ref.read(tenantRepositoryProvider);
      final tenants = await tenantRepository.watchTenants(storeId).first;

      // Check if any tenant has this grid in their grids array
      final tenantsUsingGrid =
          tenants.where((tenant) => tenant.grids.contains(gridId)).toList();

      if (tenantsUsingGrid.isNotEmpty) {
        // Format tenant names for the error message
        final tenantNames = tenantsUsingGrid
            .map((t) => '"${t.name}"')
            .join(', ');
        throw Exception(
          'Cannot delete grid that is used by tenant(s): $tenantNames. Please unassign the grid first.',
        );
      }

      await _repository.deleteGrid(storeId, gridId);

      state = state.copyWith(
        isLoading: false,
        successMessage: 'Grid deleted successfully',
        clearProcessingGridId: true,
      );
      _autoClearStateMessagesAfterDelay();
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to delete grid: ${error.toString()}',
        clearProcessingGridId: true,
      );
      _autoClearStateMessagesAfterDelay();
      rethrow;
    }
  }

  void _autoClearStateMessagesAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        final currentState = state; // Capture the current state
        // Only reset state if current state has success or error message and is not loading
        if ((currentState.successMessage != null &&
                currentState.successMessage!.isNotEmpty) ||
            (currentState.errorMessage != null &&
                    currentState.errorMessage!.isNotEmpty) &&
                !currentState.isLoading) {
          state = state.copyWith(
            clearErrorMessage: true,
            clearSuccessMessage: true,
          );
        }
      }
    });
  }

  // The isGridCodeUnique method has been moved to a separate service provider
  // to avoid Riverpod provider modification during initialization

  /// Clear any error or success message
  void clearMessages() {
    state = state.copyWith(clearErrorMessage: true, clearSuccessMessage: true);
  }

  /// Set the grid for editing
  void setGridForEditing(GridEntity grid) {
    state = state.copyWith(grid: grid);
  }

  /// Clear the grid being edited
  void clearGrid() {
    state = state.copyWith(clearGrid: true);
  }
}

/// Provider for the GridCrudNotifier
final gridCrudNotifierProvider =
    StateNotifierProvider<GridCrudNotifier, GridCrudState>((ref) {
      final repository = ref.watch(gridRepositoryProvider);
      return GridCrudNotifier(repository, ref);
    });
