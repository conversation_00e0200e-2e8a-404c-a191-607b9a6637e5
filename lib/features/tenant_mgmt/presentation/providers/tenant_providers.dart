import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/tenant_mgmt/data/datasources/grid_assignment_service.dart';
import 'package:grid_pos/features/tenant_mgmt/data/datasources/tenant_remote_ds.dart';
import 'package:grid_pos/features/tenant_mgmt/data/datasources/tenant_user_service.dart';
import 'package:grid_pos/features/tenant_mgmt/data/repositories/tenant_repository_impl.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/tenant_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/repositories/tenant_repository.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart';
import 'package:grid_pos/core/utils/logger.dart';

/// Provider for the tenant remote data source
final tenantRemoteDataSourceProvider = Provider<TenantRemoteDataSource>((ref) {
  return TenantRemoteDataSourceImpl();
});

/// Provider for the grid assignment service
final gridAssignmentServiceProvider = Provider<GridAssignmentService>((ref) {
  return GridAssignmentService();
});

/// Provider for the tenant user service
final tenantUserServiceProvider = Provider<TenantUserService>((ref) {
  return TenantUserService();
});

/// Provider for the tenant repository
final tenantRepositoryProvider = Provider<TenantRepository>((ref) {
  final tenantRemoteDS = ref.watch(tenantRemoteDataSourceProvider);
  final gridAssignmentService = ref.watch(gridAssignmentServiceProvider);

  return TenantRepositoryImpl(tenantRemoteDS: tenantRemoteDS, gridAssignmentService: gridAssignmentService);
});

/// Provider for all tenants in a store
/// Usage: ref.watch(tenantsProvider('storeId'))
final tenantsProvider = StreamProvider.autoDispose.family<List<TenantEntity>, String>((ref, storeId) {
  final repository = ref.watch(tenantRepositoryProvider);
  Logger.debug('[tenantsProvider] Created new provider instance for storeId: "$storeId"');
  return repository.watchTenants(storeId);
});

/// Provider for all tenants in the currently selected store
final currentStoreTenantsProvider = StreamProvider<List<TenantEntity>>((ref) {
  final storeId = ref.watch(selectedStoreIdProvider);

  // Add logging to track provider evaluation
  Logger.debug('[currentStoreTenantsProvider] Evaluating for storeId: "$storeId"');

  // Check if the storeId is valid for fetching tenants
  if (storeId == kNoValidStoreSelectedId || storeId.isEmpty) {
    Logger.warning('[currentStoreTenantsProvider] No valid storeId ("$storeId") selected. Returning error stream.');
    return Stream.error(Exception('No valid store selected to fetch tenants.'));
  }

  // Use better error handling similar to grid providers
  Logger.debug('[currentStoreTenantsProvider] storeId "$storeId" is valid. Watching tenantsProvider($storeId).stream');
  return ref
      .watch(tenantsProvider(storeId).stream)
      .map((data) {
        Logger.debug('[currentStoreTenantsProvider] Data for "$storeId": ${data.length} tenants.');
        return data;
      })
      .handleError((error, stackTrace) {
        Logger.error('[currentStoreTenantsProvider] Error for "$storeId": $error', error, stackTrace);
        throw error; // Rethrow the error so the stream provider can handle it
      });
});

/// Provider for active tenants in a store
/// Usage: ref.watch(activeTenantProvider('storeId'))
final activeTenantProvider = StreamProvider.autoDispose.family<List<TenantEntity>, String>((ref, storeId) {
  final repository = ref.watch(tenantRepositoryProvider);
  return repository.watchTenantsByStatus(storeId, true);
});

/// Provider for active tenants in the currently selected store
final currentStoreActiveTenantProvider = StreamProvider<List<TenantEntity>>((ref) {
  final storeId = ref.watch(selectedStoreIdProvider);
  Logger.debug('[currentStoreActiveTenantProvider] Evaluating for storeId: "$storeId"');

  if (storeId == kNoValidStoreSelectedId || storeId.isEmpty) {
    Logger.warning('[currentStoreActiveTenantProvider] No valid storeId ("$storeId") selected. Returning error stream.');
    return Stream.error(Exception('No valid store selected to fetch active tenants.'));
  }

  Logger.debug('[currentStoreActiveTenantProvider] storeId "$storeId" is valid. Watching activeTenantProvider($storeId).stream');
  return ref
      .watch(activeTenantProvider(storeId).stream)
      .map((data) {
        Logger.debug('[currentStoreActiveTenantProvider] Data for "$storeId": ${data.length} active tenants.');
        return data;
      })
      .handleError((error, stackTrace) {
        Logger.error('[currentStoreActiveTenantProvider] Error for "$storeId": $error', error, stackTrace);
        throw error;
      });
});

/// Provider for a single tenant
/// Usage: ref.watch(tenantProvider({'storeId': 'storeId', 'tenantId': 'tenantId'}))
final tenantProvider = StreamProvider.autoDispose.family<TenantEntity?, ({String storeId, String tenantId})>((ref, params) {
  final repository = ref.watch(tenantRepositoryProvider);
  return repository.watchTenant(params.storeId, params.tenantId);
});

/// Provider for the currently selected tenant ID (null if none selected)
final selectedTenantIdProvider = StateProvider<String?>((ref) => null);

/// Provider for the currently selected tenant
/// Depends on the selectedTenantIdProvider and tenantProvider
final selectedTenantProvider = StreamProvider<TenantEntity?>((ref) {
  final selectedTenantId = ref.watch(selectedTenantIdProvider);
  final currentStoreId = ref.watch(selectedStoreIdProvider);

  if (selectedTenantId == null) {
    return Stream.value(null);
  }

  return ref.watch(tenantProvider((storeId: currentStoreId, tenantId: selectedTenantId)).stream);
});

/// Provider for syncing tenant-grid relationships
/// Usage: ref.read(syncTenantsAndGridsProvider(storeId).future)
final syncTenantsAndGridsProvider = FutureProvider.family<void, String>((ref, storeId) async {
  final repository = ref.watch(tenantRepositoryProvider);
  return repository.syncTenantsAndGrids(storeId);
});
