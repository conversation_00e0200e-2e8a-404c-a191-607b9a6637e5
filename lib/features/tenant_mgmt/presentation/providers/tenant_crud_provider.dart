import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/tenant_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/repositories/tenant_repository.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/tenant_providers.dart';

/// State for tenant CRUD operations
class TenantCrudState {
  /// The tenant being created or edited
  final TenantEntity? tenant;

  /// Whether an operation is in progress
  final bool isLoading;

  /// Error message if any
  final String? errorMessage;

  /// Success message after an operation completes
  final String? successMessage;

  /// ID of the tenant being processed
  final String? processingTenantId;

  /// Constructor
  const TenantCrudState({
    this.tenant,
    this.isLoading = false,
    this.errorMessage,
    this.successMessage,
    this.processingTenantId,
  });

  /// Create a copy with new values
  TenantCrudState copyWith({
    TenantEntity? tenant,
    bool? isLoading,
    String? errorMessage,
    String? successMessage,
    String? processingTenantId,
    bool clearTenant = false,
    bool clearErrorMessage = false,
    bool clearSuccessMessage = false,
    bool clearProcessingTenantId = false,
  }) {
    return TenantCrudState(
      tenant: clearTenant ? null : tenant ?? this.tenant,
      isLoading: isLoading ?? this.isLoading,
      errorMessage:
          clearErrorMessage ? null : errorMessage ?? this.errorMessage,
      successMessage:
          clearSuccessMessage ? null : successMessage ?? this.successMessage,
      processingTenantId:
          clearProcessingTenantId
              ? null
              : processingTenantId ?? this.processingTenantId,
    );
  }
}

/// Notifier for tenant CRUD operations
class TenantCrudNotifier extends StateNotifier<TenantCrudState> {
  final TenantRepository _repository;
  final Ref _ref; // For accessing other providers if needed

  /// Constructor
  TenantCrudNotifier(this._repository, this._ref)
    : super(const TenantCrudState());

  /// Create a new tenant
  Future<void> createTenant(String storeId, TenantEntity tenant) async {
    try {
      state = state.copyWith(
        isLoading: true,
        clearErrorMessage: true,
        clearSuccessMessage: true,
        tenant: tenant,
      );

      final tenantId = await _repository.addTenant(storeId, tenant);

      state = state.copyWith(
        isLoading: false,
        clearTenant: true,
        successMessage: 'Tenant created successfully',
        processingTenantId: tenantId,
      );
      _autoClearStateMessagesAfterDelay();
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to create tenant: ${error.toString()}',
      );
      _autoClearStateMessagesAfterDelay();
      rethrow;
    }
  }

  /// Create a new tenant with a user account
  Future<void> createTenantWithUser(
    String storeId,
    TenantEntity tenant, {
    required String email,
    required String password,
  }) async {
    try {
      state = state.copyWith(
        isLoading: true,
        clearErrorMessage: true,
        clearSuccessMessage: true,
        tenant: tenant,
      );

      // 1. Create tenant first
      final tenantId = await _repository.addTenant(storeId, tenant);

      // 2. Then create user account with Firebase Auth
      try {
        final tenantUserService = _ref.read(tenantUserServiceProvider);

        // Create actual Firebase Auth account for the tenant
        // The service handles preserving admin session
        await tenantUserService.createTenantUser(
          email: email,
          password: password,
          displayName: tenant.name,
          storeId: storeId,
          tenantId: tenantId,
        );

        state = state.copyWith(
          isLoading: false,
          clearTenant: true,
          successMessage: 'Tenant and user account created successfully',
          processingTenantId: tenantId,
        );
      } catch (userError) {
        // If user creation fails, we still have created the tenant
        state = state.copyWith(
          isLoading: false,
          clearTenant: true,
          successMessage:
              'Tenant created successfully, but user account creation failed: ${userError.toString()}',
          processingTenantId: tenantId,
        );
      }

      _autoClearStateMessagesAfterDelay();
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to create tenant: ${error.toString()}',
      );
      _autoClearStateMessagesAfterDelay();
      rethrow;
    }
  }

  /// Update an existing tenant
  Future<void> updateTenant(
    String storeId,
    String tenantId,
    TenantEntity tenant,
  ) async {
    try {
      state = state.copyWith(
        isLoading: true,
        clearErrorMessage: true,
        clearSuccessMessage: true,
        tenant: tenant,
        processingTenantId: tenantId,
      );

      await _repository.updateTenant(storeId, tenantId, tenant);

      state = state.copyWith(
        isLoading: false,
        clearTenant: true,
        successMessage: 'Tenant updated successfully',
        clearProcessingTenantId: true,
      );
      _autoClearStateMessagesAfterDelay();
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to update tenant: ${error.toString()}',
        clearProcessingTenantId: true,
      );
      _autoClearStateMessagesAfterDelay();
      rethrow;
    }
  }

  /// Delete a tenant
  Future<void> deleteTenant(String storeId, String tenantId) async {
    try {
      state = state.copyWith(
        isLoading: true,
        clearErrorMessage: true,
        clearSuccessMessage: true,
        processingTenantId: tenantId,
      );

      // First get the tenant to check if it has any grids assigned
      final tenant = await _repository.watchTenant(storeId, tenantId).first;

      // Validation: Cannot delete a tenant that has grids assigned
      if (tenant != null && tenant.grids.isNotEmpty) {
        throw Exception(
          'Cannot delete tenant that has grids assigned. Please unassign all grids first.',
        );
      }

      // Find and delete the associated user account if any
      try {
        final tenantUserService = _ref.read(tenantUserServiceProvider);
        final tenantUser = await tenantUserService.findUserByTenantId(tenantId);

        if (tenantUser != null) {
          // Delete the user account linked to this tenant
          await tenantUserService.deleteTenantUser(tenantUser.uid);
        }
      } catch (userError) {
        // Log the error but continue with tenant deletion
        // This ensures tenant deletion proceeds even if user deletion fails
        // We don't want to block tenant deletion due to auth issues
        state = state.copyWith(
          errorMessage:
              'Warning: Failed to delete associated user account: ${userError.toString()}. Continuing with tenant deletion.',
        );
      }

      // Delete the tenant
      await _repository.deleteTenant(storeId, tenantId);

      state = state.copyWith(
        isLoading: false,
        successMessage:
            'Tenant and associated user account deleted successfully',
        clearProcessingTenantId: true,
      );
      _autoClearStateMessagesAfterDelay();
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to delete tenant: ${error.toString()}',
        clearProcessingTenantId: true,
      );
      _autoClearStateMessagesAfterDelay();
      rethrow;
    }
  }

  void _autoClearStateMessagesAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        final currentState = state; // Capture the current state
        // Only reset state if current state has success or error message and is not loading
        if ((currentState.successMessage != null &&
                currentState.successMessage!.isNotEmpty) ||
            (currentState.errorMessage != null &&
                    currentState.errorMessage!.isNotEmpty) &&
                !currentState.isLoading) {
          state = state.copyWith(
            clearErrorMessage: true,
            clearSuccessMessage: true,
          );
        }
      }
    });
  }

  /// Check if tenant dates (contract start/end) are valid
  bool validateTenantDates(DateTime start, DateTime end) {
    return start.isBefore(end);
  }

  /// Clear any error or success message
  void clearMessages() {
    state = state.copyWith(clearErrorMessage: true, clearSuccessMessage: true);
  }

  /// Set the tenant for editing
  void setTenantForEditing(TenantEntity tenant) {
    state = state.copyWith(tenant: tenant);
  }

  /// Clear the tenant being edited
  void clearTenant() {
    state = state.copyWith(clearTenant: true);
  }
}

/// Provider for the TenantCrudNotifier
final tenantCrudNotifierProvider =
    StateNotifierProvider<TenantCrudNotifier, TenantCrudState>((ref) {
      final repository = ref.watch(tenantRepositoryProvider);
      return TenantCrudNotifier(repository, ref);
    });
