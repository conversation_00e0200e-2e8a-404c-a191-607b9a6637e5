import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:grid_pos/core/utils/logger.dart';

/// Service to handle grid assignment operations with transactions
class GridAssignmentService {
  final FirebaseFirestore _firestore;

  /// Constructor
  GridAssignmentService({FirebaseFirestore? firestore}) : _firestore = firestore ?? FirebaseFirestore.instance;

  /// Assign a grid to a tenant using a Firebase transaction
  Future<void> assignGridToTenant(String storeId, String gridId, String tenantId) async {
    try {
      await _firestore.runTransaction((transaction) async {
        // Get grid and tenant reference
        final gridRef = _firestore.collection('stores').doc(storeId).collection('grids').doc(gridId);

        final tenantRef = _firestore.collection('stores').doc(storeId).collection('tenants').doc(tenantId);

        // Read current data
        final gridSnapshot = await transaction.get(gridRef);
        final tenantSnapshot = await transaction.get(tenantRef);

        if (!gridSnapshot.exists) {
          throw Exception('Grid not found');
        }

        if (!tenantSnapshot.exists) {
          throw Exception('Tenant not found');
        }

        // Update the grid with new tenant ID
        transaction.update(gridRef, {'tenantId': tenantId, 'updatedAt': FieldValue.serverTimestamp()});

        // Get current grids array from tenant
        final grids = List<String>.from(tenantSnapshot.data()?['grids'] ?? []);

        // Add grid ID to tenant's grids array if not already present
        if (!grids.contains(gridId)) {
          grids.add(gridId);
          transaction.update(tenantRef, {'grids': grids, 'updatedAt': FieldValue.serverTimestamp()});
        }
      });

      Logger.info('Successfully assigned grid $gridId to tenant $tenantId');
    } catch (e) {
      Logger.error('Error assigning grid: ${e.toString()}');
      rethrow;
    }
  }

  /// Unassign a grid from a tenant using a Firebase transaction
  Future<void> unassignGridFromTenant(String storeId, String gridId, String tenantId) async {
    try {
      await _firestore.runTransaction((transaction) async {
        // Get grid and tenant reference
        final gridRef = _firestore.collection('stores').doc(storeId).collection('grids').doc(gridId);

        final tenantRef = _firestore.collection('stores').doc(storeId).collection('tenants').doc(tenantId);

        // Read current data
        final gridSnapshot = await transaction.get(gridRef);
        final tenantSnapshot = await transaction.get(tenantRef);

        if (!gridSnapshot.exists || !tenantSnapshot.exists) {
          return; // If either doesn't exist, nothing to unassign
        }

        // Verify the grid is actually assigned to this tenant
        final currentTenantId = gridSnapshot.data()?['tenantId'];
        if (currentTenantId != tenantId) {
          return; // Grid isn't assigned to this tenant, no action needed
        }

        // Update grid to remove tenant assignment
        transaction.update(gridRef, {'tenantId': null, 'updatedAt': FieldValue.serverTimestamp()});

        // Get current grids array from tenant
        final grids = List<String>.from(tenantSnapshot.data()?['grids'] ?? []);

        // Remove grid ID from tenant's grids array
        if (grids.contains(gridId)) {
          grids.remove(gridId);
          transaction.update(tenantRef, {'grids': grids, 'updatedAt': FieldValue.serverTimestamp()});
        }
      });

      Logger.info('Successfully unassigned grid $gridId from tenant $tenantId');
    } catch (e) {
      Logger.error('Error unassigning grid: ${e.toString()}');
      rethrow;
    }
  }

  /// Bulk assign multiple grids to a tenant
  Future<void> bulkAssignGridsToTenant(String storeId, List<String> gridIds, String tenantId) async {
    // Process each grid assignment in sequence
    for (final gridId in gridIds) {
      await assignGridToTenant(storeId, gridId, tenantId);
    }
  }

  /// Bulk unassign multiple grids from a tenant
  Future<void> bulkUnassignGridsFromTenant(String storeId, List<String> gridIds, String tenantId) async {
    // Process each grid unassignment in sequence
    for (final gridId in gridIds) {
      await unassignGridFromTenant(storeId, gridId, tenantId);
    }
  }
}
