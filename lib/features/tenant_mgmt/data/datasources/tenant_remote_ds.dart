import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:grid_pos/features/tenant_mgmt/data/models/tenant_dto.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/tenant_entity.dart';
import 'package:grid_pos/core/utils/logger.dart';

/// Interface for tenant remote data source operations
abstract class TenantRemoteDataSource {
  /// Get a tenant by ID
  Future<TenantEntity?> getTenant(String storeId, String tenantId);

  /// Stream a single tenant's data
  Stream<TenantEntity?> watchTenant(String storeId, String tenantId);

  /// Stream all tenants for a store
  Stream<List<TenantEntity>> watchTenants(String storeId);

  /// Stream tenants filtered by active status
  Stream<List<TenantEntity>> watchTenantsByStatus(String storeId, bool active);

  /// Create a new tenant
  Future<String> createTenant(String storeId, TenantEntity tenant);

  /// Update an existing tenant
  Future<void> updateTenant(String storeId, String tenantId, TenantEntity tenant);

  /// Delete a tenant
  Future<void> deleteTenant(String storeId, String tenantId);

  /// Search tenants by name
  Stream<List<TenantEntity>> searchTenantsByName(String storeId, String query);

  /// Get collection reference for tenants
  CollectionReference<Map<String, dynamic>> tenantsCollectionRef(String storeId);

  /// Sync tenant-grid relationships for a store
  Future<void> syncTenantsAndGrids(String storeId);
}

/// Implementation of TenantRemoteDataSource with Firestore
class TenantRemoteDataSourceImpl implements TenantRemoteDataSource {
  final FirebaseFirestore _firestore;

  /// Constructor
  TenantRemoteDataSourceImpl({FirebaseFirestore? firestore}) : _firestore = firestore ?? FirebaseFirestore.instance;

  @override
  CollectionReference<Map<String, dynamic>> tenantsCollectionRef(String storeId) {
    return _firestore.collection('stores').doc(storeId).collection('tenants');
  }

  @override
  Future<TenantEntity?> getTenant(String storeId, String tenantId) async {
    final docSnapshot = await tenantsCollectionRef(storeId).doc(tenantId).get();

    if (!docSnapshot.exists) {
      return null;
    }

    return TenantDto.fromFirestore(docSnapshot);
  }

  @override
  Stream<TenantEntity?> watchTenant(String storeId, String tenantId) {
    return tenantsCollectionRef(storeId).doc(tenantId).snapshots().map((snapshot) {
      if (!snapshot.exists) {
        return null;
      }

      return TenantDto.fromFirestore(snapshot);
    });
  }

  @override
  Stream<List<TenantEntity>> watchTenants(String storeId) {
    return tenantsCollectionRef(storeId).orderBy('name').snapshots().map((snapshot) {
      return snapshot.docs.map((doc) => TenantDto.fromFirestore(doc)).toList();
    });
  }

  @override
  Stream<List<TenantEntity>> watchTenantsByStatus(String storeId, bool active) {
    return tenantsCollectionRef(storeId).where('active', isEqualTo: active).orderBy('name').snapshots().map((snapshot) {
      return snapshot.docs.map((doc) => TenantDto.fromFirestore(doc)).toList();
    });
  }

  @override
  Future<String> createTenant(String storeId, TenantEntity tenant) async {
    final docRef = tenantsCollectionRef(storeId).doc();
    final tenantId = docRef.id;

    // Create a tenant with a new ID
    final tenantWithId = tenant.copyWith(id: tenantId);

    Logger.debug('[TenantRemoteDS] Creating tenant in storeId: $storeId, with id: $tenantId and grid IDs: ${tenantWithId.grids}');

    // Run a transaction to create the tenant and update all related grids
    await _firestore.runTransaction((transaction) async {
      // Create the tenant document
      transaction.set(docRef, TenantDto.toFirestore(tenantWithId));
      Logger.debug('[TenantRemoteDS] Transaction setting tenant document: $tenantId');

      // Update each grid that is assigned to this tenant
      for (final gridId in tenantWithId.grids) {
        final gridDocRef = _firestore.collection('stores').doc(storeId).collection('grids').doc(gridId);
        Logger.debug('[TenantRemoteDS] Transaction updating grid: $gridId to set tenantId: $tenantId');
        transaction.update(gridDocRef, {'tenantId': tenantId, 'updatedAt': FieldValue.serverTimestamp()});
      }
    });

    Logger.debug('[TenantRemoteDS] Successfully created tenant: $tenantId');
    return tenantId;
  }

  @override
  Future<void> updateTenant(String storeId, String tenantId, TenantEntity tenant) async {
    // Ensure the tenant ID matches the document ID
    final tenantWithCorrectId = tenant.copyWith(id: tenantId);

    Logger.debug('[TenantRemoteDS] Updating tenant in storeId: $storeId, id: $tenantId with grid IDs: ${tenantWithCorrectId.grids}');

    // Run a transaction to update both the tenant and all related grids
    await _firestore.runTransaction((transaction) async {
      final tenantDocRef = tenantsCollectionRef(storeId).doc(tenantId);
      final tenantSnapshot = await transaction.get(tenantDocRef);

      if (!tenantSnapshot.exists) {
        Logger.error('[TenantRemoteDS] Tenant not found: $tenantId');
        throw Exception('Tenant not found: $tenantId');
      }

      // Get current grids array from the existing tenant document
      final existingTenant = TenantDto.fromFirestore(tenantSnapshot);
      final oldGridIds = existingTenant.grids;
      final newGridIds = tenantWithCorrectId.grids;

      Logger.debug('[TenantRemoteDS] Existing grid IDs: $oldGridIds, New grid IDs: $newGridIds');

      // Find grids that were added and removed
      final gridsToAdd = newGridIds.where((gridId) => !oldGridIds.contains(gridId)).toList();
      final gridsToRemove = oldGridIds.where((gridId) => !newGridIds.contains(gridId)).toList();

      Logger.debug('[TenantRemoteDS] Grids to add: $gridsToAdd, Grids to remove: $gridsToRemove');

      // Update the tenant document
      transaction.update(tenantDocRef, TenantDto.toFirestore(tenantWithCorrectId));
      Logger.debug('[TenantRemoteDS] Transaction updating tenant document: $tenantId');

      // Update each grid that was added to this tenant
      for (final gridId in gridsToAdd) {
        final gridDocRef = _firestore.collection('stores').doc(storeId).collection('grids').doc(gridId);
        Logger.debug('[TenantRemoteDS] Transaction updating grid: $gridId to add tenantId: $tenantId');
        transaction.update(gridDocRef, {'tenantId': tenantId, 'updatedAt': FieldValue.serverTimestamp()});
      }

      // Update each grid that was removed from this tenant
      for (final gridId in gridsToRemove) {
        final gridDocRef = _firestore.collection('stores').doc(storeId).collection('grids').doc(gridId);
        Logger.debug('[TenantRemoteDS] Transaction updating grid: $gridId to remove tenantId (set to null)');
        transaction.update(gridDocRef, {'tenantId': null, 'updatedAt': FieldValue.serverTimestamp()});
      }
    });

    Logger.debug('[TenantRemoteDS] Successfully updated tenant: $tenantId');
  }

  @override
  Future<void> deleteTenant(String storeId, String tenantId) async {
    Logger.debug('[TenantRemoteDS] Deleting tenant in storeId: $storeId, id: $tenantId');

    // Run a transaction to delete the tenant and update all related grids
    await _firestore.runTransaction((transaction) async {
      final tenantDocRef = tenantsCollectionRef(storeId).doc(tenantId);
      final tenantSnapshot = await transaction.get(tenantDocRef);

      if (!tenantSnapshot.exists) {
        Logger.error('[TenantRemoteDS] Tenant not found for deletion: $tenantId');
        throw Exception('Tenant not found: $tenantId');
      }

      // Get current grids array from the existing tenant document
      final existingTenant = TenantDto.fromFirestore(tenantSnapshot);
      final gridIds = existingTenant.grids;

      Logger.debug('[TenantRemoteDS] Tenant has grid IDs: $gridIds that will be cleared');

      // Delete the tenant document
      transaction.delete(tenantDocRef);
      Logger.debug('[TenantRemoteDS] Transaction deleting tenant document: $tenantId');

      // Update each grid to clear their tenantId
      for (final gridId in gridIds) {
        final gridDocRef = _firestore.collection('stores').doc(storeId).collection('grids').doc(gridId);
        Logger.debug('[TenantRemoteDS] Transaction updating grid: $gridId to clear tenantId');
        transaction.update(gridDocRef, {'tenantId': null, 'updatedAt': FieldValue.serverTimestamp()});
      }
    });

    Logger.debug('[TenantRemoteDS] Successfully deleted tenant: $tenantId');
  }

  @override
  Stream<List<TenantEntity>> searchTenantsByName(String storeId, String query) {
    // Convert the query to lowercase for case-insensitive search
    final lowercaseQuery = query.toLowerCase();

    // Firebase doesn't support native case-insensitive search or LIKE operators,
    // so we'll fetch all tenants and filter client-side
    return tenantsCollectionRef(storeId).orderBy('name').snapshots().map((snapshot) {
      return snapshot.docs.map((doc) => TenantDto.fromFirestore(doc)).where((tenant) => tenant.name.toLowerCase().contains(lowercaseQuery)).toList();
    });
  }

  @override
  Future<void> syncTenantsAndGrids(String storeId) async {
    Logger.debug('[TenantRemoteDS] Starting sync of tenants and grids for storeId: $storeId');

    try {
      // Get all tenants and their grid IDs
      final tenantsSnapshot = await tenantsCollectionRef(storeId).get();
      final tenants = tenantsSnapshot.docs.map((doc) => TenantDto.fromFirestore(doc)).toList();

      Logger.debug('[TenantRemoteDS] Found ${tenants.length} tenants to sync');

      // Get all grids
      final gridsSnapshot = await _firestore.collection('stores').doc(storeId).collection('grids').get();

      Logger.debug('[TenantRemoteDS] Found ${gridsSnapshot.docs.length} grids to check');

      // Run a batch operation to update all relationships
      final batch = _firestore.batch();

      // Build a map of gridId -> tenantId based on the tenant.grids arrays
      final Map<String, String> expectedGridTenantMap = {};
      for (final tenant in tenants) {
        for (final gridId in tenant.grids) {
          expectedGridTenantMap[gridId] = tenant.id;
        }
      }

      // Check each grid and update if necessary
      int updatedGrids = 0;
      for (final gridDoc in gridsSnapshot.docs) {
        final gridId = gridDoc.id;
        final currentTenantId = gridDoc.data()['tenantId'];
        final expectedTenantId = expectedGridTenantMap[gridId];

        // If this grid should be assigned to a tenant but isn't, or is assigned to the wrong tenant
        if (expectedTenantId != null && currentTenantId != expectedTenantId) {
          Logger.debug('[TenantRemoteDS] Grid $gridId has incorrect tenantId: $currentTenantId, should be: $expectedTenantId');
          batch.update(gridDoc.reference, {'tenantId': expectedTenantId, 'updatedAt': FieldValue.serverTimestamp()});
          updatedGrids++;
        }
        // If this grid shouldn't be assigned to any tenant but is
        else if (expectedTenantId == null && currentTenantId != null) {
          Logger.debug('[TenantRemoteDS] Grid $gridId has tenantId: $currentTenantId but should be null');
          batch.update(gridDoc.reference, {'tenantId': null, 'updatedAt': FieldValue.serverTimestamp()});
          updatedGrids++;
        }
      }

      // Commit the batch if there are changes
      if (updatedGrids > 0) {
        await batch.commit();
        Logger.debug('[TenantRemoteDS] Sync completed successfully. Updated $updatedGrids grids.');
      } else {
        Logger.debug('[TenantRemoteDS] Sync completed. No updates needed.');
      }
    } catch (e) {
      Logger.error('[TenantRemoteDS] Error in syncTenantsAndGrids', e);
      rethrow;
    }
  }
}
