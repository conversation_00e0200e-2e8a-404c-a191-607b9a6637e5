import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/auth/data/datasources/auth_remote_datasource.dart';
import 'package:grid_pos/features/auth/data/models/user_app_model_dto.dart';
import 'package:grid_pos/features/auth/domain/entities/user_app_model.dart';
import 'package:grid_pos/firebase_options.dart';

/// Service for managing tenant user accounts
class TenantUserService {
  final FirebaseAuth _auth;
  final FirebaseFirestore _firestore;
  final AuthRemoteDataSource _authDataSource;

  /// Constructor
  TenantUserService({
    FirebaseAuth? auth,
    FirebaseFirestore? firestore,
    AuthRemoteDataSource? authDataSource,
  }) : _auth = auth ?? FirebaseAuth.instance,
       _firestore = firestore ?? FirebaseFirestore.instance,
       _authDataSource = authDataSource ?? AuthRemoteDataSourceImpl();

  /// Create a user account for a tenant
  ///
  /// This method creates an actual Firebase Auth account for the tenant
  /// using a secondary Firebase app instance to avoid signing out the admin.
  /// Returns the Firebase UID of the created user
  Future<String> createTenantUser({
    required String email,
    required String password,
    required String displayName,
    required String storeId,
    required String tenantId,
  }) async {
    final adminUser = _auth.currentUser;
    if (adminUser == null) {
      throw Exception(
        'No admin user currently signed in to perform this operation',
      );
    }
    final adminUid = adminUser.uid; // To verify admin session later

    Logger.debug(
      '[TenantUserService] Preparing to create tenant user account for email: $email using secondary Firebase app.',
    );

    FirebaseApp secondaryApp;
    const String secondaryAppName =
        'tenantCreationAuthApp'; // Unique name for the secondary app

    try {
      // Check if secondary app already exists
      secondaryApp = Firebase.app(secondaryAppName);
      Logger.debug(
        '[TenantUserService] Secondary Firebase app "$secondaryAppName" already initialized.',
      );
    } catch (e) {
      // Initialize secondary app if it doesn't exist
      Logger.debug(
        '[TenantUserService] Initializing secondary Firebase app: $secondaryAppName',
      );
      secondaryApp = await Firebase.initializeApp(
        name: secondaryAppName,
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }

    // Get FirebaseAuth instance for the secondary app
    final secondaryAuth = FirebaseAuth.instanceFor(app: secondaryApp);

    try {
      // Ensure secondary auth is signed out first
      if (secondaryAuth.currentUser != null) {
        await secondaryAuth.signOut();
      }

      // Create the new tenant user using the secondary auth instance
      final userCredential = await secondaryAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (userCredential.user == null) {
        throw Exception(
          'Failed to create tenant user account via secondary app.',
        );
      }

      final newUser = userCredential.user!;
      final newUserUid = newUser.uid;

      // Update display name for the tenant user (on the secondary auth instance's user object)
      await newUser.updateDisplayName(displayName);

      // Create the user document in Firestore
      final userModel = UserAppModel(
        uid: newUserUid,
        email: email,
        role: 'tenant', // Set role to tenant
        displayName: displayName,
        storeId: storeId,
        tenantId: tenantId,
        createdAt: DateTime.now(),
      );
      await _firestore
          .collection('users')
          .doc(newUserUid)
          .set(UserAppModelDto.toFirestore(userModel));

      // IMPORTANT: Sign out the new user from the secondary auth instance.
      await secondaryAuth.signOut();
      Logger.debug(
        '[TenantUserService] Signed out new user from secondary app "$secondaryAppName".',
      );

      // Verify admin is still signed in on the primary auth instance
      if (_auth.currentUser == null || _auth.currentUser!.uid != adminUid) {
        Logger.error(
          '[TenantUserService] CRITICAL: Admin session was affected despite using secondary app.',
        );

        // This is just a failsafe but shouldn't happen with the secondary app approach
        // Force a refresh on admin document to help update auth state
        await _firestore.collection('users').doc(adminUid).update({
          'lastActivity': FieldValue.serverTimestamp(),
        });
      } else {
        Logger.debug(
          '[TenantUserService] Admin session ($adminUid) remains active as expected.',
        );
      }

      Logger.debug(
        '[TenantUserService] Successfully created tenant user account ($newUserUid) via secondary app.',
      );
      return newUserUid;
    } on FirebaseAuthException catch (e) {
      Logger.error(
        '[TenantUserService] FirebaseAuthException during secondary app user creation: ${e.code}',
        e,
      );

      // Cleanup - sign out from secondary auth
      try {
        await secondaryAuth.signOut();
      } catch (signOutError) {
        Logger.warning(
          '[TenantUserService] Error during cleanup of secondary auth: $signOutError',
        );
      }

      throw _mapFirebaseAuthErrorToException(e);
    } catch (e, stackTrace) {
      Logger.error(
        '[TenantUserService] Error creating tenant user',
        e,
        stackTrace,
      );

      // Cleanup - sign out from secondary auth
      try {
        await secondaryAuth.signOut();
      } catch (signOutError) {
        Logger.warning(
          '[TenantUserService] Error during cleanup of secondary auth: $signOutError',
        );
      }

      throw Exception('Failed to create tenant user account: ${e.toString()}');
    }
  }

  /// Find a user by tenant ID
  ///
  /// This method queries the users collection for a user with the given tenant ID
  Future<UserAppModel?> findUserByTenantId(String tenantId) async {
    Logger.debug('[TenantUserService] Finding user for tenantId: $tenantId');

    try {
      final querySnapshot =
          await _firestore
              .collection('users')
              .where('tenantId', isEqualTo: tenantId)
              .limit(1)
              .get();

      if (querySnapshot.docs.isEmpty) {
        Logger.debug(
          '[TenantUserService] No user found for tenantId: $tenantId',
        );
        return null;
      }

      final userDoc = querySnapshot.docs.first;
      Logger.debug(
        '[TenantUserService] Found user ${userDoc.id} for tenantId: $tenantId',
      );
      return UserAppModelDto.fromFirestore(userDoc);
    } catch (e) {
      Logger.error(
        '[TenantUserService] Error finding user for tenantId: $tenantId',
        e,
      );
      return null;
    }
  }

  /// Delete a tenant user account
  ///
  /// This method deletes a user from both Firebase Auth and Firestore
  /// using a secondary Firebase app to preserve the admin session
  Future<void> deleteTenantUser(String userId) async {
    final adminUser = _auth.currentUser;
    if (adminUser == null) {
      throw Exception(
        'No admin user currently signed in to perform this operation',
      );
    }
    final adminUid = adminUser.uid; // To verify admin session later

    Logger.debug(
      '[TenantUserService] Preparing to delete user account: $userId using secondary Firebase app',
    );

    FirebaseApp secondaryApp;
    const String secondaryAppName =
        'tenantDeletionAuthApp'; // Unique name for the secondary app

    try {
      // Check if secondary app already exists
      secondaryApp = Firebase.app(secondaryAppName);
      Logger.debug(
        '[TenantUserService] Secondary Firebase app "$secondaryAppName" already initialized.',
      );
    } catch (e) {
      // Initialize secondary app if it doesn't exist
      Logger.debug(
        '[TenantUserService] Initializing secondary Firebase app: $secondaryAppName',
      );
      secondaryApp = await Firebase.initializeApp(
        name: secondaryAppName,
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }

    // Get FirebaseAuth instance for the secondary app
    final secondaryAuth = FirebaseAuth.instanceFor(app: secondaryApp);

    try {
      // Get user email from Firestore first (needed for sign-in)
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        throw Exception('User document not found in Firestore: $userId');
      }
      final userData = userDoc.data();
      final userEmail = userData?['email'] as String?;

      if (userEmail == null || userEmail.isEmpty) {
        throw Exception('User email not found for user: $userId');
      }

      // Delete the user document from Firestore first
      await _firestore.collection('users').doc(userId).delete();
      Logger.debug(
        '[TenantUserService] Deleted user document from Firestore for: $userId',
      );

      // For Firebase Auth user deletion, we need admin SDK or custom token
      // Since this is client-side, we'll approach it differently
      // We'd need a Cloud Function for proper deletion of Firebase Auth user

      // Instead, just log that proper deletion requires backend function
      Logger.warning(
        '[TenantUserService] Firebase Auth user deletion requires backend Cloud Function. Consider implementing it server-side.',
      );

      // Verify admin is still signed in on the primary auth instance
      if (_auth.currentUser == null || _auth.currentUser!.uid != adminUid) {
        Logger.error(
          '[TenantUserService] CRITICAL: Admin session was affected during user deletion',
        );

        // Force a refresh on admin document to help update auth state
        await _firestore.collection('users').doc(adminUid).update({
          'lastActivity': FieldValue.serverTimestamp(),
        });
      }

      Logger.debug(
        '[TenantUserService] Successfully handled deletion of tenant user: $userId',
      );
    } catch (e, stackTrace) {
      Logger.error(
        '[TenantUserService] Error deleting tenant user',
        e,
        stackTrace,
      );
      throw Exception('Failed to delete tenant user account: ${e.toString()}');
    }
  }

  /// Maps Firebase Auth exceptions to user-friendly exceptions
  Exception _mapFirebaseAuthErrorToException(FirebaseAuthException e) {
    switch (e.code) {
      case 'email-already-in-use':
        return Exception(
          'The email address is already in use by another account.',
        );
      case 'weak-password':
        return Exception(
          'The password is too weak. Please use a stronger password.',
        );
      case 'invalid-email':
        return Exception('The email address is invalid.');
      default:
        return Exception(
          e.message ?? 'An error occurred creating the user account.',
        );
    }
  }
}
