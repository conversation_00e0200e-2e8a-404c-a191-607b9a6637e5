import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:grid_pos/features/tenant_mgmt/data/models/grid_dto.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/grid_entity.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'dart:async';

/// Interface for grid remote data source operations
abstract class GridRemoteDataSource {
  /// Get a grid by ID
  Future<GridEntity?> getGrid(String storeId, String gridId);

  /// Stream a single grid's data
  Stream<GridEntity?> watchGrid(String storeId, String gridId);

  /// Stream all grids for a store
  Stream<List<GridEntity>> watchGrids(String storeId);

  /// Stream available grids (grids with no tenant assigned)
  Stream<List<GridEntity>> watchAvailableGrids(String storeId);

  /// Stream grids assigned to a specific tenant
  Stream<List<GridEntity>> watchGridsByTenant(String storeId, String tenantId);

  /// Stream grids by size
  Stream<List<GridEntity>> watchGridsBySize(String storeId, String size);

  /// Update a grid's tenant assignment
  Future<void> updateGridTenant(String storeId, String gridId, String? tenantId);

  /// Update grid details
  Future<void> updateGrid(String storeId, String gridId, GridEntity grid);

  /// Create a new grid
  Future<String> createGrid(String storeId, GridEntity grid);

  /// Delete a grid
  Future<void> deleteGrid(String storeId, String gridId);

  /// Get collection reference for grids
  CollectionReference<Map<String, dynamic>> gridsCollectionRef(String storeId);
}

/// Implementation of GridRemoteDataSource with Firestore
class GridRemoteDataSourceImpl implements GridRemoteDataSource {
  final FirebaseFirestore _firestore;

  /// Constructor
  GridRemoteDataSourceImpl({FirebaseFirestore? firestore}) : _firestore = firestore ?? FirebaseFirestore.instance;

  @override
  CollectionReference<Map<String, dynamic>> gridsCollectionRef(String storeId) {
    return _firestore.collection('stores').doc(storeId).collection('grids');
  }

  @override
  Future<GridEntity?> getGrid(String storeId, String gridId) async {
    final docSnapshot = await gridsCollectionRef(storeId).doc(gridId).get();

    if (!docSnapshot.exists) {
      return null;
    }

    return GridDto.fromFirestore(docSnapshot);
  }

  @override
  Stream<GridEntity?> watchGrid(String storeId, String gridId) {
    Logger.debug('[GridRemoteDS] watchGrid invoked for storeId: "$storeId", gridId: "$gridId"');
    return gridsCollectionRef(storeId).doc(gridId).snapshots().map((snapshot) {
      if (!snapshot.exists) {
        Logger.debug('[GridRemoteDS] Grid not found for storeId: "$storeId", gridId: "$gridId"');
        return null;
      }

      return GridDto.fromFirestore(snapshot);
    });
  }

  @override
  Stream<List<GridEntity>> watchGrids(String storeId) {
    Logger.debug('[GridRemoteDS] watchGrids invoked for storeId: "$storeId"');

    if (storeId.isEmpty) {
      // Or other invalid ID checks
      Logger.warning('[GridRemoteDS] watchGrids called with empty storeId. Returning empty stream.');
      return Stream.value([]); // Avoid querying Firestore with invalid ID
    }

    final streamController = StreamController<List<GridEntity>>();

    // Create the Firestore stream - logging each step
    Logger.debug('[GridRemoteDS] Creating Firestore snapshots() stream for storeId: "$storeId"');
    final query = gridsCollectionRef(storeId).orderBy('code');
    final subscription = query.snapshots().listen(
      (snapshot) {
        Logger.debug('[GridRemoteDS] Snapshot received for storeId: "$storeId", docs count: ${snapshot.docs.length}');
        final grids = snapshot.docs.map((doc) => GridDto.fromFirestore(doc)).toList();
        streamController.add(grids);
      },
      onError: (error, stackTrace) {
        Logger.error('[GridRemoteDS] Error in Firestore stream for storeId: "$storeId"', error, stackTrace);
        streamController.addError(error, stackTrace);
      },
      onDone: () {
        Logger.debug('[GridRemoteDS] Stream done for storeId: "$storeId"');
        streamController.close();
      },
    );

    // Close the controller and cancel subscription when the stream is no longer listened to
    streamController.onCancel = () {
      Logger.debug('[GridRemoteDS] Stream controller canceled for storeId: "$storeId"');
      subscription.cancel();
    };

    return streamController.stream;
  }

  @override
  Stream<List<GridEntity>> watchAvailableGrids(String storeId) {
    Logger.debug('[GridRemoteDS] watchAvailableGrids invoked for storeId: "$storeId"');
    return gridsCollectionRef(storeId).where('tenantId', isNull: true).orderBy('code').snapshots().map((snapshot) {
      Logger.debug('[GridRemoteDS] Available grids snapshot received for storeId: "$storeId", count: ${snapshot.docs.length}');
      return snapshot.docs.map((doc) => GridDto.fromFirestore(doc)).toList();
    });
  }

  @override
  Stream<List<GridEntity>> watchGridsByTenant(String storeId, String tenantId) {
    Logger.debug('[GridRemoteDS] watchGridsByTenant called for storeId: "$storeId", tenantId: "$tenantId"');

    // 驗證參數
    if (storeId.isEmpty) {
      Logger.warning('[GridRemoteDS] Empty storeId provided, returning error stream');
      return Stream.error(Exception('Invalid storeId provided to watchGridsByTenant'));
    }

    // 如果 tenantId 為空字串，直接返回空列表
    if (tenantId.isEmpty) {
      Logger.debug('[GridRemoteDS] Empty tenantId provided, returning empty list');
      return Stream.value([]);
    }

    // 創建 StreamController 來更好地控制錯誤處理
    final streamController = StreamController<List<GridEntity>>();

    try {
      Logger.debug('[GridRemoteDS] Creating Firestore query for tenant grids');
      final query = gridsCollectionRef(storeId).where('tenantId', isEqualTo: tenantId).orderBy('code');

      final subscription = query.snapshots().listen(
        (snapshot) {
          try {
            Logger.debug('[GridRemoteDS] Grid snapshot for tenant "$tenantId" received, count: ${snapshot.docs.length}');

            final grids =
                snapshot.docs.map((doc) {
                  final grid = GridDto.fromFirestore(doc);
                  Logger.debug('[GridRemoteDS] Grid found: ${grid.id}, code: ${grid.code}, tenantId: ${grid.tenantId}');
                  return grid;
                }).toList();

            if (!streamController.isClosed) {
              streamController.add(grids);
            }
          } catch (error, stackTrace) {
            Logger.error('[GridRemoteDS] Error processing snapshot for tenant "$tenantId"', error, stackTrace);
            if (!streamController.isClosed) {
              streamController.addError(error, stackTrace);
            }
          }
        },
        onError: (error, stackTrace) {
          Logger.error('[GridRemoteDS] Firestore stream error for tenant "$tenantId"', error, stackTrace);
          if (!streamController.isClosed) {
            streamController.addError(error, stackTrace);
          }
        },
      );

      // 清理資源
      streamController.onCancel = () {
        Logger.debug('[GridRemoteDS] Cancelling subscription for tenant "$tenantId"');
        subscription.cancel();
      };
    } catch (error, stackTrace) {
      Logger.error('[GridRemoteDS] Error setting up stream for tenant "$tenantId"', error, stackTrace);
      streamController.addError(error, stackTrace);
    }

    return streamController.stream;
  }

  @override
  Stream<List<GridEntity>> watchGridsBySize(String storeId, String size) {
    return gridsCollectionRef(storeId).where('size', isEqualTo: size).orderBy('code').snapshots().map((snapshot) {
      return snapshot.docs.map((doc) => GridDto.fromFirestore(doc)).toList();
    });
  }

  @override
  Future<void> updateGridTenant(String storeId, String gridId, String? tenantId) async {
    await gridsCollectionRef(storeId).doc(gridId).update({'tenantId': tenantId, 'updatedAt': FieldValue.serverTimestamp()});
  }

  @override
  Future<void> updateGrid(String storeId, String gridId, GridEntity grid) async {
    // Ensure the grid ID matches the document ID
    final gridWithCorrectId = grid.copyWith(id: gridId);

    await gridsCollectionRef(storeId).doc(gridId).update(GridDto.toFirestore(gridWithCorrectId));
  }

  @override
  Future<String> createGrid(String storeId, GridEntity grid) async {
    final docRef = gridsCollectionRef(storeId).doc();

    // Create a grid with a new ID, ensuring timestamps are set
    final now = DateTime.now();
    final gridWithId = grid.copyWith(id: docRef.id, createdAt: now, updatedAt: now);

    // Set the document data in Firestore
    await docRef.set(GridDto.toFirestore(gridWithId));

    return docRef.id;
  }

  @override
  Future<void> deleteGrid(String storeId, String gridId) async {
    await gridsCollectionRef(storeId).doc(gridId).delete();
  }
}
