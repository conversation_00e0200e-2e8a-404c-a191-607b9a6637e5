import 'package:grid_pos/features/tenant_mgmt/data/datasources/grid_remote_ds.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/grid_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/repositories/grid_repository.dart';

/// Implementation of GridRepository
class GridRepositoryImpl implements GridRepository {
  final GridRemoteDataSource _gridRemoteDS;

  /// Constructor
  GridRepositoryImpl({required GridRemoteDataSource gridRemoteDS}) : _gridRemoteDS = gridRemoteDS;

  @override
  Stream<List<GridEntity>> watchGrids(String storeId) {
    return _gridRemoteDS.watchGrids(storeId);
  }

  @override
  Stream<GridEntity?> watchGrid(String storeId, String gridId) {
    return _gridRemoteDS.watchGrid(storeId, gridId);
  }

  @override
  Stream<List<GridEntity>> watchAvailableGrids(String storeId) {
    return _gridRemoteDS.watchAvailableGrids(storeId);
  }

  @override
  Stream<List<GridEntity>> watchGridsByTenant(String storeId, String tenantId) {
    return _gridRemoteDS.watchGridsByTenant(storeId, tenantId);
  }

  @override
  Stream<List<GridEntity>> watchGridsBySize(String storeId, String size) {
    return _gridRemoteDS.watchGridsBySize(storeId, size);
  }

  @override
  Future<void> updateGridTenant(String storeId, String gridId, String? tenantId) {
    return _gridRemoteDS.updateGridTenant(storeId, gridId, tenantId);
  }

  @override
  Future<String> addGrid(String storeId, GridEntity grid) {
    return _gridRemoteDS.createGrid(storeId, grid);
  }

  @override
  Future<void> updateGridDetails(String storeId, String gridId, GridEntity grid) {
    return _gridRemoteDS.updateGrid(storeId, gridId, grid);
  }

  @override
  Future<void> deleteGrid(String storeId, String gridId) async {
    // Check if grid is assigned to a tenant
    final grid = await _gridRemoteDS.getGrid(storeId, gridId);

    if (grid == null) {
      throw Exception('Grid not found');
    }

    // Only allow deletion if grid is not assigned to a tenant
    if (grid.tenantId != null) {
      throw Exception('Cannot delete grid that is assigned to a tenant. Unassign tenant first.');
    }

    return _gridRemoteDS.deleteGrid(storeId, gridId);
  }
}
