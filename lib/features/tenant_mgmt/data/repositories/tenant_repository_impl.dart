import 'package:grid_pos/features/tenant_mgmt/data/datasources/grid_assignment_service.dart';
import 'package:grid_pos/features/tenant_mgmt/data/datasources/tenant_remote_ds.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/tenant_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/repositories/tenant_repository.dart';

/// Implementation of TenantRepository
class TenantRepositoryImpl implements TenantRepository {
  final TenantRemoteDataSource _tenantRemoteDS;
  final GridAssignmentService _gridAssignmentService;

  /// Constructor
  TenantRepositoryImpl({required TenantRemoteDataSource tenantRemoteDS, required GridAssignmentService gridAssignmentService})
    : _tenantRemoteDS = tenantRemoteDS,
      _gridAssignmentService = gridAssignmentService;

  @override
  Stream<List<TenantEntity>> watchTenants(String storeId) {
    return _tenantRemoteDS.watchTenants(storeId);
  }

  @override
  Stream<TenantEntity?> watchTenant(String storeId, String tenantId) {
    return _tenantRemoteDS.watchTenant(storeId, tenantId);
  }

  @override
  Stream<List<TenantEntity>> watchTenantsByStatus(String storeId, bool active) {
    return _tenantRemoteDS.watchTenantsByStatus(storeId, active);
  }

  @override
  Stream<List<TenantEntity>> searchTenantsByName(String storeId, String query) {
    return _tenantRemoteDS.searchTenantsByName(storeId, query);
  }

  @override
  Future<String> addTenant(String storeId, TenantEntity tenant) {
    return _tenantRemoteDS.createTenant(storeId, tenant);
  }

  @override
  Future<void> updateTenant(String storeId, String tenantId, TenantEntity tenant) {
    return _tenantRemoteDS.updateTenant(storeId, tenantId, tenant);
  }

  @override
  Future<void> deleteTenant(String storeId, String tenantId) {
    return _tenantRemoteDS.deleteTenant(storeId, tenantId);
  }

  @override
  Future<void> assignGridToTenantTransaction(String storeId, String gridId, String tenantId) {
    return _gridAssignmentService.assignGridToTenant(storeId, gridId, tenantId);
  }

  @override
  Future<void> unassignGridFromTenantTransaction(String storeId, String gridId, String tenantId) {
    return _gridAssignmentService.unassignGridFromTenant(storeId, gridId, tenantId);
  }

  @override
  Future<void> syncTenantsAndGrids(String storeId) {
    return _tenantRemoteDS.syncTenantsAndGrids(storeId);
  }
}
