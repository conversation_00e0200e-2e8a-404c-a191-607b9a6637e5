import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/tenant_entity.dart';

/// Data Transfer Object for TenantEntity to handle conversions between
/// domain entity and Firestore document
class TenantDto {
  /// Converts a Firestore document to a TenantEntity
  static TenantEntity fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data() ?? {};

    // Handle contact information
    final contact =
        data['contact'] is Map
            ? ContactInfo(phone: (data['contact'] as Map)['phone'] as String? ?? '', email: (data['contact'] as Map)['email'] as String? ?? '')
            : const ContactInfo(phone: '', email: '');

    // Handle contract information
    final contractData = data['contract'] as Map?;
    final contract =
        contractData != null
            ? ContractEntity(
              start: contractData['start'] is Timestamp ? (contractData['start'] as Timestamp).toDate() : DateTime.now(),
              end: contractData['end'] is Timestamp ? (contractData['end'] as Timestamp).toDate() : DateTime.now().add(const Duration(days: 365)),
              rent: (contractData['rent'] as num?)?.toDouble() ?? 0.0,
            )
            : ContractEntity(start: DateTime.now(), end: DateTime.now().add(const Duration(days: 365)), rent: 0.0);

    // Create the tenant entity
    return TenantEntity(
      id: doc.id,
      name: data['name'] as String? ?? '',
      contact: contact,
      grids: List<String>.from(data['grids'] ?? []),
      active: data['active'] as bool? ?? false,
      contract: contract,
      createdAt: data['createdAt'] is Timestamp ? (data['createdAt'] as Timestamp).toDate() : DateTime.now(),
      updatedAt: data['updatedAt'] is Timestamp ? (data['updatedAt'] as Timestamp).toDate() : DateTime.now(),
    );
  }

  /// Converts a TenantEntity to a Map for Firestore
  static Map<String, dynamic> toFirestore(TenantEntity entity) {
    return {
      'name': entity.name,
      'contact': {'phone': entity.contact.phone, 'email': entity.contact.email},
      'grids': entity.grids,
      'active': entity.active,
      'contract': {'start': Timestamp.fromDate(entity.contract.start), 'end': Timestamp.fromDate(entity.contract.end), 'rent': entity.contract.rent},
      'createdAt': Timestamp.fromDate(entity.createdAt),
      'updatedAt': Timestamp.fromDate(entity.updatedAt),
    };
  }
}
