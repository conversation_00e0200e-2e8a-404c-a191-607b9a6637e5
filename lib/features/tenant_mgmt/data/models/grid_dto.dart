import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/grid_entity.dart';

/// Data Transfer Object for GridEntity to handle conversions between
/// domain entity and Firestore document
class GridDto {
  /// Converts a Firestore document to a GridEntity
  static GridEntity fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data() ?? {};

    return GridEntity(
      id: doc.id,
      code: data['code'] as String? ?? '',
      tenantId: data['tenantId'] as String?,
      size: data['size'] as String? ?? 'M', // Default to medium size
      createdAt: data['createdAt'] is Timestamp ? (data['createdAt'] as Timestamp).toDate() : DateTime.now(),
      updatedAt: data['updatedAt'] is Timestamp ? (data['updatedAt'] as Timestamp).toDate() : DateTime.now(),
    );
  }

  /// Converts a GridEntity to a Map for Firestore
  static Map<String, dynamic> toFirestore(GridEntity entity) {
    return {'code': entity.code, 'tenantId': entity.tenantId, 'size': entity.size, 'createdAt': Timestamp.fromDate(entity.createdAt), 'updatedAt': Timestamp.fromDate(entity.updatedAt)};
  }
}
