import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'grid_entity.freezed.dart';
part 'grid_entity.g.dart';

/// The grid entity that represents a physical grid space in a store
@freezed
abstract class GridEntity with _$GridEntity {
  /// Factory constructor for creating a new [GridEntity]
  const factory GridEntity({
    required String id,
    required String code,
    String? tenantId,
    required String size,
    @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required DateTime createdAt,
    @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required DateTime updatedAt,
  }) = _GridEntity;

  /// Creates an instance of [GridEntity] from a Map.
  factory GridEntity.fromJson(Map<String, dynamic> json) => _$GridEntityFromJson(json);
}

/// Helper function to convert Timestamp to DateTime
DateTime _timestampFromJson(dynamic timestamp) {
  if (timestamp is Timestamp) {
    return timestamp.toDate();
  }
  return DateTime.now();
}

/// Helper function to convert DateTime to Timestamp
dynamic _timestampToJson(DateTime dateTime) {
  return Timestamp.fromDate(dateTime);
}
