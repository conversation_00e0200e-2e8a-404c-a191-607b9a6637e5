// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tenant_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TenantEntity {

 String get id; String get name; ContactInfo get contact; List<String> get grids; bool get active; ContractEntity get contract;@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime get createdAt;@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime get updatedAt;
/// Create a copy of TenantEntity
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TenantEntityCopyWith<TenantEntity> get copyWith => _$TenantEntityCopyWithImpl<TenantEntity>(this as TenantEntity, _$identity);

  /// Serializes this TenantEntity to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TenantEntity&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.contact, contact) || other.contact == contact)&&const DeepCollectionEquality().equals(other.grids, grids)&&(identical(other.active, active) || other.active == active)&&(identical(other.contract, contract) || other.contract == contract)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,contact,const DeepCollectionEquality().hash(grids),active,contract,createdAt,updatedAt);

@override
String toString() {
  return 'TenantEntity(id: $id, name: $name, contact: $contact, grids: $grids, active: $active, contract: $contract, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $TenantEntityCopyWith<$Res>  {
  factory $TenantEntityCopyWith(TenantEntity value, $Res Function(TenantEntity) _then) = _$TenantEntityCopyWithImpl;
@useResult
$Res call({
 String id, String name, ContactInfo contact, List<String> grids, bool active, ContractEntity contract,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime createdAt,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime updatedAt
});


$ContactInfoCopyWith<$Res> get contact;$ContractEntityCopyWith<$Res> get contract;

}
/// @nodoc
class _$TenantEntityCopyWithImpl<$Res>
    implements $TenantEntityCopyWith<$Res> {
  _$TenantEntityCopyWithImpl(this._self, this._then);

  final TenantEntity _self;
  final $Res Function(TenantEntity) _then;

/// Create a copy of TenantEntity
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? contact = null,Object? grids = null,Object? active = null,Object? contract = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,contact: null == contact ? _self.contact : contact // ignore: cast_nullable_to_non_nullable
as ContactInfo,grids: null == grids ? _self.grids : grids // ignore: cast_nullable_to_non_nullable
as List<String>,active: null == active ? _self.active : active // ignore: cast_nullable_to_non_nullable
as bool,contract: null == contract ? _self.contract : contract // ignore: cast_nullable_to_non_nullable
as ContractEntity,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}
/// Create a copy of TenantEntity
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ContactInfoCopyWith<$Res> get contact {
  
  return $ContactInfoCopyWith<$Res>(_self.contact, (value) {
    return _then(_self.copyWith(contact: value));
  });
}/// Create a copy of TenantEntity
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ContractEntityCopyWith<$Res> get contract {
  
  return $ContractEntityCopyWith<$Res>(_self.contract, (value) {
    return _then(_self.copyWith(contract: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _TenantEntity implements TenantEntity {
  const _TenantEntity({required this.id, required this.name, required this.contact, required final  List<String> grids, required this.active, required this.contract, @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required this.createdAt, @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required this.updatedAt}): _grids = grids;
  factory _TenantEntity.fromJson(Map<String, dynamic> json) => _$TenantEntityFromJson(json);

@override final  String id;
@override final  String name;
@override final  ContactInfo contact;
 final  List<String> _grids;
@override List<String> get grids {
  if (_grids is EqualUnmodifiableListView) return _grids;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_grids);
}

@override final  bool active;
@override final  ContractEntity contract;
@override@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) final  DateTime createdAt;
@override@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) final  DateTime updatedAt;

/// Create a copy of TenantEntity
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TenantEntityCopyWith<_TenantEntity> get copyWith => __$TenantEntityCopyWithImpl<_TenantEntity>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TenantEntityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TenantEntity&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.contact, contact) || other.contact == contact)&&const DeepCollectionEquality().equals(other._grids, _grids)&&(identical(other.active, active) || other.active == active)&&(identical(other.contract, contract) || other.contract == contract)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,contact,const DeepCollectionEquality().hash(_grids),active,contract,createdAt,updatedAt);

@override
String toString() {
  return 'TenantEntity(id: $id, name: $name, contact: $contact, grids: $grids, active: $active, contract: $contract, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$TenantEntityCopyWith<$Res> implements $TenantEntityCopyWith<$Res> {
  factory _$TenantEntityCopyWith(_TenantEntity value, $Res Function(_TenantEntity) _then) = __$TenantEntityCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, ContactInfo contact, List<String> grids, bool active, ContractEntity contract,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime createdAt,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime updatedAt
});


@override $ContactInfoCopyWith<$Res> get contact;@override $ContractEntityCopyWith<$Res> get contract;

}
/// @nodoc
class __$TenantEntityCopyWithImpl<$Res>
    implements _$TenantEntityCopyWith<$Res> {
  __$TenantEntityCopyWithImpl(this._self, this._then);

  final _TenantEntity _self;
  final $Res Function(_TenantEntity) _then;

/// Create a copy of TenantEntity
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? contact = null,Object? grids = null,Object? active = null,Object? contract = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_TenantEntity(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,contact: null == contact ? _self.contact : contact // ignore: cast_nullable_to_non_nullable
as ContactInfo,grids: null == grids ? _self._grids : grids // ignore: cast_nullable_to_non_nullable
as List<String>,active: null == active ? _self.active : active // ignore: cast_nullable_to_non_nullable
as bool,contract: null == contract ? _self.contract : contract // ignore: cast_nullable_to_non_nullable
as ContractEntity,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

/// Create a copy of TenantEntity
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ContactInfoCopyWith<$Res> get contact {
  
  return $ContactInfoCopyWith<$Res>(_self.contact, (value) {
    return _then(_self.copyWith(contact: value));
  });
}/// Create a copy of TenantEntity
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ContractEntityCopyWith<$Res> get contract {
  
  return $ContractEntityCopyWith<$Res>(_self.contract, (value) {
    return _then(_self.copyWith(contract: value));
  });
}
}


/// @nodoc
mixin _$ContractEntity {

@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime get start;@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime get end; double get rent;
/// Create a copy of ContractEntity
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ContractEntityCopyWith<ContractEntity> get copyWith => _$ContractEntityCopyWithImpl<ContractEntity>(this as ContractEntity, _$identity);

  /// Serializes this ContractEntity to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ContractEntity&&(identical(other.start, start) || other.start == start)&&(identical(other.end, end) || other.end == end)&&(identical(other.rent, rent) || other.rent == rent));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,start,end,rent);

@override
String toString() {
  return 'ContractEntity(start: $start, end: $end, rent: $rent)';
}


}

/// @nodoc
abstract mixin class $ContractEntityCopyWith<$Res>  {
  factory $ContractEntityCopyWith(ContractEntity value, $Res Function(ContractEntity) _then) = _$ContractEntityCopyWithImpl;
@useResult
$Res call({
@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime start,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime end, double rent
});




}
/// @nodoc
class _$ContractEntityCopyWithImpl<$Res>
    implements $ContractEntityCopyWith<$Res> {
  _$ContractEntityCopyWithImpl(this._self, this._then);

  final ContractEntity _self;
  final $Res Function(ContractEntity) _then;

/// Create a copy of ContractEntity
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? start = null,Object? end = null,Object? rent = null,}) {
  return _then(_self.copyWith(
start: null == start ? _self.start : start // ignore: cast_nullable_to_non_nullable
as DateTime,end: null == end ? _self.end : end // ignore: cast_nullable_to_non_nullable
as DateTime,rent: null == rent ? _self.rent : rent // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ContractEntity implements ContractEntity {
  const _ContractEntity({@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required this.start, @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required this.end, required this.rent});
  factory _ContractEntity.fromJson(Map<String, dynamic> json) => _$ContractEntityFromJson(json);

@override@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) final  DateTime start;
@override@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) final  DateTime end;
@override final  double rent;

/// Create a copy of ContractEntity
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ContractEntityCopyWith<_ContractEntity> get copyWith => __$ContractEntityCopyWithImpl<_ContractEntity>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ContractEntityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ContractEntity&&(identical(other.start, start) || other.start == start)&&(identical(other.end, end) || other.end == end)&&(identical(other.rent, rent) || other.rent == rent));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,start,end,rent);

@override
String toString() {
  return 'ContractEntity(start: $start, end: $end, rent: $rent)';
}


}

/// @nodoc
abstract mixin class _$ContractEntityCopyWith<$Res> implements $ContractEntityCopyWith<$Res> {
  factory _$ContractEntityCopyWith(_ContractEntity value, $Res Function(_ContractEntity) _then) = __$ContractEntityCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime start,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime end, double rent
});




}
/// @nodoc
class __$ContractEntityCopyWithImpl<$Res>
    implements _$ContractEntityCopyWith<$Res> {
  __$ContractEntityCopyWithImpl(this._self, this._then);

  final _ContractEntity _self;
  final $Res Function(_ContractEntity) _then;

/// Create a copy of ContractEntity
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? start = null,Object? end = null,Object? rent = null,}) {
  return _then(_ContractEntity(
start: null == start ? _self.start : start // ignore: cast_nullable_to_non_nullable
as DateTime,end: null == end ? _self.end : end // ignore: cast_nullable_to_non_nullable
as DateTime,rent: null == rent ? _self.rent : rent // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$ContactInfo {

 String get phone; String get email;
/// Create a copy of ContactInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ContactInfoCopyWith<ContactInfo> get copyWith => _$ContactInfoCopyWithImpl<ContactInfo>(this as ContactInfo, _$identity);

  /// Serializes this ContactInfo to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ContactInfo&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.email, email) || other.email == email));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,phone,email);

@override
String toString() {
  return 'ContactInfo(phone: $phone, email: $email)';
}


}

/// @nodoc
abstract mixin class $ContactInfoCopyWith<$Res>  {
  factory $ContactInfoCopyWith(ContactInfo value, $Res Function(ContactInfo) _then) = _$ContactInfoCopyWithImpl;
@useResult
$Res call({
 String phone, String email
});




}
/// @nodoc
class _$ContactInfoCopyWithImpl<$Res>
    implements $ContactInfoCopyWith<$Res> {
  _$ContactInfoCopyWithImpl(this._self, this._then);

  final ContactInfo _self;
  final $Res Function(ContactInfo) _then;

/// Create a copy of ContactInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? phone = null,Object? email = null,}) {
  return _then(_self.copyWith(
phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ContactInfo implements ContactInfo {
  const _ContactInfo({required this.phone, required this.email});
  factory _ContactInfo.fromJson(Map<String, dynamic> json) => _$ContactInfoFromJson(json);

@override final  String phone;
@override final  String email;

/// Create a copy of ContactInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ContactInfoCopyWith<_ContactInfo> get copyWith => __$ContactInfoCopyWithImpl<_ContactInfo>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ContactInfoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ContactInfo&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.email, email) || other.email == email));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,phone,email);

@override
String toString() {
  return 'ContactInfo(phone: $phone, email: $email)';
}


}

/// @nodoc
abstract mixin class _$ContactInfoCopyWith<$Res> implements $ContactInfoCopyWith<$Res> {
  factory _$ContactInfoCopyWith(_ContactInfo value, $Res Function(_ContactInfo) _then) = __$ContactInfoCopyWithImpl;
@override @useResult
$Res call({
 String phone, String email
});




}
/// @nodoc
class __$ContactInfoCopyWithImpl<$Res>
    implements _$ContactInfoCopyWith<$Res> {
  __$ContactInfoCopyWithImpl(this._self, this._then);

  final _ContactInfo _self;
  final $Res Function(_ContactInfo) _then;

/// Create a copy of ContactInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? phone = null,Object? email = null,}) {
  return _then(_ContactInfo(
phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
