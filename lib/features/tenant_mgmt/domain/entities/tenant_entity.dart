import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'tenant_entity.freezed.dart';
part 'tenant_entity.g.dart';

/// The tenant entity that represents a tenant in the Grid POS system
@freezed
abstract class TenantEntity with _$TenantEntity {
  /// Factory constructor for creating a new [TenantEntity]
  const factory TenantEntity({
    required String id,
    required String name,
    required ContactInfo contact,
    required List<String> grids,
    required bool active,
    required ContractEntity contract,
    @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required DateTime createdAt,
    @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required DateTime updatedAt,
  }) = _TenantEntity;

  /// Creates an instance of [TenantEntity] from a Map.
  factory TenantEntity.fromJson(Map<String, dynamic> json) => _$TenantEntityFromJson(json);
}

/// The contract entity that represents a rental contract
@freezed
abstract class ContractEntity with _$ContractEntity {
  /// Factory constructor for creating a new [ContractEntity]
  const factory ContractEntity({
    @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required DateTime start,
    @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required DateTime end,
    required double rent,
  }) = _ContractEntity;

  /// Creates an instance of [ContractEntity] from a Map.
  factory ContractEntity.fromJson(Map<String, dynamic> json) => _$ContractEntityFromJson(json);
}

/// The contact information entity
@freezed
abstract class ContactInfo with _$ContactInfo {
  /// Factory constructor for creating a new [ContactInfo]
  const factory ContactInfo({required String phone, required String email}) = _ContactInfo;

  /// Creates an instance of [ContactInfo] from a Map.
  factory ContactInfo.fromJson(Map<String, dynamic> json) => _$ContactInfoFromJson(json);
}

/// Helper function to convert Timestamp to DateTime
DateTime _timestampFromJson(dynamic timestamp) {
  if (timestamp is Timestamp) {
    return timestamp.toDate();
  }
  return DateTime.now();
}

/// Helper function to convert DateTime to Timestamp
dynamic _timestampToJson(DateTime dateTime) {
  return Timestamp.fromDate(dateTime);
}
