// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tenant_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TenantEntity _$TenantEntityFromJson(Map<String, dynamic> json) =>
    _TenantEntity(
      id: json['id'] as String,
      name: json['name'] as String,
      contact: ContactInfo.fromJson(json['contact'] as Map<String, dynamic>),
      grids: (json['grids'] as List<dynamic>).map((e) => e as String).toList(),
      active: json['active'] as bool,
      contract: ContractEntity.fromJson(
        json['contract'] as Map<String, dynamic>,
      ),
      createdAt: _timestampFromJson(json['createdAt']),
      updatedAt: _timestampFromJson(json['updatedAt']),
    );

Map<String, dynamic> _$TenantEntityToJson(_TenantEntity instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'contact': instance.contact,
      'grids': instance.grids,
      'active': instance.active,
      'contract': instance.contract,
      'createdAt': _timestampToJson(instance.createdAt),
      'updatedAt': _timestampToJson(instance.updatedAt),
    };

_ContractEntity _$ContractEntityFromJson(Map<String, dynamic> json) =>
    _ContractEntity(
      start: _timestampFromJson(json['start']),
      end: _timestampFromJson(json['end']),
      rent: (json['rent'] as num).toDouble(),
    );

Map<String, dynamic> _$ContractEntityToJson(_ContractEntity instance) =>
    <String, dynamic>{
      'start': _timestampToJson(instance.start),
      'end': _timestampToJson(instance.end),
      'rent': instance.rent,
    };

_ContactInfo _$ContactInfoFromJson(Map<String, dynamic> json) => _ContactInfo(
  phone: json['phone'] as String,
  email: json['email'] as String,
);

Map<String, dynamic> _$ContactInfoToJson(_ContactInfo instance) =>
    <String, dynamic>{'phone': instance.phone, 'email': instance.email};
