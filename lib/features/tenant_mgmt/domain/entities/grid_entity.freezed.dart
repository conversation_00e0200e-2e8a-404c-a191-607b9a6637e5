// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'grid_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GridEntity {

 String get id; String get code; String? get tenantId; String get size;@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime get createdAt;@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime get updatedAt;
/// Create a copy of GridEntity
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GridEntityCopyWith<GridEntity> get copyWith => _$GridEntityCopyWithImpl<GridEntity>(this as GridEntity, _$identity);

  /// Serializes this GridEntity to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GridEntity&&(identical(other.id, id) || other.id == id)&&(identical(other.code, code) || other.code == code)&&(identical(other.tenantId, tenantId) || other.tenantId == tenantId)&&(identical(other.size, size) || other.size == size)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,code,tenantId,size,createdAt,updatedAt);

@override
String toString() {
  return 'GridEntity(id: $id, code: $code, tenantId: $tenantId, size: $size, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $GridEntityCopyWith<$Res>  {
  factory $GridEntityCopyWith(GridEntity value, $Res Function(GridEntity) _then) = _$GridEntityCopyWithImpl;
@useResult
$Res call({
 String id, String code, String? tenantId, String size,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime createdAt,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime updatedAt
});




}
/// @nodoc
class _$GridEntityCopyWithImpl<$Res>
    implements $GridEntityCopyWith<$Res> {
  _$GridEntityCopyWithImpl(this._self, this._then);

  final GridEntity _self;
  final $Res Function(GridEntity) _then;

/// Create a copy of GridEntity
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? code = null,Object? tenantId = freezed,Object? size = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String,tenantId: freezed == tenantId ? _self.tenantId : tenantId // ignore: cast_nullable_to_non_nullable
as String?,size: null == size ? _self.size : size // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _GridEntity implements GridEntity {
  const _GridEntity({required this.id, required this.code, this.tenantId, required this.size, @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required this.createdAt, @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required this.updatedAt});
  factory _GridEntity.fromJson(Map<String, dynamic> json) => _$GridEntityFromJson(json);

@override final  String id;
@override final  String code;
@override final  String? tenantId;
@override final  String size;
@override@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) final  DateTime createdAt;
@override@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) final  DateTime updatedAt;

/// Create a copy of GridEntity
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GridEntityCopyWith<_GridEntity> get copyWith => __$GridEntityCopyWithImpl<_GridEntity>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GridEntityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GridEntity&&(identical(other.id, id) || other.id == id)&&(identical(other.code, code) || other.code == code)&&(identical(other.tenantId, tenantId) || other.tenantId == tenantId)&&(identical(other.size, size) || other.size == size)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,code,tenantId,size,createdAt,updatedAt);

@override
String toString() {
  return 'GridEntity(id: $id, code: $code, tenantId: $tenantId, size: $size, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$GridEntityCopyWith<$Res> implements $GridEntityCopyWith<$Res> {
  factory _$GridEntityCopyWith(_GridEntity value, $Res Function(_GridEntity) _then) = __$GridEntityCopyWithImpl;
@override @useResult
$Res call({
 String id, String code, String? tenantId, String size,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime createdAt,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime updatedAt
});




}
/// @nodoc
class __$GridEntityCopyWithImpl<$Res>
    implements _$GridEntityCopyWith<$Res> {
  __$GridEntityCopyWithImpl(this._self, this._then);

  final _GridEntity _self;
  final $Res Function(_GridEntity) _then;

/// Create a copy of GridEntity
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? code = null,Object? tenantId = freezed,Object? size = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_GridEntity(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String,tenantId: freezed == tenantId ? _self.tenantId : tenantId // ignore: cast_nullable_to_non_nullable
as String?,size: null == size ? _self.size : size // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
