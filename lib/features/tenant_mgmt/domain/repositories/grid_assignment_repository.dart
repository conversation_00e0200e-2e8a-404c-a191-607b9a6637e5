import 'package:grid_pos/features/tenant_mgmt/data/datasources/grid_assignment_service.dart';

/// Repository for grid assignment operations
abstract class GridAssignmentRepository {
  /// Assign a grid to a tenant
  Future<void> assignGridToTenant(String storeId, String gridId, String tenantId);

  /// Unassign a grid from a tenant
  Future<void> unassignGridFromTenant(String storeId, String gridId, String tenantId);

  /// Bulk assign multiple grids to a tenant
  Future<void> bulkAssignGridsToTenant(String storeId, List<String> gridIds, String tenantId);

  /// Bulk unassign multiple grids from a tenant
  Future<void> bulkUnassignGridsFromTenant(String storeId, List<String> gridIds, String tenantId);
}

/// Implementation of GridAssignmentRepository
class GridAssignmentRepositoryImpl implements GridAssignmentRepository {
  final GridAssignmentService _service;

  /// Constructor
  GridAssignmentRepositoryImpl({GridAssignmentService? service}) : _service = service ?? GridAssignmentService();

  @override
  Future<void> assignGridToTenant(String storeId, String gridId, String tenantId) {
    return _service.assignGridToTenant(storeId, gridId, tenantId);
  }

  @override
  Future<void> unassignGridFromTenant(String storeId, String gridId, String tenantId) {
    return _service.unassignGridFromTenant(storeId, gridId, tenantId);
  }

  @override
  Future<void> bulkAssignGridsToTenant(String storeId, List<String> gridIds, String tenantId) {
    return _service.bulkAssignGridsToTenant(storeId, gridIds, tenantId);
  }

  @override
  Future<void> bulkUnassignGridsFromTenant(String storeId, List<String> gridIds, String tenantId) {
    return _service.bulkUnassignGridsFromTenant(storeId, gridIds, tenantId);
  }
}
