import 'package:grid_pos/features/tenant_mgmt/domain/entities/grid_entity.dart';

/// Repository interface for grid management operations
abstract class GridRepository {
  /// Watch all grids for a specific store
  Stream<List<GridEntity>> watchGrids(String storeId);

  /// Watch a single grid by ID
  Stream<GridEntity?> watchGrid(String storeId, String gridId);

  /// Watch available grids (not assigned to any tenant)
  Stream<List<GridEntity>> watchAvailableGrids(String storeId);

  /// Watch grids assigned to a specific tenant
  Stream<List<GridEntity>> watchGridsByTenant(String storeId, String tenantId);

  /// Watch grids by size
  Stream<List<GridEntity>> watchGridsBySize(String storeId, String size);

  /// Update grid tenant assignment
  Future<void> updateGridTenant(String storeId, String gridId, String? tenantId);

  /// Create a new grid and return its ID
  Future<String> addGrid(String storeId, GridEntity grid);

  /// Update grid details (code, size, etc.)
  Future<void> updateGridDetails(String storeId, String gridId, GridEntity grid);

  /// Delete a grid (only if not assigned to a tenant)
  Future<void> deleteGrid(String storeId, String gridId);
}
