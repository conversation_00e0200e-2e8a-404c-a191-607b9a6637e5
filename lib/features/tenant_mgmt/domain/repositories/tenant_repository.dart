import 'package:grid_pos/features/tenant_mgmt/domain/entities/tenant_entity.dart';

/// Repository interface for tenant management operations
abstract class TenantRepository {
  /// Watch all tenants for a specific store
  Stream<List<TenantEntity>> watchTenants(String storeId);

  /// Watch a single tenant by ID
  Stream<TenantEntity?> watchTenant(String storeId, String tenantId);

  /// Watch tenants filtered by active status
  Stream<List<TenantEntity>> watchTenantsByStatus(String storeId, bool active);

  /// Search tenants by name
  Stream<List<TenantEntity>> searchTenantsByName(String storeId, String query);

  /// Add a new tenant and return its ID
  Future<String> addTenant(String storeId, TenantEntity tenant);

  /// Update an existing tenant
  Future<void> updateTenant(String storeId, String tenantId, TenantEntity tenant);

  /// Delete a tenant
  Future<void> deleteTenant(String storeId, String tenantId);

  /// Assign a grid to a tenant with transaction
  Future<void> assignGridToTenantTransaction(String storeId, String gridId, String tenantId);

  /// Unassign a grid from a tenant with transaction
  Future<void> unassignGridFromTenantTransaction(String storeId, String gridId, String tenantId);

  /// Synchronize tenant-grid relationships
  Future<void> syncTenantsAndGrids(String storeId);
}
