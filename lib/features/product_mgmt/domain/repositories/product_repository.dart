import 'package:grid_pos/features/product_mgmt/domain/entities/product_entity.dart';

/// Interface for product repository operations
abstract class ProductRepository {
  /// Create a new product
  Future<String> createProduct(String storeId, String tenantId, ProductEntity product);

  /// Get a stream of a specific product
  Stream<ProductEntity?> watchProduct(String storeId, String tenantId, String productId);

  /// Get a stream of all products for a specific tenant
  Stream<List<ProductEntity>> watchProductsByTenant(String storeId, String tenantId);

  /// Get a stream of all products for a specific store (admin view)
  Stream<List<ProductEntity>> watchProductsByStore(String storeId);

  /// Watch products by barcode
  Stream<List<ProductEntity>> watchProductsByBarcode(String storeId, String barcode);

  /// Get a product directly by barcode (more efficient than stream)
  Future<ProductEntity?> getProductByBarcode(String storeId, String barcode);

  /// Update an existing product
  Future<void> updateProduct(String storeId, String tenantId, ProductEntity product);

  /// Delete a product
  Future<void> deleteProduct(String storeId, String tenantId, String productId);

  /// Check if a barcode exists in the store (should be unique per store)
  Future<bool> checkBarcodeExists(String storeId, String barcode, {String? excludeProductId});

  /// Check if an SKU exists for tenant (should be unique per tenant within a store)
  Future<bool> checkSkuExists(String storeId, String tenantId, String sku, {String? excludeProductId});
}
