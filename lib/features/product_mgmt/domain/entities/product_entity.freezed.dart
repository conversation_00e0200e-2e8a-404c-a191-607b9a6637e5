// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductEntity {

/// Unique identifier of the product
 String get id;/// Stock Keeping Unit (unique for tenant)
 String get sku;/// Product name
 String get name;/// Product barcode (unique for store)
 String get barcode;/// Selling price
 double get price;/// Cost price (optional, visible to admin/tenant)
 double? get cost;/// Current stock quantity
 int get stock;/// ID of the grid where product is located
 String get gridId;/// Low stock alert threshold
 int get lowStockLevel;/// Whether product is active/available for sale
 bool get active;/// Tenant ID that owns this product
 String get tenantId;/// Store ID where product is located
 String get storeId;/// When the product was created
@JsonKey(fromJson: TimestampUtils.timestampToDateTime, toJson: TimestampUtils.dateTimeToTimestamp) DateTime? get createdAt;/// When the product was last updated
@JsonKey(fromJson: TimestampUtils.timestampToDateTime, toJson: TimestampUtils.dateTimeToTimestamp) DateTime? get updatedAt;
/// Create a copy of ProductEntity
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductEntityCopyWith<ProductEntity> get copyWith => _$ProductEntityCopyWithImpl<ProductEntity>(this as ProductEntity, _$identity);

  /// Serializes this ProductEntity to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductEntity&&(identical(other.id, id) || other.id == id)&&(identical(other.sku, sku) || other.sku == sku)&&(identical(other.name, name) || other.name == name)&&(identical(other.barcode, barcode) || other.barcode == barcode)&&(identical(other.price, price) || other.price == price)&&(identical(other.cost, cost) || other.cost == cost)&&(identical(other.stock, stock) || other.stock == stock)&&(identical(other.gridId, gridId) || other.gridId == gridId)&&(identical(other.lowStockLevel, lowStockLevel) || other.lowStockLevel == lowStockLevel)&&(identical(other.active, active) || other.active == active)&&(identical(other.tenantId, tenantId) || other.tenantId == tenantId)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sku,name,barcode,price,cost,stock,gridId,lowStockLevel,active,tenantId,storeId,createdAt,updatedAt);

@override
String toString() {
  return 'ProductEntity(id: $id, sku: $sku, name: $name, barcode: $barcode, price: $price, cost: $cost, stock: $stock, gridId: $gridId, lowStockLevel: $lowStockLevel, active: $active, tenantId: $tenantId, storeId: $storeId, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $ProductEntityCopyWith<$Res>  {
  factory $ProductEntityCopyWith(ProductEntity value, $Res Function(ProductEntity) _then) = _$ProductEntityCopyWithImpl;
@useResult
$Res call({
 String id, String sku, String name, String barcode, double price, double? cost, int stock, String gridId, int lowStockLevel, bool active, String tenantId, String storeId,@JsonKey(fromJson: TimestampUtils.timestampToDateTime, toJson: TimestampUtils.dateTimeToTimestamp) DateTime? createdAt,@JsonKey(fromJson: TimestampUtils.timestampToDateTime, toJson: TimestampUtils.dateTimeToTimestamp) DateTime? updatedAt
});




}
/// @nodoc
class _$ProductEntityCopyWithImpl<$Res>
    implements $ProductEntityCopyWith<$Res> {
  _$ProductEntityCopyWithImpl(this._self, this._then);

  final ProductEntity _self;
  final $Res Function(ProductEntity) _then;

/// Create a copy of ProductEntity
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? sku = null,Object? name = null,Object? barcode = null,Object? price = null,Object? cost = freezed,Object? stock = null,Object? gridId = null,Object? lowStockLevel = null,Object? active = null,Object? tenantId = null,Object? storeId = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sku: null == sku ? _self.sku : sku // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,barcode: null == barcode ? _self.barcode : barcode // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,cost: freezed == cost ? _self.cost : cost // ignore: cast_nullable_to_non_nullable
as double?,stock: null == stock ? _self.stock : stock // ignore: cast_nullable_to_non_nullable
as int,gridId: null == gridId ? _self.gridId : gridId // ignore: cast_nullable_to_non_nullable
as String,lowStockLevel: null == lowStockLevel ? _self.lowStockLevel : lowStockLevel // ignore: cast_nullable_to_non_nullable
as int,active: null == active ? _self.active : active // ignore: cast_nullable_to_non_nullable
as bool,tenantId: null == tenantId ? _self.tenantId : tenantId // ignore: cast_nullable_to_non_nullable
as String,storeId: null == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as String,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ProductEntity extends ProductEntity {
  const _ProductEntity({required this.id, required this.sku, required this.name, required this.barcode, required this.price, this.cost, required this.stock, required this.gridId, this.lowStockLevel = 5, this.active = true, required this.tenantId, required this.storeId, @JsonKey(fromJson: TimestampUtils.timestampToDateTime, toJson: TimestampUtils.dateTimeToTimestamp) this.createdAt, @JsonKey(fromJson: TimestampUtils.timestampToDateTime, toJson: TimestampUtils.dateTimeToTimestamp) this.updatedAt}): super._();
  factory _ProductEntity.fromJson(Map<String, dynamic> json) => _$ProductEntityFromJson(json);

/// Unique identifier of the product
@override final  String id;
/// Stock Keeping Unit (unique for tenant)
@override final  String sku;
/// Product name
@override final  String name;
/// Product barcode (unique for store)
@override final  String barcode;
/// Selling price
@override final  double price;
/// Cost price (optional, visible to admin/tenant)
@override final  double? cost;
/// Current stock quantity
@override final  int stock;
/// ID of the grid where product is located
@override final  String gridId;
/// Low stock alert threshold
@override@JsonKey() final  int lowStockLevel;
/// Whether product is active/available for sale
@override@JsonKey() final  bool active;
/// Tenant ID that owns this product
@override final  String tenantId;
/// Store ID where product is located
@override final  String storeId;
/// When the product was created
@override@JsonKey(fromJson: TimestampUtils.timestampToDateTime, toJson: TimestampUtils.dateTimeToTimestamp) final  DateTime? createdAt;
/// When the product was last updated
@override@JsonKey(fromJson: TimestampUtils.timestampToDateTime, toJson: TimestampUtils.dateTimeToTimestamp) final  DateTime? updatedAt;

/// Create a copy of ProductEntity
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductEntityCopyWith<_ProductEntity> get copyWith => __$ProductEntityCopyWithImpl<_ProductEntity>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductEntityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductEntity&&(identical(other.id, id) || other.id == id)&&(identical(other.sku, sku) || other.sku == sku)&&(identical(other.name, name) || other.name == name)&&(identical(other.barcode, barcode) || other.barcode == barcode)&&(identical(other.price, price) || other.price == price)&&(identical(other.cost, cost) || other.cost == cost)&&(identical(other.stock, stock) || other.stock == stock)&&(identical(other.gridId, gridId) || other.gridId == gridId)&&(identical(other.lowStockLevel, lowStockLevel) || other.lowStockLevel == lowStockLevel)&&(identical(other.active, active) || other.active == active)&&(identical(other.tenantId, tenantId) || other.tenantId == tenantId)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,sku,name,barcode,price,cost,stock,gridId,lowStockLevel,active,tenantId,storeId,createdAt,updatedAt);

@override
String toString() {
  return 'ProductEntity(id: $id, sku: $sku, name: $name, barcode: $barcode, price: $price, cost: $cost, stock: $stock, gridId: $gridId, lowStockLevel: $lowStockLevel, active: $active, tenantId: $tenantId, storeId: $storeId, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$ProductEntityCopyWith<$Res> implements $ProductEntityCopyWith<$Res> {
  factory _$ProductEntityCopyWith(_ProductEntity value, $Res Function(_ProductEntity) _then) = __$ProductEntityCopyWithImpl;
@override @useResult
$Res call({
 String id, String sku, String name, String barcode, double price, double? cost, int stock, String gridId, int lowStockLevel, bool active, String tenantId, String storeId,@JsonKey(fromJson: TimestampUtils.timestampToDateTime, toJson: TimestampUtils.dateTimeToTimestamp) DateTime? createdAt,@JsonKey(fromJson: TimestampUtils.timestampToDateTime, toJson: TimestampUtils.dateTimeToTimestamp) DateTime? updatedAt
});




}
/// @nodoc
class __$ProductEntityCopyWithImpl<$Res>
    implements _$ProductEntityCopyWith<$Res> {
  __$ProductEntityCopyWithImpl(this._self, this._then);

  final _ProductEntity _self;
  final $Res Function(_ProductEntity) _then;

/// Create a copy of ProductEntity
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? sku = null,Object? name = null,Object? barcode = null,Object? price = null,Object? cost = freezed,Object? stock = null,Object? gridId = null,Object? lowStockLevel = null,Object? active = null,Object? tenantId = null,Object? storeId = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_ProductEntity(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,sku: null == sku ? _self.sku : sku // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,barcode: null == barcode ? _self.barcode : barcode // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,cost: freezed == cost ? _self.cost : cost // ignore: cast_nullable_to_non_nullable
as double?,stock: null == stock ? _self.stock : stock // ignore: cast_nullable_to_non_nullable
as int,gridId: null == gridId ? _self.gridId : gridId // ignore: cast_nullable_to_non_nullable
as String,lowStockLevel: null == lowStockLevel ? _self.lowStockLevel : lowStockLevel // ignore: cast_nullable_to_non_nullable
as int,active: null == active ? _self.active : active // ignore: cast_nullable_to_non_nullable
as bool,tenantId: null == tenantId ? _self.tenantId : tenantId // ignore: cast_nullable_to_non_nullable
as String,storeId: null == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as String,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
