import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:grid_pos/core/utils/timestamp_utils.dart';

part 'product_entity.freezed.dart';
part 'product_entity.g.dart';

/// Helper function to convert Timestamp from Firestore to DateTime
DateTime? _timestampFromJson(Timestamp? timestamp) => timestamp?.toDate();

/// Helper function to convert DateTime to Timestamp for Firestore
Timestamp? _timestampToJson(DateTime? dateTime) => dateTime != null ? Timestamp.fromDate(dateTime) : null;

/// Product entity class representing a product in the domain layer
@freezed
abstract class ProductEntity with _$ProductEntity {
  /// Private constructor with custom methods
  const ProductEntity._();

  /// Default constructor with required fields
  const factory ProductEntity({
    /// Unique identifier of the product
    required String id,

    /// Stock Keeping Unit (unique for tenant)
    required String sku,

    /// Product name
    required String name,

    /// Product barcode (unique for store)
    required String barcode,

    /// Selling price
    required double price,

    /// Cost price (optional, visible to admin/tenant)
    double? cost,

    /// Current stock quantity
    required int stock,

    /// ID of the grid where product is located
    required String gridId,

    /// Low stock alert threshold
    @Default(5) int lowStockLevel,

    /// Whether product is active/available for sale
    @Default(true) bool active,

    /// Tenant ID that owns this product
    required String tenantId,

    /// Store ID where product is located
    required String storeId,

    /// When the product was created
    @JsonKey(fromJson: TimestampUtils.timestampToDateTime, toJson: TimestampUtils.dateTimeToTimestamp) DateTime? createdAt,

    /// When the product was last updated
    @JsonKey(fromJson: TimestampUtils.timestampToDateTime, toJson: TimestampUtils.dateTimeToTimestamp) DateTime? updatedAt,
  }) = _ProductEntity;

  /// Helper method to check if stock is low
  bool isLowStock() => stock <= lowStockLevel;

  /// Factory to create from JSON
  factory ProductEntity.fromJson(Map<String, dynamic> json) => _$ProductEntityFromJson(json);
}
