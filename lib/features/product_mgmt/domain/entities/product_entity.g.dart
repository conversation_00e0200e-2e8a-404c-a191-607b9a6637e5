// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ProductEntity _$ProductEntityFromJson(Map<String, dynamic> json) =>
    _ProductEntity(
      id: json['id'] as String,
      sku: json['sku'] as String,
      name: json['name'] as String,
      barcode: json['barcode'] as String,
      price: (json['price'] as num).toDouble(),
      cost: (json['cost'] as num?)?.toDouble(),
      stock: (json['stock'] as num).toInt(),
      gridId: json['gridId'] as String,
      lowStockLevel: (json['lowStockLevel'] as num?)?.toInt() ?? 5,
      active: json['active'] as bool? ?? true,
      tenantId: json['tenantId'] as String,
      storeId: json['storeId'] as String,
      createdAt: TimestampUtils.timestampToDateTime(
        json['createdAt'] as Timestamp?,
      ),
      updatedAt: TimestampUtils.timestampToDateTime(
        json['updatedAt'] as Timestamp?,
      ),
    );

Map<String, dynamic> _$ProductEntityToJson(_ProductEntity instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sku': instance.sku,
      'name': instance.name,
      'barcode': instance.barcode,
      'price': instance.price,
      'cost': instance.cost,
      'stock': instance.stock,
      'gridId': instance.gridId,
      'lowStockLevel': instance.lowStockLevel,
      'active': instance.active,
      'tenantId': instance.tenantId,
      'storeId': instance.storeId,
      'createdAt': TimestampUtils.dateTimeToTimestamp(instance.createdAt),
      'updatedAt': TimestampUtils.dateTimeToTimestamp(instance.updatedAt),
    };
