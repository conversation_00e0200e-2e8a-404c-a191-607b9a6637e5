import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/product_mgmt/data/datasources/product_remote_datasource.dart';
import 'package:grid_pos/features/product_mgmt/domain/entities/product_entity.dart';
import 'package:grid_pos/features/product_mgmt/domain/repositories/product_repository.dart';

/// Implementation of ProductRepository using ProductRemoteDataSource
class ProductRepositoryImpl implements ProductRepository {
  final ProductRemoteDataSource _remoteDataSource;

  /// Constructor for ProductRepositoryImpl
  ProductRepositoryImpl(this._remoteDataSource);

  @override
  Future<String> createProduct(String storeId, String tenantId, ProductEntity product) async {
    try {
      Logger.debug('Creating product through repository: storeId=$storeId, tenantId=$tenantId');

      // Ensure product has the correct storeId and tenantId
      final productWithIds = product.copyWith(storeId: storeId, tenantId: tenantId, createdAt: DateTime.now(), updatedAt: DateTime.now());

      return await _remoteDataSource.createProduct(storeId, tenantId, productWithIds);
    } catch (e) {
      Logger.error('Repository error creating product', e);
      rethrow;
    }
  }

  @override
  Stream<ProductEntity?> watchProduct(String storeId, String tenantId, String productId) {
    Logger.debug('Watching product through repository: storeId=$storeId, tenantId=$tenantId, productId=$productId');
    return _remoteDataSource.watchProduct(storeId, tenantId, productId);
  }

  @override
  Stream<List<ProductEntity>> watchProductsByTenant(String storeId, String tenantId) {
    Logger.debug('Watching tenant products through repository: storeId=$storeId, tenantId=$tenantId');
    return _remoteDataSource.watchProductsByTenant(storeId, tenantId);
  }

  @override
  Stream<List<ProductEntity>> watchProductsByStore(String storeId) {
    Logger.debug('Watching store products through repository: storeId=$storeId');
    return _remoteDataSource.watchProductsByStore(storeId);
  }

  @override
  Future<void> updateProduct(String storeId, String tenantId, ProductEntity product) async {
    try {
      Logger.debug('Updating product through repository: storeId=$storeId, tenantId=$tenantId, productId=${product.id}');

      // Ensure product has the correct storeId, tenantId and updated timestamp
      final productWithIds = product.copyWith(storeId: storeId, tenantId: tenantId, updatedAt: DateTime.now());

      await _remoteDataSource.updateProduct(storeId, tenantId, productWithIds);
    } catch (e) {
      Logger.error('Repository error updating product', e);
      rethrow;
    }
  }

  @override
  Future<void> deleteProduct(String storeId, String tenantId, String productId) async {
    try {
      Logger.debug('Deleting product through repository: storeId=$storeId, tenantId=$tenantId, productId=$productId');
      await _remoteDataSource.deleteProduct(storeId, tenantId, productId);
    } catch (e) {
      Logger.error('Repository error deleting product', e);
      rethrow;
    }
  }

  @override
  Future<bool> checkBarcodeExists(String storeId, String barcode, {String? excludeProductId}) async {
    try {
      Logger.debug('Checking barcode exists through repository: storeId=$storeId, barcode=$barcode');
      return await _remoteDataSource.checkBarcodeExists(storeId, barcode, excludeProductId: excludeProductId);
    } catch (e) {
      Logger.error('Repository error checking barcode', e);
      rethrow;
    }
  }

  @override
  Future<bool> checkSkuExists(String storeId, String tenantId, String sku, {String? excludeProductId}) async {
    try {
      Logger.debug('Checking SKU exists through repository: storeId=$storeId, tenantId=$tenantId, sku=$sku');
      return await _remoteDataSource.checkSkuExists(storeId, tenantId, sku, excludeProductId: excludeProductId);
    } catch (e) {
      Logger.error('Repository error checking SKU', e);
      rethrow;
    }
  }

  @override
  Stream<List<ProductEntity>> watchProductsByBarcode(String storeId, String barcode) {
    return _remoteDataSource.watchProductsByBarcode(storeId, barcode);
  }

  @override
  Future<ProductEntity?> getProductByBarcode(String storeId, String barcode) async {
    try {
      Logger.debug('Getting product by barcode through repository: storeId=$storeId, barcode=$barcode');
      return await _remoteDataSource.getProductByBarcode(storeId, barcode);
    } catch (e) {
      Logger.error('Repository error getting product by barcode', e);
      rethrow;
    }
  }
}
