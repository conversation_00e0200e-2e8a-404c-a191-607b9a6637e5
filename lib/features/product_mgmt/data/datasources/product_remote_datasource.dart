import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:grid_pos/core/constants/firestore_constants.dart';
import 'package:grid_pos/core/errors/failure.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/product_mgmt/data/models/product_dto.dart';
import 'package:grid_pos/features/product_mgmt/domain/entities/product_entity.dart';

/// Interface for product remote data source operations
abstract class ProductRemoteDataSource {
  /// Create a new product document in Firestore
  Future<String> createProduct(String storeId, String tenantId, ProductEntity product);

  /// Watch a specific product for changes
  Stream<ProductEntity?> watchProduct(String storeId, String tenantId, String productId);

  /// Watch all products for a specific tenant
  Stream<List<ProductEntity>> watchProductsByTenant(String storeId, String tenantId);

  /// Watch all products for a specific store (admin view)
  Stream<List<ProductEntity>> watchProductsByStore(String storeId);

  /// Watch all products matching a barcode
  Stream<List<ProductEntity>> watchProductsByBarcode(String storeId, String barcode);

  /// Get a product directly by barcode (more efficient for direct queries)
  Future<ProductEntity?> getProductByBarcode(String storeId, String barcode);

  /// Update an existing product document
  Future<void> updateProduct(String storeId, String tenantId, ProductEntity product);

  /// Delete a product document
  Future<void> deleteProduct(String storeId, String tenantId, String productId);

  /// Check if a barcode exists in the store (should be unique per store)
  Future<bool> checkBarcodeExists(String storeId, String barcode, {String? excludeProductId});

  /// Check if an SKU exists for tenant (should be unique per tenant within a store)
  Future<bool> checkSkuExists(String storeId, String tenantId, String sku, {String? excludeProductId});
}

/// Implementation of [ProductRemoteDataSource] using Firebase Firestore
class ProductRemoteDataSourceImpl implements ProductRemoteDataSource {
  final FirebaseFirestore _firestore;

  /// Constructor for [ProductRemoteDataSourceImpl]
  ProductRemoteDataSourceImpl(this._firestore);

  @override
  Future<String> createProduct(String storeId, String tenantId, ProductEntity product) async {
    try {
      Logger.debug('Creating product in Firestore: storeId=$storeId, tenantId=$tenantId');

      final productsCollection = _getProductsCollection(storeId, tenantId);
      final docRef = await productsCollection.add(ProductDto.toFirestore(product));

      Logger.debug('Product created with ID: ${docRef.id}');
      return docRef.id;
    } on FirebaseException catch (e) {
      Logger.error('Firebase error creating product', e);
      throw _handleFirebaseException(e);
    } catch (e) {
      Logger.error('Error creating product', e);
      throw FirestoreFailure(message: 'Failed to create product: ${e.toString()}', originalError: e);
    }
  }

  @override
  Stream<ProductEntity?> watchProduct(String storeId, String tenantId, String productId) {
    Logger.debug('Watching product: storeId=$storeId, tenantId=$tenantId, productId=$productId');

    try {
      // 如果tenantId为空，通过collectionGroup方式查询
      if (tenantId.isEmpty) {
        Logger.debug('tenantId为空，使用collectionGroup查询商品: $productId');

        // 使用collectionGroup查询所有租户下的产品，然后手动过滤productId
        return _firestore
            .collectionGroup(FirestoreConstants.products)
            .where(FirestoreConstants.storeId, isEqualTo: storeId)
            .snapshots()
            .map((snapshot) {
              // 手动过滤文档ID
              final matchingDocs = snapshot.docs.where((doc) => doc.id == productId).toList();
              if (matchingDocs.isNotEmpty) {
                final doc = matchingDocs.first;
                Logger.debug('成功通过collectionGroup查找到商品: $productId, tenantId: ${doc.reference.parent.parent?.id}');
                return ProductDto.fromFirestore(doc);
              }
              Logger.debug('未找到商品: $productId');
              return null;
            })
            .handleError((error) {
              Logger.error('使用collectionGroup查询商品时出错: $productId', error);
              if (error is FirebaseException) {
                throw _handleFirebaseException(error);
              }
              throw FirestoreFailure(message: '商品查询失败: ${error.toString()}', originalError: error);
            });
      }

      // 有tenantId，直接查询指定租户的产品集合
      return _getProductsCollection(storeId, tenantId)
          .doc(productId)
          .snapshots()
          .map((snapshot) {
            if (snapshot.exists) {
              return ProductDto.fromFirestore(snapshot);
            }
            return null;
          })
          .handleError((error) {
            Logger.error('Error watching product: $productId', error);
            if (error is FirebaseException) {
              throw _handleFirebaseException(error);
            }
            throw FirestoreFailure(message: 'Failed to watch product: ${error.toString()}', originalError: error);
          });
    } catch (e) {
      Logger.error('Error setting up product watch stream', e);
      throw FirestoreFailure(message: 'Failed to watch product: ${e.toString()}', originalError: e);
    }
  }

  @override
  Stream<List<ProductEntity>> watchProductsByTenant(String storeId, String tenantId) {
    Logger.debug('Watching products by tenant: storeId=$storeId, tenantId=$tenantId');

    try {
      return _getProductsCollection(storeId, tenantId).snapshots().map((snapshot) => snapshot.docs.map((doc) => ProductDto.fromFirestore(doc)).toList()).handleError((error) {
        Logger.error('Error watching products by tenant', error);
        if (error is FirebaseException) {
          throw _handleFirebaseException(error);
        }
        throw FirestoreFailure(message: 'Failed to watch tenant products: ${error.toString()}', originalError: error);
      });
    } catch (e) {
      Logger.error('Error setting up tenant products watch stream', e);
      throw FirestoreFailure(message: 'Failed to watch tenant products: ${e.toString()}', originalError: e);
    }
  }

  @override
  Stream<List<ProductEntity>> watchProductsByStore(String storeId) {
    Logger.debug('Watching all products in store: storeId=$storeId');

    try {
      // For watching all products in a store, we need to query across tenant collections
      // This requires a different approach since we can't directly query across subcollections

      // One approach is to use collectionGroup, but we need to filter by storeId
      return _firestore
          .collectionGroup(FirestoreConstants.products)
          .where(FirestoreConstants.storeId, isEqualTo: storeId)
          .snapshots()
          .map((snapshot) => snapshot.docs.map((doc) => ProductDto.fromFirestore(doc)).toList())
          .handleError((error) {
            Logger.error('Error watching products by store', error);
            if (error is FirebaseException) {
              throw _handleFirebaseException(error);
            }
            throw FirestoreFailure(message: 'Failed to watch store products: ${error.toString()}', originalError: error);
          });
    } catch (e) {
      Logger.error('Error setting up store products watch stream', e);
      throw FirestoreFailure(message: 'Failed to watch store products: ${e.toString()}', originalError: e);
    }
  }

  @override
  Future<void> updateProduct(String storeId, String tenantId, ProductEntity product) async {
    try {
      Logger.debug('Updating product: storeId=$storeId, tenantId=$tenantId, productId=${product.id}');

      await _getProductsCollection(storeId, tenantId).doc(product.id).update(ProductDto.toFirestore(product));

      Logger.debug('Product updated successfully');
    } on FirebaseException catch (e) {
      Logger.error('Firebase error updating product', e);
      throw _handleFirebaseException(e);
    } catch (e) {
      Logger.error('Error updating product', e);
      throw FirestoreFailure(message: 'Failed to update product: ${e.toString()}', originalError: e);
    }
  }

  @override
  Future<void> deleteProduct(String storeId, String tenantId, String productId) async {
    try {
      Logger.debug('Deleting product: storeId=$storeId, tenantId=$tenantId, productId=$productId');

      await _getProductsCollection(storeId, tenantId).doc(productId).delete();

      Logger.debug('Product deleted successfully');
    } on FirebaseException catch (e) {
      Logger.error('Firebase error deleting product', e);
      throw _handleFirebaseException(e);
    } catch (e) {
      Logger.error('Error deleting product', e);
      throw FirestoreFailure(message: 'Failed to delete product: ${e.toString()}', originalError: e);
    }
  }

  @override
  Future<bool> checkBarcodeExists(String storeId, String barcode, {String? excludeProductId}) async {
    try {
      Logger.debug('Checking barcode uniqueness: storeId=$storeId, barcode=$barcode');

      // Query across all products in the store (across tenants)
      final snapshot = await _firestore.collectionGroup(FirestoreConstants.products).where(FirestoreConstants.storeId, isEqualTo: storeId).where(FirestoreConstants.barcode, isEqualTo: barcode).get();

      // If we're excluding a product ID (e.g., during update), filter it out
      final matchingDocs = snapshot.docs.where((doc) => doc.id != excludeProductId).toList();

      return matchingDocs.isNotEmpty;
    } on FirebaseException catch (e) {
      Logger.error('Firebase error checking barcode existence', e);
      throw _handleFirebaseException(e);
    } catch (e) {
      Logger.error('Error checking barcode existence', e);
      throw FirestoreFailure(message: 'Failed to check barcode uniqueness: ${e.toString()}', originalError: e);
    }
  }

  @override
  Future<bool> checkSkuExists(String storeId, String tenantId, String sku, {String? excludeProductId}) async {
    try {
      Logger.debug('Checking SKU uniqueness: storeId=$storeId, tenantId=$tenantId, sku=$sku');

      // Query only within tenant's products
      final snapshot = await _getProductsCollection(storeId, tenantId).where(FirestoreConstants.sku, isEqualTo: sku).get();

      // If we're excluding a product ID (e.g., during update), filter it out
      final matchingDocs = snapshot.docs.where((doc) => doc.id != excludeProductId).toList();

      return matchingDocs.isNotEmpty;
    } on FirebaseException catch (e) {
      Logger.error('Firebase error checking SKU existence', e);
      throw _handleFirebaseException(e);
    } catch (e) {
      Logger.error('Error checking SKU existence', e);
      throw FirestoreFailure(message: 'Failed to check SKU uniqueness: ${e.toString()}', originalError: e);
    }
  }

  @override
  Stream<List<ProductEntity>> watchProductsByBarcode(String storeId, String barcode) {
    Logger.debug('Watching products by barcode: $barcode in store: $storeId');

    return _firestore
        .collectionGroup(FirestoreConstants.products)
        .where(FirestoreConstants.storeId, isEqualTo: storeId)
        .where(FirestoreConstants.barcode, isEqualTo: barcode)
        .where(FirestoreConstants.active, isEqualTo: true)
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => ProductDto.fromFirestore(doc)).toList());
  }

  @override
  Future<ProductEntity?> getProductByBarcode(String storeId, String barcode) async {
    Logger.debug('Getting product by barcode: $barcode in store: $storeId');

    try {
      final snapshot =
          await _firestore
              .collectionGroup(FirestoreConstants.products)
              .where(FirestoreConstants.storeId, isEqualTo: storeId)
              .where(FirestoreConstants.barcode, isEqualTo: barcode)
              .where(FirestoreConstants.active, isEqualTo: true)
              .limit(1) // 限制只返回一個結果以提高效率
              .get();

      if (snapshot.docs.isEmpty) {
        return null;
      }

      return ProductDto.fromFirestore(snapshot.docs.first);
    } on FirebaseException catch (e) {
      Logger.error('Firebase error getting product by barcode', e);
      throw _handleFirebaseException(e);
    } catch (e) {
      Logger.error('Error getting product by barcode', e);
      throw FirestoreFailure(message: 'Failed to get product by barcode: ${e.toString()}', originalError: e);
    }
  }

  /// Helper method to get the products collection reference
  CollectionReference<Map<String, dynamic>> _getProductsCollection(String storeId, String tenantId) {
    return _firestore.collection(FirestoreConstants.stores).doc(storeId).collection(FirestoreConstants.tenants).doc(tenantId).collection(FirestoreConstants.products);
  }

  /// Helper method to convert Firebase exceptions to domain failures
  Failure _handleFirebaseException(FirebaseException e) {
    switch (e.code) {
      case 'permission-denied':
        return FirestoreFailure.permissionDenied();
      case 'not-found':
        return FirestoreFailure.documentNotFound();
      case 'unavailable':
      case 'internal':
        return FirestoreFailure.serverError();
      default:
        return FirestoreFailure(message: 'Firestore error: ${e.message ?? e.code}', originalError: e);
    }
  }
}
