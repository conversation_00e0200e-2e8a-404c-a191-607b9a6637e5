import 'package:grid_pos/core/utils/debouncer.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/product_mgmt/domain/repositories/product_repository.dart';

/// Service for validating product attributes like barcode and SKU
class ProductValidationService {
  final ProductRepository _productRepository;
  final Debouncer _barcodeDebouncer;
  final Debouncer _skuDebouncer;

  /// Constructor for ProductValidationService
  ProductValidationService(this._productRepository)
    : _barcodeDebouncer = Debouncer(delay: const Duration(milliseconds: 500)),
      _skuDebouncer = Debouncer(delay: const Duration(milliseconds: 500));

  /// Validates if a barcode is unique within a store
  /// Returns a Future that completes with true if barcode is unique, false otherwise
  /// Use debounce to avoid excessive Firestore reads during typing
  Future<bool> validateBarcodeUniqueness({
    required String storeId,
    required String barcode,
    String? excludeProductId,
    required Function(bool isValid) onResult,
  }) async {
    if (barcode.isEmpty) {
      onResult(false);
      return false;
    }

    // Use debouncer to delay the validation
    _barcodeDebouncer.run(() async {
      try {
        Logger.debug('正在驗證條碼唯一性: $barcode');
        final exists = await _productRepository.checkBarcodeExists(
          storeId,
          barcode,
          excludeProductId: excludeProductId,
        );

        // Call onResult with the opposite of exists (true if barcode doesn't exist/is unique)
        onResult(!exists);
      } catch (e) {
        Logger.error('驗證條碼時發生錯誤', e);
        // In case of error, inform the UI that validation failed
        onResult(false);
      }
    });

    // Return a placeholder value initially
    // The actual result will be delivered via the onResult callback
    return true;
  }

  /// Validates if an SKU is unique within a tenant
  /// Returns a Future that completes with true if SKU is unique, false otherwise
  /// Use debounce to avoid excessive Firestore reads during typing
  Future<bool> validateSkuUniqueness({
    required String storeId,
    required String tenantId,
    required String sku,
    String? excludeProductId,
    required Function(bool isValid) onResult,
  }) async {
    if (sku.isEmpty) {
      onResult(false);
      return false;
    }

    // Use debouncer to delay the validation
    _skuDebouncer.run(() async {
      try {
        Logger.debug('正在驗證SKU唯一性: $sku');
        final exists = await _productRepository.checkSkuExists(
          storeId,
          tenantId,
          sku,
          excludeProductId: excludeProductId,
        );

        // Call onResult with the opposite of exists (true if SKU doesn't exist/is unique)
        onResult(!exists);
      } catch (e) {
        Logger.error('驗證SKU時發生錯誤', e);
        // In case of error, inform the UI that validation failed
        onResult(false);
      }
    });

    // Return a placeholder value initially
    // The actual result will be delivered via the onResult callback
    return true;
  }

  /// Disposes of the debouncers
  void dispose() {
    _barcodeDebouncer.dispose();
    _skuDebouncer.dispose();
  }
}
