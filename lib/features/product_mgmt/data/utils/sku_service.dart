import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:grid_pos/core/constants/firestore_constants.dart';
import 'package:grid_pos/core/errors/failure.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:intl/intl.dart';
import 'dart:math' as math;

/// Failure specific to SKU generation issues
class SkuGenerationFailure extends BusinessFailure {
  const SkuGenerationFailure({required super.message, super.originalError});

  factory SkuGenerationFailure.sequenceError() => const SkuGenerationFailure(message: 'Failed to generate a unique SKU sequence number. Please try again.');

  factory SkuGenerationFailure.generateError(dynamic error) => SkuGenerationFailure(message: 'Failed to generate a unique SKU: ${error.toString()}', originalError: error);
}

/// Service for handling SKU generation and related functionality
class SkuService {
  final FirebaseFirestore _firestore;

  /// Constructor for SkuService
  SkuService(this._firestore);

  /// Generates a unique SKU for a tenant's product
  /// Format: {tenant-code}-{YYYYMMDD}-{sequence}
  /// Example: ABC-20230501-0001
  Future<String> generateSku(String storeId, String tenantId) async {
    try {
      // Get tenant short code (first 3 characters of tenantId as a placeholder)
      // In a real application, you might want to store and use a proper tenant code
      final tenantCode = tenantId.substring(0, math.min(3, tenantId.length)).toUpperCase();

      // Get current date in YYYYMMDD format
      final dateStr = DateFormat('yyyyMMdd').format(DateTime.now());

      // Get next sequence number
      final sequenceNumber = await _getNextSkuNumber(storeId, tenantId);
      final sequenceStr = sequenceNumber.toString().padLeft(4, '0');

      // Combine all parts to form the SKU
      return '$tenantCode-$dateStr-$sequenceStr';
    } catch (e) {
      Logger.error('Error generating SKU', e);
      throw SkuGenerationFailure.generateError(e);
    }
  }

  /// Gets the next sequence number for SKU generation using a distributed counter
  Future<int> _getNextSkuNumber(String storeId, String tenantId) async {
    try {
      // Get a reference to the counter document
      final counterRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.tenants)
          .doc(tenantId)
          .collection(FirestoreConstants.counters)
          .doc(FirestoreConstants.productSkuCounter);

      // Use a transaction to safely increment the counter
      return _firestore.runTransaction<int>((transaction) async {
        final counterDoc = await transaction.get(counterRef);

        if (!counterDoc.exists) {
          // If counter doesn't exist, create it with initial value 1
          transaction.set(counterRef, {'value': 1});
          return 1;
        } else {
          // Get current value and increment
          final currentValue = counterDoc.data()?['value'] as int? ?? 0;
          final nextValue = currentValue + 1;

          // Update the counter
          transaction.update(counterRef, {'value': nextValue});
          return nextValue;
        }
      });
    } catch (e) {
      Logger.error('Error getting next SKU number', e, StackTrace.current);
      throw SkuGenerationFailure.sequenceError();
    }
  }
}
