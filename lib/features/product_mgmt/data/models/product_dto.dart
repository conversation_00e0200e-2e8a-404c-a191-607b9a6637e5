import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:grid_pos/core/constants/firestore_constants.dart';
import 'package:grid_pos/features/product_mgmt/domain/entities/product_entity.dart';

/// Data Transfer Object for Product entities to/from Firestore
class ProductDto {
  /// Convert a Firestore document to a ProductEntity
  static ProductEntity fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data() ?? {};

    return ProductEntity(
      id: doc.id,
      sku: data[FirestoreConstants.sku] as String,
      name: data[FirestoreConstants.name] as String,
      barcode: data[FirestoreConstants.barcode] as String,
      price: (data[FirestoreConstants.price] as num).toDouble(),
      cost: data[FirestoreConstants.cost] != null ? (data[FirestoreConstants.cost] as num).toDouble() : null,
      stock: (data[FirestoreConstants.stock] as num).toInt(),
      gridId: data[FirestoreConstants.gridId] as String,
      lowStockLevel: data[FirestoreConstants.lowStockLevel] != null ? (data[FirestoreConstants.lowStockLevel] as num).toInt() : 5,
      active: data[FirestoreConstants.active] as bool? ?? true,
      tenantId: data[FirestoreConstants.tenantId] as String,
      storeId: data[FirestoreConstants.storeId] as String,
      createdAt: (data[FirestoreConstants.createdAt] as Timestamp?)?.toDate(),
      updatedAt: (data[FirestoreConstants.updatedAt] as Timestamp?)?.toDate(),
    );
  }

  /// Convert a ProductEntity to a Firestore document map
  static Map<String, dynamic> toFirestore(ProductEntity product) {
    final now = DateTime.now();

    return {
      FirestoreConstants.sku: product.sku,
      FirestoreConstants.name: product.name,
      FirestoreConstants.barcode: product.barcode,
      FirestoreConstants.price: product.price,
      FirestoreConstants.cost: product.cost,
      FirestoreConstants.stock: product.stock,
      FirestoreConstants.gridId: product.gridId,
      FirestoreConstants.lowStockLevel: product.lowStockLevel,
      FirestoreConstants.active: product.active,
      FirestoreConstants.tenantId: product.tenantId,
      FirestoreConstants.storeId: product.storeId,
      FirestoreConstants.createdAt: product.createdAt != null ? Timestamp.fromDate(product.createdAt!) : Timestamp.fromDate(now),
      FirestoreConstants.updatedAt: Timestamp.fromDate(now),
    };
  }
}
