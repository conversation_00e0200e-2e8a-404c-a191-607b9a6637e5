import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/product_mgmt/data/utils/sku_service.dart';
import 'package:grid_pos/features/product_mgmt/domain/entities/product_entity.dart';
import 'package:grid_pos/features/product_mgmt/domain/repositories/product_repository.dart';
import 'package:grid_pos/features/product_mgmt/presentation/providers/product_providers.dart';
import 'package:grid_pos/features/product_mgmt/presentation/providers/product_validation_providers.dart';
import 'package:intl/intl.dart';
import 'dart:math' as math;
import 'package:cloud_firestore/cloud_firestore.dart';

part 'product_crud_provider.freezed.dart';

/// State for product CRUD operations
@freezed
abstract class ProductCrudState with _$ProductCrudState {
  /// Default state constructor
  const factory ProductCrudState({
    @Default(false) bool isLoading,
    @Default(false) bool isSuccess,
    String? errorMessage,
    String? successMessage,
    String? newProductId, // 存储新创建的产品ID
  }) = _ProductCrudState;
}

/// Notifier for product CRUD operations
class ProductCrudNotifier extends StateNotifier<ProductCrudState> {
  final ProductRepository _productRepository;
  final SkuService _skuService;
  final Ref _ref; // For accessing other providers if needed

  /// Constructor for ProductCrudNotifier
  ProductCrudNotifier(this._productRepository, this._skuService, this._ref)
    : super(const ProductCrudState());

  /// Create a new product
  Future<String?> createProduct(
    String storeId,
    String tenantId,
    ProductEntity product,
  ) async {
    try {
      state = state.copyWith(
        isLoading: true,
        errorMessage: null,
        successMessage: null,
        isSuccess: false,
      );

      // 使用一个变量来跟踪可能需要修改的产品实例
      var productWithGeneratedValues = product;

      // 1. 如果SKU为空，自动生成
      if (productWithGeneratedValues.sku.isEmpty) {
        try {
          final sku = await _skuService.generateSku(storeId, tenantId);
          productWithGeneratedValues = productWithGeneratedValues.copyWith(
            sku: sku,
          );
          Logger.debug('生成SKU: $sku');
        } on SkuGenerationFailure catch (e) {
          Logger.error('SKU生成失败', e);
          state = state.copyWith(
            isLoading: false,
            isSuccess: false,
            errorMessage: e.message,
          );
          _autoClearStateMessagesAfterDelay();
          return null;
        }
      }

      // 2. 如果QR code为空，使用Firestore的自動生成ID
      if (productWithGeneratedValues.barcode.isEmpty) {
        // 直接使用Firestore自動生成的唯一ID作為條形碼
        final generatedBarcode =
            FirebaseFirestore.instance.collection('_tmp').doc().id;

        productWithGeneratedValues = productWithGeneratedValues.copyWith(
          barcode: generatedBarcode,
        );
        Logger.debug('生成條形碼 (基於Firestore ID): $generatedBarcode');
      }

      // 3. 确保storeId、tenantId和时间戳都已设置
      // product.id将为空，会由Firestore生成
      final now = DateTime.now();
      final finalProductToSave = productWithGeneratedValues.copyWith(
        storeId: storeId,
        tenantId: tenantId,
        createdAt: productWithGeneratedValues.createdAt ?? now,
        updatedAt: now,
      );

      // 调用存储库的createProduct方法，该方法会返回Firestore生成的文档ID
      final firestoreDocId = await _productRepository.createProduct(
        storeId,
        tenantId,
        finalProductToSave,
      );
      Logger.info(
        '产品已创建。Firestore文档ID: $firestoreDocId, SKU: ${finalProductToSave.sku}, QR Code: ${finalProductToSave.barcode}',
      );

      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        successMessage: '产品创建成功！',
        newProductId: firestoreDocId, // 存储新创建的产品ID
      );
      _autoClearStateMessagesAfterDelay();
      return firestoreDocId; // 返回Firestore文档ID
    } catch (e) {
      Logger.error('ProductCrudNotifier中创建产品时出错', e);
      if (e is! SkuGenerationFailure) {
        state = state.copyWith(
          isLoading: false,
          isSuccess: false,
          errorMessage: '创建产品失败: ${e.toString()}',
        );
        _autoClearStateMessagesAfterDelay();
      }
      return null;
    }
  }

  /// Update an existing product
  Future<bool> updateProduct(
    String storeId,
    String tenantId,
    ProductEntity product,
  ) async {
    try {
      state = state.copyWith(
        isLoading: true,
        errorMessage: null,
        successMessage: null,
        isSuccess: false,
      );

      await _productRepository.updateProduct(storeId, tenantId, product);

      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        successMessage: '产品更新成功',
      );
      _autoClearStateMessagesAfterDelay();

      return true;
    } catch (e) {
      Logger.error('Error updating product', e);
      state = state.copyWith(
        isLoading: false,
        isSuccess: false,
        errorMessage: '更新产品失败: ${e.toString()}',
      );
      _autoClearStateMessagesAfterDelay();
      return false;
    }
  }

  /// Delete a product
  Future<bool> deleteProduct(
    String storeId,
    String tenantId,
    String productId,
  ) async {
    try {
      state = state.copyWith(
        isLoading: true,
        errorMessage: null,
        successMessage: null,
        isSuccess: false,
      );

      await _productRepository.deleteProduct(storeId, tenantId, productId);

      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        successMessage: '产品删除成功',
      );
      _autoClearStateMessagesAfterDelay();

      return true;
    } catch (e) {
      Logger.error('Error deleting product', e);
      state = state.copyWith(
        isLoading: false,
        isSuccess: false,
        errorMessage: '删除产品失败: ${e.toString()}',
      );
      _autoClearStateMessagesAfterDelay();
      return false;
    }
  }

  void _autoClearStateMessagesAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        final currentState = state;
        // Only clear messages if they exist and we're not loading
        if (!currentState.isLoading &&
            (currentState.successMessage != null ||
                currentState.errorMessage != null)) {
          // 清除消息但保留newProductId
          state = state.copyWith(
            errorMessage: null,
            successMessage: null,
            // 保留其他字段
            newProductId: currentState.newProductId,
            isSuccess: currentState.isSuccess,
            isLoading: currentState.isLoading,
          );
        }
      }
    });
  }

  /// Reset state
  void resetState() {
    state = const ProductCrudState();
  }
}

/// Provider for product CRUD operations
final productCrudNotifierProvider =
    StateNotifierProvider<ProductCrudNotifier, ProductCrudState>((ref) {
      final repository = ref.watch(productRepositoryProvider);
      final skuService = ref.watch(skuServiceProvider);
      return ProductCrudNotifier(repository, skuService, ref);
    });
