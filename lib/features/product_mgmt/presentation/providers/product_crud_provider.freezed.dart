// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_crud_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ProductCrudState {

 bool get isLoading; bool get isSuccess; String? get errorMessage; String? get successMessage; String? get newProductId;
/// Create a copy of ProductCrudState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductCrudStateCopyWith<ProductCrudState> get copyWith => _$ProductCrudStateCopyWithImpl<ProductCrudState>(this as ProductCrudState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductCrudState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isSuccess, isSuccess) || other.isSuccess == isSuccess)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.successMessage, successMessage) || other.successMessage == successMessage)&&(identical(other.newProductId, newProductId) || other.newProductId == newProductId));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,isSuccess,errorMessage,successMessage,newProductId);

@override
String toString() {
  return 'ProductCrudState(isLoading: $isLoading, isSuccess: $isSuccess, errorMessage: $errorMessage, successMessage: $successMessage, newProductId: $newProductId)';
}


}

/// @nodoc
abstract mixin class $ProductCrudStateCopyWith<$Res>  {
  factory $ProductCrudStateCopyWith(ProductCrudState value, $Res Function(ProductCrudState) _then) = _$ProductCrudStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, bool isSuccess, String? errorMessage, String? successMessage, String? newProductId
});




}
/// @nodoc
class _$ProductCrudStateCopyWithImpl<$Res>
    implements $ProductCrudStateCopyWith<$Res> {
  _$ProductCrudStateCopyWithImpl(this._self, this._then);

  final ProductCrudState _self;
  final $Res Function(ProductCrudState) _then;

/// Create a copy of ProductCrudState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? isSuccess = null,Object? errorMessage = freezed,Object? successMessage = freezed,Object? newProductId = freezed,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isSuccess: null == isSuccess ? _self.isSuccess : isSuccess // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,successMessage: freezed == successMessage ? _self.successMessage : successMessage // ignore: cast_nullable_to_non_nullable
as String?,newProductId: freezed == newProductId ? _self.newProductId : newProductId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc


class _ProductCrudState implements ProductCrudState {
  const _ProductCrudState({this.isLoading = false, this.isSuccess = false, this.errorMessage, this.successMessage, this.newProductId});
  

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool isSuccess;
@override final  String? errorMessage;
@override final  String? successMessage;
@override final  String? newProductId;

/// Create a copy of ProductCrudState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductCrudStateCopyWith<_ProductCrudState> get copyWith => __$ProductCrudStateCopyWithImpl<_ProductCrudState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductCrudState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isSuccess, isSuccess) || other.isSuccess == isSuccess)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.successMessage, successMessage) || other.successMessage == successMessage)&&(identical(other.newProductId, newProductId) || other.newProductId == newProductId));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,isSuccess,errorMessage,successMessage,newProductId);

@override
String toString() {
  return 'ProductCrudState(isLoading: $isLoading, isSuccess: $isSuccess, errorMessage: $errorMessage, successMessage: $successMessage, newProductId: $newProductId)';
}


}

/// @nodoc
abstract mixin class _$ProductCrudStateCopyWith<$Res> implements $ProductCrudStateCopyWith<$Res> {
  factory _$ProductCrudStateCopyWith(_ProductCrudState value, $Res Function(_ProductCrudState) _then) = __$ProductCrudStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, bool isSuccess, String? errorMessage, String? successMessage, String? newProductId
});




}
/// @nodoc
class __$ProductCrudStateCopyWithImpl<$Res>
    implements _$ProductCrudStateCopyWith<$Res> {
  __$ProductCrudStateCopyWithImpl(this._self, this._then);

  final _ProductCrudState _self;
  final $Res Function(_ProductCrudState) _then;

/// Create a copy of ProductCrudState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? isSuccess = null,Object? errorMessage = freezed,Object? successMessage = freezed,Object? newProductId = freezed,}) {
  return _then(_ProductCrudState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isSuccess: null == isSuccess ? _self.isSuccess : isSuccess // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,successMessage: freezed == successMessage ? _self.successMessage : successMessage // ignore: cast_nullable_to_non_nullable
as String?,newProductId: freezed == newProductId ? _self.newProductId : newProductId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
