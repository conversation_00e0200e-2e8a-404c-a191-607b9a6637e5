import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/product_mgmt/data/utils/product_validation_service.dart';
import 'package:grid_pos/features/product_mgmt/data/utils/sku_service.dart';
import 'package:grid_pos/features/product_mgmt/presentation/providers/product_providers.dart';

/// Provider for SKU Service
final skuServiceProvider = Provider<SkuService>((ref) {
  final firestore = ref.watch(firestoreProvider);
  return SkuService(firestore);
});

/// Provider for the Product Validation Service
final productValidationServiceProvider = Provider<ProductValidationService>((ref) {
  final productRepository = ref.watch(productRepositoryProvider);
  return ProductValidationService(productRepository);
});

/// State class for barcode validation
class BarcodeValidationState {
  final bool isValidating;
  final bool? isValid;
  final String barcode;

  /// Constructor
  const BarcodeValidationState({this.isValidating = false, this.isValid, this.barcode = ''});

  /// Create a copy with updated fields
  BarcodeValidationState copyWith({bool? isValidating, bool? isValid, String? barcode, bool clearIsValid = false}) {
    return BarcodeValidationState(isValidating: isValidating ?? this.isValidating, isValid: clearIsValid ? null : isValid ?? this.isValid, barcode: barcode ?? this.barcode);
  }
}

/// Notifier for barcode validation
class BarcodeValidationNotifier extends StateNotifier<BarcodeValidationState> {
  final ProductValidationService _validationService;
  final String _storeId;
  final String? _excludeProductId;

  /// Constructor
  BarcodeValidationNotifier(this._validationService, this._storeId, {String? excludeProductId}) : _excludeProductId = excludeProductId, super(const BarcodeValidationState());

  /// Validate the barcode
  Future<void> validateBarcode(String barcode) async {
    if (barcode == state.barcode && state.isValid != null) {
      // If the barcode hasn't changed and we already have a validation result,
      // no need to revalidate
      return;
    }

    // Update state to indicate validation is in progress and clear previous result
    state = state.copyWith(isValidating: true, barcode: barcode, clearIsValid: true);

    if (barcode.isEmpty) {
      // Empty barcode is not valid
      state = state.copyWith(isValidating: false, isValid: false);
      return;
    }

    try {
      // Start the validation process
      await _validationService.validateBarcodeUniqueness(
        storeId: _storeId,
        barcode: barcode,
        excludeProductId: _excludeProductId,
        onResult: (isValid) {
          // Update state with validation result when it arrives
          state = state.copyWith(isValidating: false, isValid: isValid);
        },
      );
    } catch (e) {
      Logger.error('Error in barcode validation notifier', e);
      state = state.copyWith(isValidating: false, isValid: false);
    }
  }
}

/// Provider for barcode validation
final barcodeValidationProvider = StateNotifierProvider.family<BarcodeValidationNotifier, BarcodeValidationState, ({String storeId, String? excludeProductId})>((ref, params) {
  final validationService = ref.watch(productValidationServiceProvider);
  return BarcodeValidationNotifier(validationService, params.storeId, excludeProductId: params.excludeProductId);
});

/// State class for SKU validation
class SkuValidationState {
  final bool isValidating;
  final bool? isValid;
  final String sku;

  /// Constructor
  const SkuValidationState({this.isValidating = false, this.isValid, this.sku = ''});

  /// Create a copy with updated fields
  SkuValidationState copyWith({bool? isValidating, bool? isValid, String? sku, bool clearIsValid = false}) {
    return SkuValidationState(isValidating: isValidating ?? this.isValidating, isValid: clearIsValid ? null : isValid ?? this.isValid, sku: sku ?? this.sku);
  }
}

/// Notifier for SKU validation
class SkuValidationNotifier extends StateNotifier<SkuValidationState> {
  final ProductValidationService _validationService;
  final String _storeId;
  final String _tenantId;
  final String? _excludeProductId;

  /// Constructor
  SkuValidationNotifier(this._validationService, this._storeId, this._tenantId, {String? excludeProductId}) : _excludeProductId = excludeProductId, super(const SkuValidationState());

  /// Validate the SKU
  Future<void> validateSku(String sku) async {
    if (sku == state.sku && state.isValid != null) {
      // If the SKU hasn't changed and we already have a validation result,
      // no need to revalidate
      return;
    }

    // Update state to indicate validation is in progress and clear previous result
    state = state.copyWith(isValidating: true, sku: sku, clearIsValid: true);

    if (sku.isEmpty) {
      // Empty SKU is not valid
      state = state.copyWith(isValidating: false, isValid: false);
      return;
    }

    try {
      // Start the validation process
      await _validationService.validateSkuUniqueness(
        storeId: _storeId,
        tenantId: _tenantId,
        sku: sku,
        excludeProductId: _excludeProductId,
        onResult: (isValid) {
          // Update state with validation result when it arrives
          state = state.copyWith(isValidating: false, isValid: isValid);
        },
      );
    } catch (e) {
      Logger.error('Error in SKU validation notifier', e);
      state = state.copyWith(isValidating: false, isValid: false);
    }
  }
}

/// Provider for SKU validation
final skuValidationProvider = StateNotifierProvider.family<SkuValidationNotifier, SkuValidationState, ({String storeId, String tenantId, String? excludeProductId})>((ref, params) {
  final validationService = ref.watch(productValidationServiceProvider);
  return SkuValidationNotifier(validationService, params.storeId, params.tenantId, excludeProductId: params.excludeProductId);
});
