import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/product_mgmt/data/datasources/product_remote_datasource.dart';
import 'package:grid_pos/features/product_mgmt/data/repositories/product_repository_impl.dart';
import 'package:grid_pos/features/product_mgmt/domain/entities/product_entity.dart';
import 'package:grid_pos/features/product_mgmt/domain/repositories/product_repository.dart';

/// Provider for Firestore instance
final firestoreProvider = Provider<FirebaseFirestore>((ref) {
  return FirebaseFirestore.instance;
});

/// Provider for ProductRemoteDataSource
final productRemoteDataSourceProvider = Provider<ProductRemoteDataSource>((ref) {
  final firestore = ref.watch(firestoreProvider);
  return ProductRemoteDataSourceImpl(firestore);
});

/// Provider for ProductRepository
final productRepositoryProvider = Provider<ProductRepository>((ref) {
  final remoteDataSource = ref.watch(productRemoteDataSourceProvider);
  return ProductRepositoryImpl(remoteDataSource);
});

/// Stream provider for a single product
final productProvider =
    StreamProvider.family<ProductEntity?, ({String storeId, String tenantId, String productId})>((
      ref,
      params,
    ) {
      final repository = ref.watch(productRepositoryProvider);
      return repository.watchProduct(params.storeId, params.tenantId, params.productId);
    });

/// Stream provider for all products of a tenant
final tenantProductsProvider =
    StreamProvider.family<List<ProductEntity>, ({String storeId, String tenantId})>((ref, params) {
      final repository = ref.watch(productRepositoryProvider);
      return repository.watchProductsByTenant(params.storeId, params.tenantId);
    });

/// Stream provider for all products in a store (admin view)
final storeProductsProvider = StreamProvider.family<List<ProductEntity>, String>((ref, storeId) {
  final repository = ref.watch(productRepositoryProvider);
  return repository.watchProductsByStore(storeId);
});

/// Provider that fetches products by barcode
final productsByBarcodeProvider = StreamProvider.autoDispose
    .family<List<ProductEntity>, ({String storeId, String barcode})>((ref, params) {
      final repository = ref.watch(productRepositoryProvider);
      return repository.watchProductsByBarcode(params.storeId, params.barcode);
    });

/// Provider that directly fetches a product by barcode (more efficient for POS scanning)
final productByBarcodeProvider = FutureProvider.autoDispose
    .family<ProductEntity?, ({String storeId, String barcode})>((ref, params) async {
      final repository = ref.watch(productRepositoryProvider);
      return repository.getProductByBarcode(params.storeId, params.barcode);
    });

/// Stream provider for low stock products of a tenant
final lowStockProductsByTenantProvider =
    StreamProvider.family<List<ProductEntity>, ({String storeId, String tenantId})>((ref, params) {
      final repository = ref.watch(productRepositoryProvider);
      return repository.watchLowStockProductsByTenant(params.storeId, params.tenantId);
    });

/// Stream provider for low stock products in a store (admin view)
final lowStockProductsByStoreProvider = StreamProvider.family<List<ProductEntity>, String>((
  ref,
  storeId,
) {
  final repository = ref.watch(productRepositoryProvider);
  return repository.watchLowStockProductsByStore(storeId);
});
