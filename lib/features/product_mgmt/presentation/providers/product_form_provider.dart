import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/product_mgmt/domain/entities/product_entity.dart';
import 'package:grid_pos/features/product_mgmt/presentation/providers/product_crud_provider.dart';
import 'package:grid_pos/features/product_mgmt/presentation/providers/product_providers.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/grid_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/grid_providers.dart';

part 'product_form_provider.freezed.dart';

/// State for product form
@freezed
abstract class ProductFormState with _$ProductFormState {
  /// Default state constructor
  const factory ProductFormState({
    ProductEntity? product,
    @Default(false) bool isLoading,
    @Default(false) bool isSubmitting,
    @Default(false) bool isSuccess,
    String? errorMessage,
    String? successMessage,
  }) = _ProductFormState;
}

/// Parameters for ProductFormNotifier
class ProductFormNotifierParams {
  /// Store ID
  final String storeId;

  /// Tenant ID
  final String tenantId;

  /// Initial product for editing mode
  final ProductEntity? initialProduct;

  /// Product ID for edit mode when initial product is not available
  final String? productIdForEdit;

  /// Constructor for ProductFormNotifierParams
  const ProductFormNotifierParams({
    required this.storeId,
    required this.tenantId,
    this.initialProduct,
    this.productIdForEdit,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductFormNotifierParams &&
        other.storeId == storeId &&
        other.tenantId == tenantId &&
        other.initialProduct == initialProduct &&
        other.productIdForEdit == productIdForEdit;
  }

  @override
  int get hashCode =>
      storeId.hashCode ^ tenantId.hashCode ^ initialProduct.hashCode ^ productIdForEdit.hashCode;
}

/// 管理员创建新产品时选择租户的状态提供者
final adminSelectedTenantIdForNewProductProvider = StateProvider<String?>((ref) => null);

/// Notifier for product form
class ProductFormNotifier extends StateNotifier<ProductFormState> {
  final Ref _ref;
  final String _storeId;
  final String _tenantId;
  final String? productIdForEdit;

  /// Constructor for ProductFormNotifier
  ProductFormNotifier(
    this._ref,
    this._storeId,
    this._tenantId, {
    ProductEntity? initialProduct,
    this.productIdForEdit,
  }) : super(const ProductFormState(isLoading: true)) {
    Logger.debug(
      "ProductFormNotifier: 构造函数开始, storeId=$_storeId, tenantId=$_tenantId, initialProduct=${initialProduct?.id}, productIdForEdit=$productIdForEdit",
    );

    if (initialProduct != null) {
      // If initialProduct is provided (usually for edit mode)
      state = state.copyWith(product: initialProduct, isLoading: false);
      Logger.debug("ProductFormNotifier: 使用直接提供的产品初始化: ${initialProduct.id}, isLoading=false");
    } else if (productIdForEdit != null && productIdForEdit!.isNotEmpty) {
      // If productIdForEdit is provided but no initialProduct (edit mode after page refresh)
      Logger.debug(
        "ProductFormNotifier: 使用productIdForEdit进行编辑模式初始化: $productIdForEdit. 商店: $_storeId, 租户: $_tenantId",
      );

      if (_tenantId.isEmpty) {
        // 如果没有租户ID，无法加载产品，设置错误状态
        Logger.error("ProductFormNotifier: 加载产品ID为$productIdForEdit时无租户ID。设置错误状态。");
        state = state.copyWith(isLoading: false, errorMessage: "无法加载产品：缺少租户上下文。");
      } else {
        // 有租户ID，可以加载产品
        _loadProduct();
      }
    } else if (_tenantId.isNotEmpty) {
      // For new product with tenantId
      _initializeNewProduct();
      Logger.debug("ProductFormNotifier: 初始化新产品. 商店: $_storeId, 租户: $_tenantId, isLoading=false");
    } else {
      // For new product but tenantId is not determined yet (e.g. Admin has not selected tenant)
      state = state.copyWith(
        isLoading: false,
        product: null,
      ); // Keep product as null, UI should prompt for tenant selection
      Logger.debug("ProductFormNotifier: 初始化新产品但租户ID为空. 等待租户选择. isLoading=false");
    }
  }

  // Load product by ID
  Future<void> _loadProduct() async {
    if (productIdForEdit == null || productIdForEdit!.isEmpty) {
      state = state.copyWith(isLoading: false, errorMessage: "缺少产品ID，无法编辑。");
      return;
    }

    // Check if tenantId is available
    if (_tenantId.isEmpty) {
      Logger.error(
        'ProductFormNotifier: _loadProduct被调用但_tenantId为空，产品ID为$productIdForEdit。无法加载产品。',
      );
      state = state.copyWith(isLoading: false, errorMessage: "无法加载产品：缺少租户上下文。");
      return;
    }

    Logger.info(
      'ProductFormNotifier: 正在为商店$_storeId的租户$_tenantId加载产品$productIdForEdit, isLoading=true',
    );
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);

      // 使用正确的参数格式调用productProvider
      final productAsyncValue = await _ref.read(
        productProvider((
          storeId: _storeId,
          tenantId: _tenantId,
          productId: productIdForEdit!,
        )).future,
      );

      if (productAsyncValue != null) {
        Logger.info('ProductFormNotifier: 产品加载成功: ${productAsyncValue.name}, isLoading=false');
        state = state.copyWith(product: productAsyncValue, isLoading: false, errorMessage: null);
      } else {
        Logger.warning('ProductFormNotifier: 未找到产品ID为$productIdForEdit的产品, isLoading=false');
        state = state.copyWith(isLoading: false, errorMessage: '未找到产品');
      }
    } catch (e) {
      Logger.error('ProductFormNotifier: 加载产品时出错', e);
      state = state.copyWith(isLoading: false, errorMessage: '加载产品时出错: ${e.toString()}');
    }
  }

  // Initialize a new product with default values
  void _initializeNewProduct() {
    Logger.debug('ProductFormNotifier: 初始化新产品, tenantId=$_tenantId, isLoading=false');
    state = state.copyWith(
      product: ProductEntity(
        id: '', // Will be generated by Firestore
        sku: '', // Will be auto-generated
        name: '',
        barcode: '', // Will be auto-generated
        price: 0.0,
        stock: 0,
        gridId: '',
        tenantId: _tenantId,
        storeId: _storeId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      isLoading: false,
      errorMessage: null,
    );
  }

  /// Reset form state (used when switching tenants)
  void resetFormState() {
    Logger.debug('ProductFormNotifier: 重置表单状态, tenantId=$_tenantId, isLoading=false');
    if (_tenantId.isEmpty) {
      // 如果没有租户ID，应该将产品置空，等待租户选择
      state = state.copyWith(
        product: null,
        isLoading: false,
        isSubmitting: false,
        errorMessage: null,
        successMessage: null,
      );
      Logger.debug('ProductFormNotifier: 重置为空产品状态(无租户), isLoading=false');
    } else {
      // 有租户ID，可以初始化一个新的空白产品
      _initializeNewProduct();
      Logger.debug('ProductFormNotifier: 重置为新产品状态(有租户), isLoading=false');
    }
  }

  /// Check if the form is in edit mode
  bool get isEditMode => state.product?.id.isNotEmpty == true;

  /// Update product field
  void updateProduct(ProductEntity Function(ProductEntity product) update) {
    if (state.product != null) {
      state = state.copyWith(product: update(state.product!));
    }
  }

  /// Set name
  void setName(String name) {
    updateProduct((product) => product.copyWith(name: name));
  }

  /// Set price
  void setPrice(String priceStr) {
    try {
      final price = double.tryParse(priceStr) ?? 0.0;
      // 如果是整数，转为整数保存
      if (price == price.toInt()) {
        updateProduct((product) => product.copyWith(price: price.toInt().toDouble()));
      } else {
        updateProduct((product) => product.copyWith(price: price));
      }
    } catch (e) {
      Logger.error('Error parsing price', e);
    }
  }

  /// Set cost
  void setCost(String costStr) {
    try {
      if (costStr.isEmpty) {
        updateProduct((product) => product.copyWith(cost: null));
      } else {
        final cost = double.tryParse(costStr);
        if (cost != null) {
          // 如果是整数，转为整数保存
          if (cost == cost.toInt()) {
            updateProduct((product) => product.copyWith(cost: cost.toInt().toDouble()));
          } else {
            updateProduct((product) => product.copyWith(cost: cost));
          }
        }
      }
    } catch (e) {
      Logger.error('Error parsing cost', e);
    }
  }

  /// Set barcode
  void setBarcode(String barcode) {
    updateProduct((product) => product.copyWith(barcode: barcode));
  }

  /// Set SKU
  void setSku(String sku) {
    updateProduct((product) => product.copyWith(sku: sku));
  }

  /// Set stock
  void setStock(String stockStr) {
    try {
      final stock = int.tryParse(stockStr) ?? 0;
      updateProduct((product) => product.copyWith(stock: stock));
    } catch (e) {
      Logger.error('Error parsing stock', e);
    }
  }

  /// Set low stock level
  void setLowStockLevel(String levelStr) {
    try {
      final level = int.tryParse(levelStr) ?? 5;
      updateProduct((product) => product.copyWith(lowStockLevel: level));
    } catch (e) {
      Logger.error('Error parsing low stock level', e);
    }
  }

  /// Set grid ID
  void setGridId(String? gridId) {
    if (gridId != null) {
      updateProduct((product) => product.copyWith(gridId: gridId));
    }
  }

  /// Set active status
  void setActive(bool active) {
    updateProduct((product) => product.copyWith(active: active));
  }

  /// Save product
  Future<bool> saveProduct() async {
    if (state.product == null) return false;

    Logger.debug('ProductFormNotifier: 开始保存产品, isSubmitting=true, isSuccess=false');
    state = state.copyWith(
      isSubmitting: true,
      errorMessage: null,
      isSuccess: false,
      successMessage: null,
    );

    try {
      final product = state.product!;
      bool success;

      if (isEditMode) {
        // Update existing product, keeping existing SKU and barcode
        Logger.info('ProductFormNotifier: 更新已有产品 ${product.id}');
        success = await _ref
            .read(productCrudNotifierProvider.notifier)
            .updateProduct(_storeId, _tenantId, product);
      } else {
        // Create new product (SKU and barcode will be auto-generated in ProductCrudNotifier)
        Logger.info('ProductFormNotifier: 创建新产品');
        final productId = await _ref
            .read(productCrudNotifierProvider.notifier)
            .createProduct(_storeId, _tenantId, product);
        success = productId != null;
      }

      Logger.debug(
        'ProductFormNotifier: 产品保存${success ? "成功" : "失败"}, isSubmitting=false, isSuccess=$success',
      );
      state = state.copyWith(
        isSubmitting: false,
        isSuccess: success,
        successMessage: success ? (isEditMode ? '产品更新成功' : '产品创建成功') : null,
        errorMessage: success ? null : '保存产品失败',
      );

      return success;
    } catch (e) {
      Logger.error('Error saving product', e);
      state = state.copyWith(
        isSubmitting: false,
        isSuccess: false,
        errorMessage: '错误: ${e.toString()}',
      );
      return false;
    }
  }
}

/// Family provider for product form
final productFormNotifierProvider =
    StateNotifierProvider.family<ProductFormNotifier, ProductFormState, ProductFormNotifierParams>((
      ref,
      params,
    ) {
      return ProductFormNotifier(
        ref,
        params.storeId,
        params.tenantId,
        initialProduct: params.initialProduct,
        productIdForEdit: params.productIdForEdit,
      );
    });

/// Provider to get available grids for a tenant in product form context
/// 重命名为productFormTenantGridsProvider，避免与tenant_mgmt模块中的同名provider冲突
final productFormTenantGridsProvider =
    StreamProvider.family<List<GridEntity>, ({String storeId, String tenantId})>((ref, params) {
      // 使用 watch 而不是 read，以確保當參數改變時會重新訂閱
      return ref.watch(
        tenantGridsProvider((storeId: params.storeId, tenantId: params.tenantId)).stream,
      );
    });
