import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/auth/presentation/providers/auth_provider.dart';
import 'package:grid_pos/features/product_mgmt/domain/entities/product_entity.dart';
import 'package:grid_pos/features/product_mgmt/presentation/providers/product_crud_provider.dart';
import 'package:grid_pos/features/product_mgmt/presentation/providers/product_form_provider.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/tenant_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/tenant_providers.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/widgets/store_selector.dart';
import 'package:grid_pos/shared/widgets/custom_snackbar.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/// 管理員創建新產品時選擇租戶的狀態提供者
final adminSelectedTenantIdForNewProductProvider = StateProvider<String?>((ref) => null);

/// Page for creating or editing a product with improved UI/UX
class ProductFormPage extends ConsumerStatefulWidget {
  /// The initial product for editing, null for creating a new product
  final ProductEntity? product;

  /// The tenant ID for which to create/edit the product
  final String? tenantId;

  /// The product ID for edit mode, from the route path parameter
  final String? productIdForEdit;

  /// The pre-selected tenant ID for the admin (optional)
  final String? preSelectedTenantId;

  /// Constructor for ProductFormPage
  const ProductFormPage({super.key, this.product, this.tenantId, this.productIdForEdit, this.preSelectedTenantId});

  @override
  ConsumerState<ProductFormPage> createState() => _ProductFormPageState();
}

class _ProductFormPageState extends ConsumerState<ProductFormPage> {
  final _formKey = GlobalKey<FormState>();

  // 生成單個產品二維碼的PDF
  Future<Uint8List> _generateSingleProductQrPdf(ProductEntity product) async {
    final pdf = pw.Document();
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4.copyWith(marginLeft: 20, marginRight: 20, marginTop: 20, marginBottom: 20),
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Column(
              mainAxisSize: pw.MainAxisSize.min,
              children: [
                pw.Text(product.name, style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold)),
                pw.SizedBox(height: 2),
                pw.Text("SKU: ${product.sku}", style: const pw.TextStyle(fontSize: 8)),
                pw.SizedBox(height: 5),
                pw.BarcodeWidget(barcode: pw.Barcode.qrCode(), data: product.barcode, width: 60, height: 60),
                pw.SizedBox(height: 2),
                pw.Text(product.barcode, style: const pw.TextStyle(fontSize: 7)),
              ],
            ),
          );
        },
      ),
    );
    return pdf.save();
  }

  // Controllers for form fields
  late final TextEditingController _nameController;
  late final TextEditingController _skuController;
  late final TextEditingController _barcodeController;
  late final TextEditingController _priceController;
  late final TextEditingController _costController;
  late final TextEditingController _stockController;
  late final TextEditingController _lowStockLevelController;

  String? _selectedTenantId;
  String? _selectedGridId;
  bool _isActive = true;
  bool _isAdmin = false;
  bool _isNavigatingAway = false;

  // 根據productIdForEdit或傳入的帶ID的product判斷表單是否處於編輯模式
  bool get _isEditMode => widget.productIdForEdit != null || (widget.product?.id.isNotEmpty ?? false);

  // 跟蹤用於初始化ProductFormNotifier的租戶ID
  String? _notifierTenantIdCache;

  // 清空表單數據
  void _clearFormData() {
    _nameController.clear();
    _skuController.text = '將自動生成';
    _barcodeController.text = '將自動生成 (唯一ID)';
    _priceController.clear();
    _costController.clear();
    _stockController.clear();
    _lowStockLevelController.clear();
    setState(() {
      _selectedGridId = null;
      _isActive = true;
    });

    if (_isAdmin && !_isEditMode) {
      ref.read(adminSelectedTenantIdForNewProductProvider.notifier).state = null;
    }
  }

  // Safe navigation method
  void _safeNavigateBack({bool success = false, String? newProductId}) {
    if (!_isNavigatingAway && mounted && context.mounted) {
      Logger.debug('Navigating back from ProductFormPage. Success: $success, New Product ID: $newProductId');

      setState(() {
        _isNavigatingAway = true;
      });

      if (context.canPop()) {
        context.pop(success);
      } else {
        context.go('/products');
      }
    }
  }

  @override
  void initState() {
    super.initState();

    debugPrint('ProductFormPage.initState: product=${widget.product != null ? "非空" : "空"}');
    if (widget.product != null) {
      debugPrint('編輯產品: ${widget.product!.id}, 名稱: ${widget.product!.name}');
    }

    // Initialize controllers
    _nameController = TextEditingController();
    _skuController = TextEditingController();
    _barcodeController = TextEditingController();
    _priceController = TextEditingController();
    _costController = TextEditingController();
    _stockController = TextEditingController();
    _lowStockLevelController = TextEditingController();

    _selectedTenantId = widget.tenantId;
    _isActive = widget.product?.active ?? true;

    final bool isEditMode = widget.productIdForEdit != null || (widget.product?.id.isNotEmpty ?? false);
    if (!isEditMode) {
      _skuController.text = '將自動生成';
      _barcodeController.text = '將自動生成 (唯一ID)';
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _isAdmin = ref.read(userRoleProvider) == 'admin';
        });

        if (_isAdmin && widget.preSelectedTenantId != null && widget.preSelectedTenantId!.isNotEmpty) {
          ref.read(adminSelectedTenantIdForNewProductProvider.notifier).state = widget.preSelectedTenantId;
        }
      }
    });

    if (widget.product != null) {
      _populateFormWithProduct(widget.product!);
    }

    Logger.debug('ProductFormPage.initState: 初始化完成');
  }

  // Populate form fields with product data
  void _populateFormWithProduct(ProductEntity product) {
    _nameController.text = product.name;
    _skuController.text = product.sku;
    _barcodeController.text = product.barcode;

    if (product.price > 0) {
      if (product.price == product.price.toInt()) {
        _priceController.text = product.price.toInt().toString();
      } else {
        _priceController.text = product.price.toString();
      }
    } else {
      _priceController.text = '';
    }

    if (product.cost != null && product.cost! > 0) {
      if (product.cost == product.cost!.toInt()) {
        _costController.text = product.cost!.toInt().toString();
      } else {
        _costController.text = product.cost.toString();
      }
    } else {
      _costController.text = '';
    }

    if (product.stock > 0) {
      _stockController.text = product.stock.toString();
    } else {
      _stockController.text = '';
    }

    if (product.lowStockLevel > 0) {
      _lowStockLevelController.text = product.lowStockLevel.toString();
    } else {
      _lowStockLevelController.text = '';
    }

    setState(() {
      _selectedTenantId = product.tenantId;
      _selectedGridId = product.gridId;
      _isActive = product.active;
    });
  }

  // 獲取ProductFormNotifier的參數，處理所有可能的情況
  ProductFormNotifierParams _getNotifierParams(String storeId) {
    ProductFormNotifierParams result;

    if (_isEditMode) {
      final String effectiveTenantId = widget.product?.tenantId ?? _selectedTenantId ?? "";

      Logger.debug('ProductFormPage._getNotifierParams: 編輯模式 - productId: ${widget.productIdForEdit ?? widget.product?.id}, tenantId: $effectiveTenantId');

      result = ProductFormNotifierParams(storeId: storeId, tenantId: effectiveTenantId, initialProduct: widget.product, productIdForEdit: widget.productIdForEdit);
    } else {
      String? adminSelectedTenantId;
      if (_isAdmin) {
        adminSelectedTenantId = ref.read(adminSelectedTenantIdForNewProductProvider);
        Logger.debug('ProductFormPage._getNotifierParams: 管理員新建模式 - adminSelectedTenantId: $adminSelectedTenantId');
      }

      final String effectiveTenantId = adminSelectedTenantId ?? widget.preSelectedTenantId ?? _selectedTenantId ?? "";

      if (_notifierTenantIdCache != null && _notifierTenantIdCache != effectiveTenantId) {
        Logger.debug('ProductFormPage._getNotifierParams: 檢測到租戶變化 $_notifierTenantIdCache -> $effectiveTenantId');
      }

      _notifierTenantIdCache = effectiveTenantId;

      Logger.debug('ProductFormPage._getNotifierParams: 新建模式 - tenantId: $effectiveTenantId');

      result = ProductFormNotifierParams(storeId: storeId, tenantId: effectiveTenantId, initialProduct: null, productIdForEdit: null);
    }

    Logger.debug('ProductFormPage._getNotifierParams: 返回參數hashCode=${result.hashCode}');
    return result;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _skuController.dispose();
    _barcodeController.dispose();
    _priceController.dispose();
    _costController.dispose();
    _stockController.dispose();
    _lowStockLevelController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final selectedStoreId = ref.watch(selectedStoreIdProvider);
    final userAuth = ref.watch(authStateProvider);

    final notifierParams = _getNotifierParams(selectedStoreId);
    final bool tenantSelected = notifierParams.tenantId.isNotEmpty;

    final formState = tenantSelected ? ref.watch(productFormNotifierProvider(notifierParams)) : null;

    Logger.debug("ProductFormPage build: tenantSelected=$tenantSelected, formState.isLoading=${formState?.isLoading}");

    final bool formSubmitting = formState?.isSubmitting ?? false;
    final bool formLoading = formState?.isLoading ?? false;
    final bool isFormEnabled = tenantSelected && !formLoading && !formSubmitting;

    // Listen to form state changes
    if (tenantSelected) {
      ref.listen<ProductFormState>(productFormNotifierProvider(notifierParams), (previous, current) {
        if (!mounted) return;

        if (current.product != null && previous?.product != current.product) {
          Logger.debug("ProductFormPage: Product changed in state, updating form controllers");
          _populateFormWithProduct(current.product!);
        }

        if (previous?.isSubmitting == true && !current.isSubmitting) {
          if (current.isSuccess) {
            if (context.mounted) {
              showSuccessSnackBar(context, current.successMessage ?? '產品保存成功');
            }
          } else if (current.errorMessage != null) {
            if (context.mounted) {
              showErrorSnackBar(context, current.errorMessage!);
            }
          }
        }

        if (current.product == null && !_isEditMode) {
          _clearFormData();
        }
      });
    }

    // Listen to CRUD state changes
    ref.listen<ProductCrudState>(productCrudNotifierProvider, (previous, current) {
      if (!mounted || _isNavigatingAway) return;

      if (previous?.isLoading == true && !current.isLoading && current.isSuccess) {
        if (!_isEditMode && current.newProductId != null) {
          final newProductId = current.newProductId!;
          Logger.debug('獲取到新創建的產品ID: $newProductId');

          if (context.mounted) {
            showSuccessSnackBar(context, current.successMessage ?? '產品創建成功！');
            _clearFormData();
            _safeNavigateBack(success: true, newProductId: newProductId);
          }
        } else if (_isEditMode) {
          if (context.mounted) {
            showSuccessSnackBar(context, current.successMessage ?? '產品更新成功');
            _clearFormData();
            _safeNavigateBack(success: true);
          }
        }
      } else if (previous?.isLoading == true && !current.isLoading && current.errorMessage != null) {
        if (context.mounted) {
          showErrorSnackBar(context, current.errorMessage!);
        }
      }
    });

    // Admin tenant change listener
    if (!_isEditMode && _isAdmin) {
      ref.listen<String?>(adminSelectedTenantIdForNewProductProvider, (prevTenantId, nextTenantId) {
        if (prevTenantId != nextTenantId && nextTenantId != null && nextTenantId.isNotEmpty) {
          Logger.debug("Admin changed tenant for new product. Old: $prevTenantId, New: $nextTenantId");
          final newNotifierParams = _getNotifierParams(selectedStoreId);
          ref.read(productFormNotifierProvider(newNotifierParams).notifier).resetFormState();
        } else if (nextTenantId == null || nextTenantId.isEmpty) {
          final newNotifierParams = _getNotifierParams(selectedStoreId);
          ref.read(productFormNotifierProvider(newNotifierParams).notifier).resetFormState();
        }
      });
    }

    final bool isEditMode = widget.product != null || widget.productIdForEdit != null;
    final bool isFormEffectivelyLoading = (formLoading || (formState == null && tenantSelected)) && (_isEditMode || (tenantSelected && formState?.product == null));

    Logger.debug("ProductFormPage build CHECK: isFormEffectivelyLoading: $isFormEffectivelyLoading");

    if (isFormEffectivelyLoading) {
      return _buildLoadingScreen(isEditMode);
    }

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        title: Text(isEditMode ? '編輯產品' : '添加產品', style: const TextStyle(fontWeight: FontWeight.w600)),
        leading: IconButton(icon: const Icon(Icons.arrow_back_ios), onPressed: () => _safeNavigateBack()),
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // Progress indicator
            if (formSubmitting) const LinearProgressIndicator(backgroundColor: Colors.transparent, valueColor: AlwaysStoppedAnimation<Color>(Colors.green)),

            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header section
                    _buildHeaderSection(context, isEditMode),
                    const SizedBox(height: 24),

                    // Admin tenant selector
                    if (_isAdmin) ...[_buildTenantSelectorCard(selectedStoreId), const SizedBox(height: 20)],

                    // Tenant selection prompt
                    if (!tenantSelected) ...[_buildTenantPromptCard(context), const SizedBox(height: 20)],

                    // Basic Information Card
                    if (tenantSelected) ...[_buildBasicInfoCard(context, isFormEnabled, isEditMode), const SizedBox(height: 20)],

                    // Pricing Information Card
                    if (tenantSelected) ...[_buildPricingCard(context, isFormEnabled), const SizedBox(height: 20)],

                    // Inventory Information Card
                    if (tenantSelected) ...[_buildInventoryCard(context, isFormEnabled), const SizedBox(height: 20)],

                    // Grid Assignment Card
                    if (tenantSelected) ...[_buildGridAssignmentCard(context, selectedStoreId, isFormEnabled), const SizedBox(height: 32)],
                  ],
                ),
              ),
            ),

            // Bottom action bar
            if (tenantSelected) _buildBottomActionBar(context, isFormEnabled, formSubmitting, userAuth, isEditMode),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingScreen(bool isEditMode) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        title: Text(isEditMode ? '編輯產品' : '添加產品'),
        leading: IconButton(icon: const Icon(Icons.arrow_back_ios), onPressed: () => _safeNavigateBack()),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(width: 48, height: 48, child: CircularProgressIndicator(strokeWidth: 3, valueColor: AlwaysStoppedAnimation<Color>(Colors.green))),
            SizedBox(height: 24),
            Text('載入產品資料中...', style: TextStyle(fontSize: 16, color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context, bool isEditMode) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: [Colors.green[600]!, Colors.green[400]!], begin: Alignment.topLeft, end: Alignment.bottomRight),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(color: Colors.white.withOpacity(0.2), borderRadius: BorderRadius.circular(12)),
            child: Icon(isEditMode ? Icons.edit : Icons.add_shopping_cart, color: Colors.white, size: 28),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(isEditMode ? '編輯產品資料' : '創建新產品', style: const TextStyle(color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold)),
                const SizedBox(height: 4),
                Text(isEditMode ? '更新下方的產品詳細資料' : '填寫表單來創建新的產品', style: TextStyle(color: Colors.white.withOpacity(0.9), fontSize: 14)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTenantSelectorCard(String storeId) {
    return _buildSectionCard(
      title: '租戶選擇',
      icon: Icons.store,
      child: Consumer(
        builder: (context, ref, child) {
          final tenantsAsyncValue = ref.watch(tenantsProvider(storeId));

          return tenantsAsyncValue.when(
            data: (tenants) {
              if (tenants.isEmpty) {
                return _buildWarningCard(context, '沒有可用的租戶', '請先創建一個租戶才能添加產品', Icons.error_outline, Colors.red);
              }

              return _buildStyledDropdown(
                label: '選擇租戶',
                hint: '請選擇要管理的租戶',
                icon: Icons.business,
                value: tenants.any((t) => t.id == _selectedTenantId) ? _selectedTenantId : null,
                items: tenants.map((TenantEntity tenant) => DropdownMenuItem<String>(value: tenant.id, child: Text(tenant.name))).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedTenantId = newValue;
                    _selectedGridId = null;
                  });
                  ref.read(adminSelectedTenantIdForNewProductProvider.notifier).state = newValue;
                },
                validator: (value) => value == null ? '請選擇一個租戶' : null,
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => _buildWarningCard(context, '載入錯誤', '加載租戶時出錯: $error', Icons.error, Colors.red),
          );
        },
      ),
    );
  }

  Widget _buildTenantPromptCard(BuildContext context) {
    return _buildWarningCard(context, _isAdmin ? '請選擇租戶' : '缺少租戶信息', _isAdmin ? '請先選擇一個租戶來添加/編輯產品' : '缺少租戶信息，無法繼續', Icons.info_outline, _isAdmin ? Colors.orange : Colors.red);
  }

  Widget _buildBasicInfoCard(BuildContext context, bool isFormEnabled, bool isEditMode) {
    return _buildSectionCard(
      title: '基本資料',
      icon: Icons.info,
      child: Column(
        children: [
          _buildStyledTextField(
            controller: _nameController,
            label: '產品名稱',
            hint: '請輸入產品名稱',
            icon: Icons.shopping_bag,
            enabled: isFormEnabled,
            onChanged: (value) {
              if (!isFormEnabled) return;
              final notifierParams = _getNotifierParams(ref.read(selectedStoreIdProvider));
              if (notifierParams.tenantId.isNotEmpty) {
                ref.read(productFormNotifierProvider(notifierParams).notifier).setName(value);
              }
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '請輸入產品名稱';
              }
              return null;
            },
          ),
          if (isEditMode) ...[
            const SizedBox(height: 16),
            _buildStyledTextField(controller: _skuController, label: 'SKU', hint: '將自動生成', icon: Icons.qr_code, enabled: false, readOnly: true, onChanged: (value) {}),
            const SizedBox(height: 16),
            _buildStyledTextField(controller: _barcodeController, label: '二維碼 (QR Code)', hint: '將自動生成 (唯一ID)', icon: Icons.qr_code_scanner, enabled: false, readOnly: true, onChanged: (value) {}),
          ],
        ],
      ),
    );
  }

  Widget _buildPricingCard(BuildContext context, bool isFormEnabled) {
    return _buildSectionCard(
      title: '價格資料',
      icon: Icons.attach_money,
      child: Column(
        children: [
          _buildStyledTextField(
            controller: _priceController,
            label: '銷售價格',
            hint: '0',
            icon: Icons.sell,
            enabled: isFormEnabled,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}'))],
            prefixText: '\$',
            onChanged: (value) {
              if (!isFormEnabled) return;
              final notifierParams = _getNotifierParams(ref.read(selectedStoreIdProvider));
              if (notifierParams.tenantId.isNotEmpty) {
                ref.read(productFormNotifierProvider(notifierParams).notifier).setPrice(value);
              }
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '請輸入價格';
              }
              final price = double.tryParse(value);
              if (price == null) {
                return '請輸入有效價格';
              }
              if (price < 0) {
                return '價格不能為負';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          _buildStyledTextField(
            controller: _costController,
            label: '成本價格（可選）',
            hint: '0',
            icon: Icons.money_off,
            enabled: isFormEnabled,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}'))],
            prefixText: '\$',
            onChanged: (value) {
              if (!isFormEnabled) return;
              final notifierParams = _getNotifierParams(ref.read(selectedStoreIdProvider));
              if (notifierParams.tenantId.isNotEmpty) {
                ref.read(productFormNotifierProvider(notifierParams).notifier).setCost(value);
              }
            },
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final cost = double.tryParse(value);
                if (cost == null) {
                  return '請輸入有效成本';
                }
                if (cost < 0) {
                  return '成本不能為負';
                }
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryCard(BuildContext context, bool isFormEnabled) {
    return _buildSectionCard(
      title: '庫存管理',
      icon: Icons.inventory,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStyledTextField(
                  controller: _stockController,
                  label: '庫存數量',
                  hint: '0',
                  icon: Icons.warehouse,
                  enabled: isFormEnabled,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  onChanged: (value) {
                    if (!isFormEnabled) return;
                    final notifierParams = _getNotifierParams(ref.read(selectedStoreIdProvider));
                    if (notifierParams.tenantId.isNotEmpty) {
                      ref.read(productFormNotifierProvider(notifierParams).notifier).setStock(value);
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '請輸入庫存數量';
                    }
                    final stock = int.tryParse(value);
                    if (stock == null) {
                      return '請輸入有效數字';
                    }
                    if (stock < 0) {
                      return '庫存不能為負';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStyledTextField(
                  controller: _lowStockLevelController,
                  label: '低庫存警告',
                  hint: '5',
                  icon: Icons.warning,
                  enabled: isFormEnabled,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  onChanged: (value) {
                    if (!isFormEnabled) return;
                    final notifierParams = _getNotifierParams(ref.read(selectedStoreIdProvider));
                    if (notifierParams.tenantId.isNotEmpty) {
                      ref.read(productFormNotifierProvider(notifierParams).notifier).setLowStockLevel(value);
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '請輸入低庫存閾值';
                    }
                    final level = int.tryParse(value);
                    if (level == null) {
                      return '請輸入有效數字';
                    }
                    if (level < 0) {
                      return '閾值不能為負';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildStyledSwitchTile(
            title: '產品激活',
            subtitle: '產品可供銷售',
            value: _isActive,
            onChanged:
                !isFormEnabled
                    ? null
                    : (bool value) {
                      setState(() {
                        _isActive = value;
                      });

                      final notifierParams = _getNotifierParams(ref.read(selectedStoreIdProvider));
                      if (notifierParams.tenantId.isNotEmpty) {
                        ref.read(productFormNotifierProvider(notifierParams).notifier).setActive(value);
                      }
                    },
          ),
        ],
      ),
    );
  }

  Widget _buildGridAssignmentCard(BuildContext context, String storeId, bool isFormEnabled) {
    return _buildSectionCard(title: '格位分配', icon: Icons.grid_view, child: _buildGridSelector(storeId, isFormEnabled));
  }

  Widget _buildGridSelector(String storeId, bool isFormEnabled) {
    final notifierParams = _getNotifierParams(storeId);
    if (notifierParams.tenantId.isEmpty) {
      return _buildWarningCard(context, '請先選擇租戶', '需要先選擇租戶才能選擇格位', Icons.info, Colors.grey);
    }

    Logger.debug('_buildGridSelector: storeId=$storeId, tenantId=${notifierParams.tenantId}');

    return Consumer(
      builder: (context, ref, child) {
        final gridsAsyncValue = ref.watch(productFormTenantGridsProvider((storeId: storeId, tenantId: notifierParams.tenantId)));

        return gridsAsyncValue.when(
          data: (grids) {
            Logger.debug('_buildGridSelector: received ${grids.length} grids for tenant ${notifierParams.tenantId}');

            if (grids.isEmpty) {
              return _buildWarningCard(context, '沒有可用格位', '此租戶沒有可用的格位，請先分配或創建格位', Icons.warning, Colors.orange);
            }

            return _buildStyledDropdown(
              label: '選擇格位',
              hint: '請選擇產品格位',
              icon: Icons.grid_4x4,
              value: grids.any((g) => g.id == _selectedGridId) ? _selectedGridId : null,
              items:
                  grids
                      .map(
                        (grid) => DropdownMenuItem<String>(
                          value: grid.id,
                          child: Row(
                            children: [
                              Container(width: 12, height: 12, decoration: BoxDecoration(color: _getGridSizeColor(grid.size), borderRadius: BorderRadius.circular(2))),
                              const SizedBox(width: 8),
                              Text('格位: ${grid.code} (${grid.size})'),
                            ],
                          ),
                        ),
                      )
                      .toList(),
              onChanged: (String? newValue) {
                if (!isFormEnabled) return;
                setState(() {
                  _selectedGridId = newValue;
                });

                if (newValue != null) {
                  ref.read(productFormNotifierProvider(notifierParams).notifier).setGridId(newValue);
                }
              },
              validator: (value) {
                if (!_isEditMode && (value == null || value.isEmpty)) {
                  return '請選擇一個格位';
                }
                return null;
              },
            );
          },
          loading:
              () => Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(width: 24, height: 24, child: CircularProgressIndicator(strokeWidth: 2, valueColor: AlwaysStoppedAnimation<Color>(Colors.green))),
                      const SizedBox(height: 8),
                      const Text('載入格位中...', style: TextStyle(fontSize: 14, color: Colors.grey)),
                      const SizedBox(height: 8),
                      TextButton(
                        onPressed: () {
                          // 重新整理格位資料
                          ref.refresh(productFormTenantGridsProvider((storeId: storeId, tenantId: notifierParams.tenantId)));
                        },
                        child: const Text('重試', style: TextStyle(fontSize: 12)),
                      ),
                    ],
                  ),
                ),
              ),
          error:
              (error, stack) => _buildErrorCardWithRetry(context, '載入錯誤', '加載格位時出錯: $error', Icons.error, Colors.red, () {
                // 重新整理格位資料
                ref.refresh(productFormTenantGridsProvider((storeId: storeId, tenantId: notifierParams.tenantId)));
              }),
        );
      },
    );
  }

  Color _getGridSizeColor(String size) {
    switch (size.toUpperCase()) {
      case 'S':
        return Colors.green;
      case 'M':
        return Colors.orange;
      case 'L':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildSectionCard({required String title, required IconData icon, required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.05), blurRadius: 10, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(color: Colors.grey[50], borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16))),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(color: Colors.green[100], borderRadius: BorderRadius.circular(8)),
                  child: Icon(icon, color: Colors.green[700], size: 20),
                ),
                const SizedBox(width: 12),
                Text(title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: Colors.black87)),
              ],
            ),
          ),
          Padding(padding: const EdgeInsets.all(20), child: child),
        ],
      ),
    );
  }

  Widget _buildWarningCard(BuildContext context, String title, String message, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(color: color.withOpacity(0.1), borderRadius: BorderRadius.circular(12), border: Border.all(color: color.withOpacity(0.3))),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: TextStyle(fontWeight: FontWeight.w600, color: color, fontSize: 14)),
                const SizedBox(height: 2),
                Text(message, style: TextStyle(color: color.withOpacity(0.8), fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorCardWithRetry(BuildContext context, String title, String message, IconData icon, Color color, VoidCallback onRetry) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(color: color.withOpacity(0.1), borderRadius: BorderRadius.circular(12), border: Border.all(color: color.withOpacity(0.3))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(title, style: TextStyle(fontWeight: FontWeight.w600, color: color, fontSize: 14)),
                    const SizedBox(height: 2),
                    Text(message, style: TextStyle(color: color.withOpacity(0.8), fontSize: 12)),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Center(
            child: TextButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh, size: 16),
              label: const Text('重試'),
              style: TextButton.styleFrom(foregroundColor: color, textStyle: const TextStyle(fontSize: 12)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStyledTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    required IconData icon,
    String? prefixText,
    bool enabled = true,
    bool readOnly = false,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    required Function(String) onChanged,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      enabled: enabled,
      readOnly: readOnly,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      onChanged: onChanged,
      validator: validator,
      style: const TextStyle(fontSize: 16),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixText: prefixText,
        prefixIcon: Icon(icon, color: Colors.grey[600]),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide(color: Colors.grey[300]!)),
        enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide(color: Colors.grey[300]!)),
        focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide(color: Colors.green[600]!, width: 2)),
        errorBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: const BorderSide(color: Colors.red, width: 2)),
        focusedErrorBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: const BorderSide(color: Colors.red, width: 2)),
        filled: true,
        fillColor: enabled ? (readOnly ? Colors.grey[100] : Colors.grey[50]) : Colors.grey[100],
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
    );
  }

  Widget _buildStyledDropdown({
    required String label,
    String? hint,
    required IconData icon,
    required String? value,
    required List<DropdownMenuItem<String>> items,
    required Function(String?) onChanged,
    String? Function(String?)? validator,
  }) {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.grey[600]),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide(color: Colors.grey[300]!)),
        enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide(color: Colors.grey[300]!)),
        focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide(color: Colors.green[600]!, width: 2)),
        filled: true,
        fillColor: Colors.grey[50],
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
      value: value,
      items: items,
      onChanged: onChanged,
      validator: validator,
    );
  }

  Widget _buildStyledSwitchTile({required String title, required String subtitle, required bool value, required Function(bool)? onChanged}) {
    return Container(
      decoration: BoxDecoration(color: Colors.grey[50], borderRadius: BorderRadius.circular(12), border: Border.all(color: Colors.grey[200]!)),
      child: SwitchListTile(
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
        subtitle: Text(subtitle, style: TextStyle(color: Colors.grey[600])),
        value: value,
        onChanged: onChanged,
        activeColor: Colors.green[600],
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      ),
    );
  }

  Widget _buildBottomActionBar(BuildContext context, bool isFormEnabled, bool formSubmitting, dynamic userAuth, bool isEditMode) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(color: Colors.white, boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.05), blurRadius: 10, offset: const Offset(0, -2))]),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: isFormEnabled && !(userAuth.isLoading || formSubmitting) ? _submitForm : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[600],
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            child:
                formSubmitting
                    ? const SizedBox(width: 24, height: 24, child: CircularProgressIndicator(strokeWidth: 2, valueColor: AlwaysStoppedAnimation<Color>(Colors.white)))
                    : Row(mainAxisAlignment: MainAxisAlignment.center, children: [Icon(isEditMode ? Icons.update : Icons.add), const SizedBox(width: 8), Text(isEditMode ? '更新產品' : '創建產品')]),
          ),
        ),
      ),
    );
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      final storeId = ref.read(selectedStoreIdProvider);
      final notifierParams = _getNotifierParams(storeId);

      if (notifierParams.tenantId.isEmpty) {
        showErrorSnackBar(context, '請選擇租戶');
        return;
      }

      // 只在創建新產品時檢查格位
      if (!_isEditMode && (_selectedGridId == null || _selectedGridId!.isEmpty)) {
        showErrorSnackBar(context, '請選擇一個格位');
        return;
      }

      final success = await ref.read(productFormNotifierProvider(notifierParams).notifier).saveProduct();

      if (success) {
        Logger.debug('產品保存成功，準備導航');

        final crudState = ref.read(productCrudNotifierProvider);

        if (!_isEditMode && crudState.newProductId != null) {
          _clearFormData();
          _safeNavigateBack(success: true, newProductId: crudState.newProductId);
        } else if (_isEditMode) {
          _clearFormData();
          _safeNavigateBack(success: true);
        }
      }
    }
  }
}
