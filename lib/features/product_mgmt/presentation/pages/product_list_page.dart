import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:grid_pos/features/auth/presentation/providers/auth_provider.dart';
import 'package:grid_pos/features/pos/domain/services/printing_service.dart';
import 'package:grid_pos/features/pos/presentation/pages/pdf_preview_page.dart';
import 'package:grid_pos/features/product_mgmt/domain/entities/product_entity.dart';
import 'package:grid_pos/features/product_mgmt/presentation/providers/product_crud_provider.dart';
import 'package:grid_pos/features/product_mgmt/presentation/providers/product_providers.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/tenant_providers.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/widgets/store_selector.dart';
import 'package:grid_pos/shared/widgets/custom_snackbar.dart';
import 'package:grid_pos/shared/widgets/qr_code_display.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/// 列印標籤樣式配置
class QrPrintConfig {
  /// 每頁標籤數量
  final int itemsPerPage;

  /// 每行標籤數量
  final int itemsPerRow;

  /// 標籤寬度
  final double labelWidth;

  /// 標籤高度
  final double labelHeight;

  /// 二維碼大小
  final double qrCodeSize;

  /// 是否顯示價格
  final bool showPrice;

  /// 是否顯示SKU
  final bool showSku;

  /// 是否使用加粗字體
  final bool useBoldFont;

  /// 商品名稱字體大小
  final double nameSize;

  const QrPrintConfig({
    this.itemsPerPage = 24,
    this.itemsPerRow = 6,
    this.labelWidth = 90,
    this.labelHeight = 90,
    this.qrCodeSize = 50,
    this.showPrice = false,
    this.showSku = true,
    this.useBoldFont = true,
    this.nameSize = 6,
  });

  /// 根據每行標籤數量計算最大QR碼尺寸
  static double calculateMaxQrSize(int itemsPerRow) {
    // A4紙張寬度約爲210mm，減去左右邊距(40mm)，再根據每行標籤數量平分
    // 同時考慮標籤之間的間距
    double availableWidth = (210 - 40) / itemsPerRow;
    // 每個標籤內部還有padding，所以再減去10mm左右
    availableWidth -= 10;
    // 轉換爲pt單位並適當縮小以確保完全顯示
    return availableWidth * 2.5;
  }

  /// 根據列印參數自動計算每頁標籤數量
  static int calculateItemsPerPage({
    required int itemsPerRow,
    required double qrCodeSize,
    required bool showSku,
    required bool showPrice,
    required bool useBoldFont,
    required double nameSize,
  }) {
    // A4紙張高度約爲297mm，減去上下邊距(40mm)
    double availableHeight = 297 - 40;

    // 基本標籤高度，考慮padding和邊框
    double baseItemHeight = 10;

    // 調整項目：標題佔用的高度
    baseItemHeight += nameSize * 1.5; // 根據字體大小調整，考慮粗體因素
    if (useBoldFont) baseItemHeight += 2; // 粗體需要更多空間

    // 調整項目：SKU佔用的高度
    if (showSku) baseItemHeight += 8;

    // 調整項目：價格佔用的高度
    if (showPrice) baseItemHeight += 8;

    // QR碼佔用的高度
    baseItemHeight += qrCodeSize;

    // 每行之間的間距
    double rowSpacing = 5;

    // 計算一頁可以容納多少行
    int rowsPerPage = (availableHeight / (baseItemHeight + rowSpacing)).floor();

    // 計算一頁可以容納多少個標籤
    int itemsPerPage = rowsPerPage * itemsPerRow;

    // 確保數量合理（最少8個，最多40個）
    return itemsPerPage.clamp(8, 40);
  }
}

/// A page that displays a list of products
class ProductListPage extends ConsumerStatefulWidget {
  /// Constructor for ProductListPage
  const ProductListPage({super.key});

  @override
  ConsumerState<ProductListPage> createState() => _ProductListPageState();
}

class _ProductListPageState extends ConsumerState<ProductListPage> with TickerProviderStateMixin {
  String _searchQuery = '';
  String? _selectedTenantId;
  bool _showActiveOnly = false;
  _SortOption _sortOption = _SortOption.nameAsc;

  // UI狀態控制
  bool _isSelectionMode = false;
  bool _showFilters = false;
  late AnimationController _selectionModeController;
  late AnimationController _filterController;

  final Set<String> _selectedProductIds = {};
  final _searchController = TextEditingController();

  // 標記是否正在進行批量刪除操作
  bool _isBatchDeleting = false;

  // 默認列印配置
  QrPrintConfig _printConfig = const QrPrintConfig();

  @override
  void initState() {
    super.initState();
    _selectionModeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _filterController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  // 打開列印設置對話框
  Future<QrPrintConfig?> _showPrintConfigDialog(BuildContext context) async {
    QrPrintConfig config = _printConfig;

    // 根據當前每行標籤數量計算最大QR碼尺寸
    double maxQrSize = QrPrintConfig.calculateMaxQrSize(config.itemsPerRow);

    // 確保初始QR碼尺寸不超過最大值
    double qrSize = config.qrCodeSize > maxQrSize ? maxQrSize : config.qrCodeSize;

    // 自動計算每頁標籤數量
    int calculatedItemsPerPage = QrPrintConfig.calculateItemsPerPage(
      itemsPerRow: config.itemsPerRow,
      qrCodeSize: qrSize,
      showSku: config.showSku,
      showPrice: config.showPrice,
      useBoldFont: config.useBoldFont,
      nameSize: config.nameSize,
    );

    // 使用調整後的QR碼尺寸和計算出的每頁標籤數量創建新的配置
    config = QrPrintConfig(
      itemsPerRow: config.itemsPerRow,
      itemsPerPage: calculatedItemsPerPage,
      labelWidth: config.labelWidth,
      labelHeight: config.labelHeight,
      qrCodeSize: qrSize,
      showPrice: config.showPrice,
      showSku: config.showSku,
      useBoldFont: config.useBoldFont,
      nameSize: config.nameSize,
    );

    return showDialog<QrPrintConfig>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // 計算當前設置下的最大QR碼尺寸
            double currentMaxQrSize = QrPrintConfig.calculateMaxQrSize(config.itemsPerRow);

            // 重新計算當前設置下的每頁標籤數量
            int currentItemsPerPage = QrPrintConfig.calculateItemsPerPage(
              itemsPerRow: config.itemsPerRow,
              qrCodeSize: config.qrCodeSize,
              showSku: config.showSku,
              showPrice: config.showPrice,
              useBoldFont: config.useBoldFont,
              nameSize: config.nameSize,
            );

            return AlertDialog(
              title: const Text('自定義列印樣式'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('標籤佈局', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),

                    // 每行标签数量
                    Row(
                      children: [
                        const Text('每行標籤數量：'),
                        const Spacer(),
                        DropdownButton<int>(
                          value: config.itemsPerRow,
                          items:
                              [2, 3, 4, 5, 6]
                                  .map(
                                    (value) =>
                                        DropdownMenuItem<int>(value: value, child: Text('$value')),
                                  )
                                  .toList(),
                          onChanged: (value) {
                            if (value != null) {
                              // 計算新的每行標籤數量下的最大QR碼尺寸
                              double newMaxQrSize = QrPrintConfig.calculateMaxQrSize(value);
                              // 如果當前QR碼尺寸超過最大值，則自動調整
                              double newQrSize =
                                  config.qrCodeSize > newMaxQrSize
                                      ? newMaxQrSize
                                      : config.qrCodeSize;

                              // 計算新的每頁標籤數量
                              int newItemsPerPage = QrPrintConfig.calculateItemsPerPage(
                                itemsPerRow: value,
                                qrCodeSize: newQrSize,
                                showSku: config.showSku,
                                showPrice: config.showPrice,
                                useBoldFont: config.useBoldFont,
                                nameSize: config.nameSize,
                              );

                              setState(() {
                                config = QrPrintConfig(
                                  itemsPerRow: value,
                                  itemsPerPage: newItemsPerPage,
                                  labelWidth: config.labelWidth,
                                  labelHeight: config.labelHeight,
                                  qrCodeSize: newQrSize,
                                  showPrice: config.showPrice,
                                  showSku: config.showSku,
                                  useBoldFont: config.useBoldFont,
                                  nameSize: config.nameSize,
                                );
                              });
                            }
                          },
                        ),
                      ],
                    ),

                    // 每页标签数量
                    Row(
                      children: [
                        const Text('每頁標籤數量：'),
                        const Spacer(),
                        Text(
                          '${config.itemsPerPage} (自動計算)',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      '根據您的設置自動計算出最佳標籤數量',
                      style: TextStyle(
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 8),

                    const Divider(),
                    const Text('標籤內容', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),

                    // 显示SKU
                    SwitchListTile(
                      title: const Text('顯示SKU'),
                      value: config.showSku,
                      contentPadding: EdgeInsets.zero,
                      onChanged: (value) {
                        // 計算新的每頁標籤數量
                        int newItemsPerPage = QrPrintConfig.calculateItemsPerPage(
                          itemsPerRow: config.itemsPerRow,
                          qrCodeSize: config.qrCodeSize,
                          showSku: value,
                          showPrice: config.showPrice,
                          useBoldFont: config.useBoldFont,
                          nameSize: config.nameSize,
                        );

                        setState(() {
                          config = QrPrintConfig(
                            itemsPerRow: config.itemsPerRow,
                            itemsPerPage: newItemsPerPage,
                            labelWidth: config.labelWidth,
                            labelHeight: config.labelHeight,
                            qrCodeSize: config.qrCodeSize,
                            showPrice: config.showPrice,
                            showSku: value,
                            useBoldFont: config.useBoldFont,
                            nameSize: config.nameSize,
                          );
                        });
                      },
                    ),

                    // 显示价格
                    SwitchListTile(
                      title: const Text('顯示價格'),
                      value: config.showPrice,
                      contentPadding: EdgeInsets.zero,
                      onChanged: (value) {
                        // 計算新的每頁標籤數量
                        int newItemsPerPage = QrPrintConfig.calculateItemsPerPage(
                          itemsPerRow: config.itemsPerRow,
                          qrCodeSize: config.qrCodeSize,
                          showSku: config.showSku,
                          showPrice: value,
                          useBoldFont: config.useBoldFont,
                          nameSize: config.nameSize,
                        );

                        setState(() {
                          config = QrPrintConfig(
                            itemsPerRow: config.itemsPerRow,
                            itemsPerPage: newItemsPerPage,
                            labelWidth: config.labelWidth,
                            labelHeight: config.labelHeight,
                            qrCodeSize: config.qrCodeSize,
                            showPrice: value,
                            showSku: config.showSku,
                            useBoldFont: config.useBoldFont,
                            nameSize: config.nameSize,
                          );
                        });
                      },
                    ),

                    // 使用加粗字体
                    SwitchListTile(
                      title: const Text('加粗商品名稱'),
                      value: config.useBoldFont,
                      contentPadding: EdgeInsets.zero,
                      onChanged: (value) {
                        // 計算新的每頁標籤數量
                        int newItemsPerPage = QrPrintConfig.calculateItemsPerPage(
                          itemsPerRow: config.itemsPerRow,
                          qrCodeSize: config.qrCodeSize,
                          showSku: config.showSku,
                          showPrice: config.showPrice,
                          useBoldFont: value,
                          nameSize: config.nameSize,
                        );

                        setState(() {
                          config = QrPrintConfig(
                            itemsPerRow: config.itemsPerRow,
                            itemsPerPage: newItemsPerPage,
                            labelWidth: config.labelWidth,
                            labelHeight: config.labelHeight,
                            qrCodeSize: config.qrCodeSize,
                            showPrice: config.showPrice,
                            showSku: config.showSku,
                            useBoldFont: value,
                            nameSize: config.nameSize,
                          );
                        });
                      },
                    ),

                    const Divider(),
                    const Text('尺寸設置', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),

                    // 商品名称字体大小
                    const Text('商品名稱字體大小：'),
                    const SizedBox(height: 4),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Slider(
                          value: config.nameSize,
                          min: 4,
                          max: 10,
                          divisions: 6,
                          label: config.nameSize.toStringAsFixed(1),
                          onChanged: (value) {
                            // 計算新的每頁標籤數量
                            int newItemsPerPage = QrPrintConfig.calculateItemsPerPage(
                              itemsPerRow: config.itemsPerRow,
                              qrCodeSize: config.qrCodeSize,
                              showSku: config.showSku,
                              showPrice: config.showPrice,
                              useBoldFont: config.useBoldFont,
                              nameSize: value,
                            );

                            setState(() {
                              config = QrPrintConfig(
                                itemsPerRow: config.itemsPerRow,
                                itemsPerPage: newItemsPerPage,
                                labelWidth: config.labelWidth,
                                labelHeight: config.labelHeight,
                                qrCodeSize: config.qrCodeSize,
                                showPrice: config.showPrice,
                                showSku: config.showSku,
                                useBoldFont: config.useBoldFont,
                                nameSize: value,
                              );
                            });
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    // 二维码大小
                    const Text('二維碼大小：'),
                    const SizedBox(height: 4),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Slider(
                          value: config.qrCodeSize,
                          min: 30,
                          max: currentMaxQrSize,
                          divisions: 8,
                          label: config.qrCodeSize.toStringAsFixed(0),
                          onChanged: (value) {
                            // 計算新的每頁標籤數量
                            int newItemsPerPage = QrPrintConfig.calculateItemsPerPage(
                              itemsPerRow: config.itemsPerRow,
                              qrCodeSize: value,
                              showSku: config.showSku,
                              showPrice: config.showPrice,
                              useBoldFont: config.useBoldFont,
                              nameSize: config.nameSize,
                            );

                            setState(() {
                              config = QrPrintConfig(
                                itemsPerRow: config.itemsPerRow,
                                itemsPerPage: newItemsPerPage,
                                labelWidth: config.labelWidth,
                                labelHeight: config.labelHeight,
                                qrCodeSize: value,
                                showPrice: config.showPrice,
                                showSku: config.showSku,
                                useBoldFont: config.useBoldFont,
                                nameSize: config.nameSize,
                              );
                            });
                          },
                        ),
                      ],
                    ),

                    // 添加提示信息
                    Text(
                      '提示：每行標籤數量為 ${config.itemsPerRow} 時，二維碼大小不可超過 ${currentMaxQrSize.toStringAsFixed(0)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.secondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(config);
                  },
                  child: const Text('確認'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // 生成批量產品二維碼的PDF，根據列印配置自定義生成方式
  Future<Uint8List> _generateBatchProductQrPdf(List<ProductEntity> products) async {
    final pdf = pw.Document();
    final itemsPerPage = _printConfig.itemsPerPage;
    final itemsPerRow = _printConfig.itemsPerRow;
    final qrCodeSize = _printConfig.qrCodeSize;
    final showPrice = _printConfig.showPrice;
    final showSku = _printConfig.showSku;
    final useBoldFont = _printConfig.useBoldFont;
    final nameSize = _printConfig.nameSize;

    // 根據標籤數量計算每個標籤的實際尺寸
    final labelWidth = (PdfPageFormat.a4.width - 40) / itemsPerRow;
    final padding = 5.0;
    final contentWidth = labelWidth - (padding * 2);

    // 額外安全檢查：確保QR碼尺寸不會超過標籤寬度的80%
    final safeMaxQrSize = contentWidth * 0.8;
    final finalQrSize = qrCodeSize > safeMaxQrSize ? safeMaxQrSize : qrCodeSize;

    // 調試信息
    print('標籤參數 - 每行數量: $itemsPerRow, 標籤寬度: $labelWidth, 內容寬度: $contentWidth');
    print('QR碼參數 - 設置大小: $qrCodeSize, 安全最大值: $safeMaxQrSize, 最終使用: $finalQrSize');

    for (int i = 0; i < products.length; i += itemsPerPage) {
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4.copyWith(
            marginLeft: 20,
            marginRight: 20,
            marginTop: 20,
            marginBottom: 20,
          ),
          build: (pw.Context context) {
            final currentBatch = products.skip(i).take(itemsPerPage).toList();

            // 創建標籤網格
            return pw.GridView(
              crossAxisCount: itemsPerRow,
              childAspectRatio: 1.0,
              crossAxisSpacing: 5,
              mainAxisSpacing: 5,
              children:
                  currentBatch.map((product) {
                    // 計算每個標籤的可用空間（考慮內容）
                    double availableHeight = labelWidth - (padding * 2);

                    // 計算文本佔用的空間
                    double textSpace = nameSize * 2; // 基本文本空間
                    if (showSku) textSpace += 8;
                    if (showPrice) textSpace += 8;

                    // 計算QR碼的可用空間
                    double availableQrSpace = availableHeight - textSpace - 10; // 10爲額外間距

                    // 確保二維碼不會超出標籤寬度和高度
                    final safeQrSize = finalQrSize.clamp(30.0, availableQrSpace.clamp(30.0, 150.0));

                    return pw.Container(
                      padding: pw.EdgeInsets.all(padding),
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.grey, width: 0.5),
                        borderRadius: pw.BorderRadius.circular(3),
                      ),
                      child: pw.Column(
                        mainAxisSize: pw.MainAxisSize.min,
                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                        children: [
                          // 商品名稱
                          pw.Text(
                            product.name,
                            style: pw.TextStyle(
                              fontSize: nameSize,
                              fontWeight: useBoldFont ? pw.FontWeight.bold : pw.FontWeight.normal,
                            ),
                            textAlign: pw.TextAlign.center,
                            maxLines: 2,
                          ),
                          pw.SizedBox(height: 2),

                          // SKU (可選)
                          if (showSku) ...[
                            pw.Text(product.sku, style: const pw.TextStyle(fontSize: 6)),
                            pw.SizedBox(height: 2),
                          ],

                          // 價格 (可選)
                          if (showPrice) ...[
                            pw.Text(
                              '\$${product.price.toStringAsFixed(2)}',
                              style: pw.TextStyle(fontSize: 7, fontWeight: pw.FontWeight.bold),
                            ),
                            pw.SizedBox(height: 2),
                          ],

                          // 二維碼 (使用安全大小)
                          pw.Expanded(
                            child: pw.Center(
                              child: pw.SizedBox(
                                width: safeQrSize,
                                height: safeQrSize,
                                child: pw.BarcodeWidget(
                                  barcode: pw.Barcode.qrCode(),
                                  data: product.barcode,
                                  width: safeQrSize,
                                  height: safeQrSize,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
            );
          },
        ),
      );
    }
    return pdf.save();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _selectionModeController.dispose();
    _filterController.dispose();
    super.dispose();
  }

  // 切換選擇狀態
  void _toggleSelection(String productId) {
    setState(() {
      if (_selectedProductIds.contains(productId)) {
        _selectedProductIds.remove(productId);
        if (_selectedProductIds.isEmpty) {
          _exitSelectionMode();
        }
      } else {
        _selectedProductIds.add(productId);
      }
    });
  }

  // 列印單個產品標籤（新UI方法）
  Future<void> _printSingleProduct(ProductEntity product) async {
    try {
      // 先顯示列印配置對話框
      final newConfig = await _showPrintConfigDialog(context);
      if (newConfig != null) {
        setState(() {
          _printConfig = newConfig;
        });
      } else {
        // 用戶取消了列印
        return;
      }

      final pdfBytes = await _generateBatchProductQrPdf([product]);
      final printingService = ref.read(printingServiceProvider);
      final printResult = await printingService.printPdf(
        pdfBytes,
        name: "Product_QR_${product.sku}",
      );

      // 只有在列印成功時才顯示成功消息
      if (mounted && printResult) {
        showSuccessSnackBar(context, '成功列印產品標籤');
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar(context, "列印二維碼失敗: $e");
      }
    }
  }

  // 預覽單個產品標籤（新UI方法）
  Future<void> _previewSingleProduct(ProductEntity product) async {
    try {
      // 先顯示列印配置對話框
      final newConfig = await _showPrintConfigDialog(context);
      if (newConfig != null) {
        setState(() {
          _printConfig = newConfig;
        });
      } else {
        // 用戶取消了預覽
        return;
      }

      final pdfBytes = await _generateBatchProductQrPdf([product]);
      if (context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => PdfPreviewPage(
                  pdfData: pdfBytes,
                  title: 'QR Code Preview',
                  filename: "Product_QR_${product.sku}.pdf",
                ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar(context, "生成二維碼預覽失敗: $e");
      }
    }
  }

  // 退出選擇模式
  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedProductIds.clear();
    });
    _selectionModeController.reverse();
  }

  // 進入選擇模式
  void _enterSelectionMode(String productId) {
    setState(() {
      _isSelectionMode = true;
      _selectedProductIds.add(productId);
    });
    _selectionModeController.forward();
  }

  // 選擇所有產品
  void _selectAll() {
    final storeId = ref.read(selectedStoreIdProvider);

    // 根據過濾條件獲取當前顯示的產品列表
    final productsAsync =
        _selectedTenantId != null || ref.read(userRoleProvider) != 'admin'
            ? ref.read(
              tenantProductsProvider((
                storeId: storeId,
                tenantId: _selectedTenantId ?? ref.read(currentUserProvider)?.tenantId ?? '',
              )),
            )
            : ref.read(storeProductsProvider(storeId));

    productsAsync.whenData((products) {
      final filteredProducts =
          products.where((product) {
            if (_showActiveOnly && !product.active) {
              return false;
            }
            if (_searchQuery.isNotEmpty) {
              final query = _searchQuery.toLowerCase();
              return product.name.toLowerCase().contains(query) ||
                  product.sku.toLowerCase().contains(query) ||
                  product.barcode.toLowerCase().contains(query);
            }
            return true;
          }).toList();

      setState(() {
        _selectedProductIds.clear();
        _selectedProductIds.addAll(filteredProducts.map((p) => p.id));
      });
    });
  }

  // 添加取消全選功能
  void _unselectAll() {
    setState(() {
      _selectedProductIds.clear();
      if (!_isSelectionMode) {
        _exitSelectionMode();
      }
    });
  }

  // 檢查是否所有顯示的產品都已被選中
  bool _areAllCurrentProductsSelected(List<ProductEntity> filteredProducts) {
    return filteredProducts.isNotEmpty &&
        filteredProducts.every((product) => _selectedProductIds.contains(product.id));
  }

  @override
  Widget build(BuildContext context) {
    final userRole = ref.watch(userRoleProvider);
    final storeId = ref.watch(selectedStoreIdProvider);
    final isAdmin = userRole == 'admin';

    // Listen for product CRUD state changes
    ref.listen<ProductCrudState>(productCrudNotifierProvider, (previous, current) {
      if (current.successMessage != null && previous?.successMessage != current.successMessage) {
        if (context.mounted && !_isBatchDeleting) {
          // 只有在非批量刪除操作時才顯示單個成功消息
          showSuccessSnackBar(context, current.successMessage!);
        }
      }
      if (current.errorMessage != null && previous?.errorMessage != current.errorMessage) {
        if (context.mounted) {
          showErrorSnackBar(context, current.errorMessage!);
          // Messages will be auto-cleared by the notifier
        }
      }
    });

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      // 改進的AppBar
      appBar: _buildAppBar(context, isAdmin),
      body: Stack(
        children: [
          Column(
            children: [
              // 改進的搜索和過濾欄
              _buildSearchAndFilterSection(context, isAdmin, storeId),
              // 產品列表
              Expanded(child: _buildProductList(storeId, isAdmin)),
            ],
          ),
          // 浮動批量操作欄
          if (_isSelectionMode) _buildFloatingBatchActions(context, storeId),
        ],
      ),
      // 改進的浮動按鈕
      floatingActionButton: _isSelectionMode ? null : _buildFloatingActionButton(context, storeId),
    );
  }

  // 改進的AppBar
  PreferredSizeWidget _buildAppBar(BuildContext context, bool isAdmin) {
    return AppBar(
      title: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child:
            _isSelectionMode
                ? Text(
                  '已選擇 ${_selectedProductIds.length} 個產品',
                  key: const ValueKey('selection'),
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                )
                : const Text(
                  '產品管理',
                  key: ValueKey('normal'),
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
      ),
      leading:
          _isSelectionMode
              ? IconButton(icon: const Icon(Icons.close), onPressed: _exitSelectionMode)
              : null,
      // actions: [if (isAdmin) const StoreSelector()],
    );
  }

  // 改進的搜索和過濾區域
  Widget _buildSearchAndFilterSection(BuildContext context, bool isAdmin, String storeId) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 主搜索欄
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: '搜索產品名稱、SKU或條碼...',
                        hintStyle: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                        ),
                        prefixIcon: Icon(
                          Icons.search,
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                        ),
                        suffixIcon:
                            _searchQuery.isNotEmpty
                                ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    setState(() {
                                      _searchController.clear();
                                      _searchQuery = '';
                                    });
                                  },
                                )
                                : null,
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                        });
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // 過濾按鈕
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: TextButton.icon(
                    icon: Icon(_showFilters ? Icons.filter_list_off : Icons.filter_list),
                    label: Text('篩選'),
                    style: TextButton.styleFrom(
                      foregroundColor: Theme.of(context).colorScheme.onSurface,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    onPressed: () {
                      setState(() => _showFilters = !_showFilters);
                      if (_showFilters) {
                        _filterController.forward();
                      } else {
                        _filterController.reverse();
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),
                // 全選按鈕
                Consumer(
                  builder: (context, ref, _) {
                    // 獲取當前過濾後的產品列表
                    final storeId = ref.watch(selectedStoreIdProvider);
                    final productsAsync =
                        _selectedTenantId != null || ref.watch(userRoleProvider) != 'admin'
                            ? ref.watch(
                              tenantProductsProvider((
                                storeId: storeId,
                                tenantId:
                                    _selectedTenantId ??
                                    ref.read(currentUserProvider)?.tenantId ??
                                    '',
                              )),
                            )
                            : ref.watch(storeProductsProvider(storeId));

                    return productsAsync.when(
                      data: (products) {
                        // 過濾產品
                        final filteredProducts =
                            products.where((product) {
                              if (_showActiveOnly && !product.active) {
                                return false;
                              }
                              if (_searchQuery.isNotEmpty) {
                                final query = _searchQuery.toLowerCase();
                                return product.name.toLowerCase().contains(query) ||
                                    product.sku.toLowerCase().contains(query) ||
                                    product.barcode.toLowerCase().contains(query);
                              }
                              return true;
                            }).toList();

                        // 檢查是否所有產品都已選中
                        final allSelected = _areAllCurrentProductsSelected(filteredProducts);

                        return Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primaryContainer,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: TextButton.icon(
                            icon: Icon(allSelected ? Icons.deselect : Icons.select_all),
                            label: Text(allSelected ? '取消全選' : '全選'),
                            style: TextButton.styleFrom(
                              foregroundColor: Theme.of(context).colorScheme.onPrimaryContainer,
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            ),
                            onPressed: () {
                              if (!_isSelectionMode) {
                                setState(() {
                                  _isSelectionMode = true;
                                });
                                _selectionModeController.forward();
                              }

                              if (allSelected) {
                                _unselectAll();
                              } else {
                                _selectAll();
                              }
                            },
                          ),
                        );
                      },
                      loading:
                          () => Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primaryContainer,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: TextButton.icon(
                              icon: const Icon(Icons.select_all),
                              label: const Text('全選'),
                              style: TextButton.styleFrom(
                                foregroundColor: Theme.of(context).colorScheme.onPrimaryContainer,
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              ),
                              onPressed: () {
                                setState(() {
                                  _isSelectionMode = true;
                                });
                                _selectionModeController.forward();
                                _selectAll();
                              },
                            ),
                          ),
                      error:
                          (_, __) => Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primaryContainer,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: TextButton.icon(
                              icon: const Icon(Icons.select_all),
                              label: const Text('全選'),
                              style: TextButton.styleFrom(
                                foregroundColor: Theme.of(context).colorScheme.onPrimaryContainer,
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              ),
                              onPressed: () {
                                setState(() {
                                  _isSelectionMode = true;
                                });
                                _selectionModeController.forward();
                                _selectAll();
                              },
                            ),
                          ),
                    );
                  },
                ),
              ],
            ),
          ),

          // 可展開的過濾選項
          AnimatedBuilder(
            animation: _filterController,
            builder: (context, child) {
              return SizeTransition(sizeFactor: _filterController, child: child!);
            },
            child: Container(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Column(
                children: [
                  Row(
                    children: [
                      // 排序選擇
                      Expanded(
                        flex: 2,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surfaceContainerHighest,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<_SortOption>(
                              value: _sortOption,
                              isExpanded: true,
                              icon: const Icon(Icons.keyboard_arrow_down),
                              items:
                                  _SortOption.values.map((option) {
                                    return DropdownMenuItem<_SortOption>(
                                      value: option,
                                      child: Text(
                                        option.displayName,
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                    );
                                  }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    _sortOption = value;
                                  });
                                }
                              },
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      // 活躍狀態過濾
                      FilterChip(
                        label: const Text('僅顯示活躍'),
                        selected: _showActiveOnly,
                        onSelected: (selected) {
                          setState(() {
                            _showActiveOnly = selected;
                          });
                        },
                        backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                        selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                        checkmarkColor: Theme.of(context).colorScheme.primary,
                      ),
                    ],
                  ),
                  // 租戶選擇器（管理員）
                  if (isAdmin) ...[const SizedBox(height: 12), _buildTenantSelector(storeId)],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 浮動批量操作欄
  Widget _buildFloatingBatchActions(BuildContext context, String storeId) {
    return Positioned(
      bottom: 24,
      left: 16,
      right: 16,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 1),
          end: Offset.zero,
        ).animate(CurvedAnimation(parent: _selectionModeController, curve: Curves.easeOutCubic)),
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(24),
          color: Theme.of(context).colorScheme.primaryContainer,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildBatchActionButton(
                  icon: Icons.print,
                  label: '批量列印',
                  onPressed: () => _batchPrintQrCodes(context, ref, storeId),
                ),
                Container(
                  width: 1,
                  height: 32,
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                ),
                _buildBatchActionButton(
                  icon: Icons.delete,
                  label: '批量刪除',
                  color: Colors.red,
                  onPressed: () => _showDeleteConfirmation(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBatchActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    Color? color,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color ?? Theme.of(context).colorScheme.primary, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color ?? Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 改進的浮動按鈕
  Widget _buildFloatingActionButton(BuildContext context, String storeId) {
    return FloatingActionButton.extended(
      onPressed: () => _navigateToProductForm(context, storeId),
      icon: const Icon(Icons.add),
      label: const Text('添加產品'),
      backgroundColor: Theme.of(context).colorScheme.primary,
      foregroundColor: Theme.of(context).colorScheme.onPrimary,
    );
  }

  Widget _buildTenantSelector(String storeId) {
    return Consumer(
      builder: (context, ref, _) {
        final tenantsAsyncValue = ref.watch(tenantsProvider(storeId));

        return tenantsAsyncValue.when(
          data: (tenants) {
            if (tenants.isEmpty) {
              return const Text('No tenants available. Please create a tenant first.');
            }

            return DropdownButtonFormField<String?>(
              decoration: const InputDecoration(
                labelText: 'Filter by Tenant',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16),
              ),
              value: _selectedTenantId,
              items: [
                const DropdownMenuItem<String?>(value: null, child: Text('All Tenants')),
                ...tenants.map(
                  (tenant) => DropdownMenuItem<String?>(value: tenant.id, child: Text(tenant.name)),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedTenantId = value;
                });
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, _) => Text('Error loading tenants: $error'),
        );
      },
    );
  }

  Widget _buildProductList(String storeId, bool isAdmin) {
    // If admin with tenant filter or tenant user
    if (_selectedTenantId != null || !isAdmin) {
      final tenantId = _selectedTenantId ?? ref.watch(currentUserProvider)?.tenantId ?? '';

      if (tenantId.isEmpty) {
        return const Center(child: Text('You need to be assigned to a tenant to view products'));
      }

      final productsAsyncValue = ref.watch(
        tenantProductsProvider((storeId: storeId, tenantId: tenantId)),
      );

      return productsAsyncValue.when(
        data: (products) => _buildFilteredProductList(products),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, _) => Center(child: Text('載入產品時發生錯誤: $error')),
      );
    }
    // Admin viewing all products
    else {
      final productsAsyncValue = ref.watch(storeProductsProvider(storeId));

      return productsAsyncValue.when(
        data: (products) => _buildFilteredProductList(products),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, _) => Center(child: Text('載入產品時發生錯誤: $error')),
      );
    }
  }

  Widget _buildFilteredProductList(List<ProductEntity> products) {
    // Apply filters
    var filteredProducts =
        products.where((product) {
          // Filter by active status if enabled
          if (_showActiveOnly && !product.active) {
            return false;
          }

          // Filter by search query if not empty
          if (_searchQuery.isNotEmpty) {
            final query = _searchQuery.toLowerCase();
            return product.name.toLowerCase().contains(query) ||
                product.sku.toLowerCase().contains(query) ||
                product.barcode.toLowerCase().contains(query);
          }

          return true;
        }).toList();

    // Apply sorting
    _sortProducts(filteredProducts);

    if (filteredProducts.isEmpty) {
      return _buildEmptyState(products.isEmpty);
    }

    // 檢查是否所有過濾後的產品都已選中
    final bool allSelected =
        filteredProducts.isNotEmpty &&
        filteredProducts.every((product) => _selectedProductIds.contains(product.id));

    return Column(
      children: [
        // 全選欄

        // 產品列表
        Expanded(
          child: ListView.builder(
            itemCount: filteredProducts.length,
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemBuilder: (context, index) {
              final product = filteredProducts[index];
              return _buildProductCard(context, product);
            },
          ),
        ),
      ],
    );
  }

  // 空狀態視圖
  Widget _buildEmptyState(bool isCompletelyEmpty) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Icon(
                  isCompletelyEmpty ? Icons.inventory_2_outlined : Icons.search_off_outlined,
                  size: 64,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: 32),
            Text(
              isCompletelyEmpty ? '還沒有產品' : '沒有符合条件的產品',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              isCompletelyEmpty ? '點擊下方按鈕添加第一個產品' : '嘗試調整搜索條件或重置過濾器',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            if (isCompletelyEmpty) ...[
              const SizedBox(height: 40),
              ElevatedButton.icon(
                onPressed: () => _navigateToProductForm(context, ref.read(selectedStoreIdProvider)),
                icon: const Icon(Icons.add),
                label: const Text('添加產品'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
              ),
            ] else ...[
              const SizedBox(height: 24),
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _searchController.clear();
                    _searchQuery = '';
                    _showActiveOnly = false;
                    _sortOption = _SortOption.nameAsc;
                    _selectedTenantId = null;
                  });
                },
                icon: const Icon(Icons.refresh),
                label: const Text('重置所有過濾器'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _sortProducts(List<ProductEntity> products) {
    switch (_sortOption) {
      case _SortOption.nameAsc:
        products.sort((a, b) => a.name.compareTo(b.name));
        break;
      case _SortOption.nameDesc:
        products.sort((a, b) => b.name.compareTo(a.name));
        break;
      case _SortOption.priceAsc:
        products.sort((a, b) => a.price.compareTo(b.price));
        break;
      case _SortOption.priceDesc:
        products.sort((a, b) => b.price.compareTo(a.price));
        break;
      case _SortOption.stockAsc:
        products.sort((a, b) => a.stock.compareTo(b.stock));
        break;
      case _SortOption.stockDesc:
        products.sort((a, b) => b.stock.compareTo(a.stock));
        break;
    }
  }

  // 改進的產品卡片
  Widget _buildProductCard(BuildContext context, ProductEntity product) {
    final isLowStock = product.isLowStock();
    final isSelected = _selectedProductIds.contains(product.id);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border:
            isSelected ? Border.all(color: Theme.of(context).colorScheme.primary, width: 2) : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            if (_isSelectionMode) {
              _toggleSelection(product.id);
            } else {
              _navigateToProductForm(context, product.storeId, product: product);
            }
          },
          onLongPress: () {
            if (!_isSelectionMode) {
              _enterSelectionMode(product.id);
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 選擇指示器
                    if (_isSelectionMode)
                      Container(
                        margin: const EdgeInsets.only(right: 12),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color:
                                isSelected
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.transparent,
                            border: Border.all(
                              color:
                                  isSelected
                                      ? Theme.of(context).colorScheme.primary
                                      : Theme.of(context).colorScheme.outline,
                              width: 2,
                            ),
                          ),
                          child:
                              isSelected
                                  ? Icon(
                                    Icons.check,
                                    size: 16,
                                    color: Theme.of(context).colorScheme.onPrimary,
                                  )
                                  : null,
                        ),
                      ),

                    // 產品信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  product.name,
                                  style: Theme.of(
                                    context,
                                  ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              // 狀態標籤
                              _buildStatusChip(product, isLowStock),
                            ],
                          ),
                          const SizedBox(height: 8),

                          // 產品詳情
                          _buildProductDetails(product, isLowStock),
                        ],
                      ),
                    ),

                    // 二維碼
                    Container(
                      margin: const EdgeInsets.only(left: 12),
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: QrCodeDisplay(data: product.barcode, size: 60),
                    ),
                  ],
                ),
                _buildProductDetailsPriceAndStock(product, isLowStock),
                // 操作按鈕
                if (!_isSelectionMode) ...[
                  const SizedBox(height: 16),
                  _buildActionButtons(context, product),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 狀態標籤
  Widget _buildStatusChip(ProductEntity product, bool isLowStock) {
    if (!product.active) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          '未啓用',
          style: TextStyle(fontSize: 12, color: Colors.grey[700], fontWeight: FontWeight.w500),
        ),
      );
    } else if (product.stock == 0) {
      // 當庫存爲0時顯示缺貨標籤
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, size: 12, color: Colors.red[700]),
            const SizedBox(width: 4),
            Text(
              '缺貨',
              style: TextStyle(fontSize: 12, color: Colors.red[700], fontWeight: FontWeight.w500),
            ),
          ],
        ),
      );
    } else if (isLowStock) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.warning, size: 12, color: Colors.orange[700]),
            const SizedBox(width: 4),
            Text(
              '庫存不足',
              style: TextStyle(
                fontSize: 12,
                color: Colors.orange[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }
    return const SizedBox.shrink();
  }

  // 產品詳情
  Widget _buildProductDetailsPriceAndStock(ProductEntity product, bool isLowStock) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // 價格
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.attach_money,
                      size: 16,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    Text(
                      product.price.toStringAsFixed(2),
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 12),
            // 庫存
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color:
                      product.stock == 0
                          ? Colors.red.withOpacity(0.1)
                          : (isLowStock
                              ? Colors.orange.withOpacity(0.1)
                              : Colors.green.withOpacity(0.1)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      product.stock == 0 ? Icons.error_outline : Icons.inventory,
                      size: 16,
                      color:
                          product.stock == 0
                              ? Colors.red
                              : (isLowStock ? Colors.orange : Colors.green),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '庫存: ${product.stock}',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color:
                            product.stock == 0
                                ? Colors.red
                                : (isLowStock ? Colors.orange : Colors.green),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 產品詳情
  Widget _buildProductDetails(ProductEntity product, bool isLowStock) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.qr_code,
              size: 16,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            const SizedBox(width: 4),
            Text(
              'SKU: ${product.sku}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Icon(
              Icons.barcode_reader,
              size: 16,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                '條碼: ${product.barcode}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
      ],
    );
  }

  // 操作按鈕
  Widget _buildActionButtons(BuildContext context, ProductEntity product) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton(
          icon: Icons.edit_outlined,
          label: '編輯',
          onPressed: () => _navigateToProductForm(context, product.storeId, product: product),
        ),
        _buildActionButton(
          icon: Icons.print_outlined,
          label: '列印',
          onPressed: () => _printSingleProduct(product),
        ),
        _buildActionButton(
          icon: Icons.preview_outlined,
          label: '預覽',
          onPressed: () => _previewSingleProduct(product),
        ),
        _buildActionButton(
          icon: Icons.delete_outline,
          label: '刪除',
          color: Colors.red,
          onPressed: () => _confirmDelete(context, product),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    Color? color,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 20,
              color: color ?? Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color ?? Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _confirmDelete(BuildContext context, ProductEntity product) {
    showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('刪除產品'),
            content: Text('確定要刪除產品 "${product.name}" 嗎？'),
            actions: [
              TextButton(onPressed: () => context.pop(false), child: const Text('取消')),
              TextButton(
                onPressed: () {
                  context.pop(true);
                  _deleteProduct(product);
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('刪除'),
              ),
            ],
          ),
    );
  }

  void _deleteProduct(ProductEntity product) async {
    try {
      await ref
          .read(productCrudNotifierProvider.notifier)
          .deleteProduct(product.storeId, product.tenantId, product.id);
    } catch (e) {
      // Error will be handled by the productCrudNotifierProvider listener
    }
  }

  void _navigateToProductForm(BuildContext context, String storeId, {ProductEntity? product}) {
    if (product != null) {
      // 調試信息
      debugPrint('準備編輯產品: ${product.id}, 名稱: ${product.name}');
      // Edit existing product - use route with extra
      context.push('/products/${product.id}/edit', extra: product);
    } else {
      // Create new product - use ProductFormPage constructor
      debugPrint('創建新產品');
      context.push('/products/new');
    }
  }

  Future<void> _showDeleteConfirmation() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('確認刪除'),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text('您確定要刪除已選擇的 ${_selectedProductIds.length} 個產品嗎？'),
                const SizedBox(height: 8),
                const Text('此操作無法撤消，產品將永久刪除。', style: TextStyle(fontWeight: FontWeight.bold)),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('刪除'),
              onPressed: () {
                Navigator.of(context).pop();
                _deleteSelectedProducts();
              },
            ),
          ],
        );
      },
    );
  }

  // 批量列印二維碼
  void _batchPrintQrCodes(BuildContext context, WidgetRef ref, String storeId) async {
    final allProductsAsync = ref.read(storeProductsProvider(storeId));
    allProductsAsync.whenData((allProducts) async {
      final productsToPrint = allProducts.where((p) => _selectedProductIds.contains(p.id)).toList();
      if (productsToPrint.isNotEmpty) {
        try {
          // 先顯示列印配置對話框
          final newConfig = await _showPrintConfigDialog(context);
          if (newConfig != null) {
            setState(() {
              _printConfig = newConfig;
            });
          } else {
            // 用戶取消了列印
            return;
          }

          final pdfBytes = await _generateBatchProductQrPdf(productsToPrint);
          final printingService = ref.read(printingServiceProvider);
          final printResult = await printingService.printPdf(pdfBytes, name: "批量產品二維碼");

          // 只有在列印成功時才顯示成功消息
          if (mounted && printResult) {
            showSuccessSnackBar(context, '成功列印 ${productsToPrint.length} 個產品標籤');
          }
        } catch (e) {
          if (mounted) {
            showErrorSnackBar(context, "批量列印二維碼失敗: $e");
          }
        }
      }
    });
  }

  void _deleteSelectedProducts() {
    final storeId = ref.read(selectedStoreIdProvider);

    // 設置批量刪除標記，避免顯示單個產品的成功消息
    setState(() {
      _isBatchDeleting = true;
    });

    final allProductsAsync = ref.read(storeProductsProvider(storeId));
    allProductsAsync.whenData((allProducts) async {
      final productsToDelete =
          allProducts.where((p) => _selectedProductIds.contains(p.id)).toList();

      // 暫時移除監聽，避免顯示每個產品的成功消息
      final productCrudNotifier = ref.read(productCrudNotifierProvider.notifier);

      bool hasError = false;
      for (final product in productsToDelete) {
        try {
          await productCrudNotifier.deleteProduct(product.storeId, product.tenantId, product.id);
        } catch (e) {
          hasError = true;
          if (mounted) {
            showErrorSnackBar(context, "刪除產品 ${product.name} 失敗: $e");
          }
        }
      }

      if (!hasError && mounted) {
        showSuccessSnackBar(context, '所選產品已成功刪除');
      }

      setState(() {
        _selectedProductIds.clear();
        _isSelectionMode = false;
        _isBatchDeleting = false; // 重置批量刪除標記
      });
    });
  }
}

/// Sort options for the product list
enum _SortOption {
  nameAsc,
  nameDesc,
  priceAsc,
  priceDesc,
  stockAsc,
  stockDesc;

  String get displayName {
    switch (this) {
      case _SortOption.nameAsc:
        return '名稱 (A-Z)';
      case _SortOption.nameDesc:
        return '名稱 (Z-A)';
      case _SortOption.priceAsc:
        return '價格 (由低到高)';
      case _SortOption.priceDesc:
        return '價格 (由高到低)';
      case _SortOption.stockAsc:
        return '庫存 (由低到高)';
      case _SortOption.stockDesc:
        return '庫存 (由高到低)';
    }
  }
}
