import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:grid_pos/core/constants/firestore_constants.dart';
import 'package:grid_pos/core/utils/timestamp_utils.dart';
import 'package:grid_pos/features/pos/domain/entities/sale_entity.dart';
import 'package:grid_pos/features/pos/domain/entities/sale_item_embed.dart';

/// Data Transfer Object for SaleEntity
class SaleDto {
  /// Converts a SaleEntity to a Firestore document data map
  static Map<String, dynamic> toFirestore(SaleEntity sale) {
    return {
      if (sale.id != null) FirestoreConstants.id: sale.id,
      FirestoreConstants.storeId: sale.storeId,
      FirestoreConstants.tenantId: sale.tenantId,
      FirestoreConstants.cashierId: sale.cashierId,
      FirestoreConstants.status: sale.status,
      FirestoreConstants.totalAmount: sale.totalAmount,
      FirestoreConstants.items:
          sale.items.map((item) => SaleItemDto.toFirestore(item)).toList(),
      FirestoreConstants.paymentType: sale.paymentType,
      FirestoreConstants.printed: sale.printed,
      FirestoreConstants.createdAt:
          sale.createdAt != null
              ? Timestamp.fromDate(sale.createdAt!)
              : FieldValue.serverTimestamp(),
      FirestoreConstants.updatedAt: FieldValue.serverTimestamp(),
    };
  }

  /// Converts a Firestore DocumentSnapshot to a SaleEntity
  static SaleEntity fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> snapshot,
    SnapshotOptions? options,
  ) {
    final data = snapshot.data()!;
    return SaleEntity(
      id: snapshot.id,
      storeId: data[FirestoreConstants.storeId] as String,
      tenantId: data[FirestoreConstants.tenantId] as String,
      cashierId: data[FirestoreConstants.cashierId] as String,
      status: data[FirestoreConstants.status] as String,
      totalAmount: (data[FirestoreConstants.totalAmount] as num).toDouble(),
      items:
          ((data[FirestoreConstants.items] as List<dynamic>?)
                  ?.map(
                    (item) =>
                        SaleItemDto.fromFirestore(item as Map<String, dynamic>),
                  )
                  .toList() ??
              []),
      paymentType: data[FirestoreConstants.paymentType] as String,
      printed: data[FirestoreConstants.printed] as bool? ?? false,
      createdAt: TimestampUtils.timestampToDateTime(
        data[FirestoreConstants.createdAt] as Timestamp?,
      ),
      updatedAt: TimestampUtils.timestampToDateTime(
        data[FirestoreConstants.updatedAt] as Timestamp?,
      ),
    );
  }
}

/// Data Transfer Object for SaleItemEmbed
class SaleItemDto {
  /// Converts a SaleItemEmbed to a Firestore document data map
  static Map<String, dynamic> toFirestore(SaleItemEmbed item) {
    return {
      'productId': item.productId,
      'tenantId': item.tenantId,
      'qty': item.qty,
      'price': item.price,
      'name': item.name,
      'sku': item.sku,
    };
  }

  /// Converts a Firestore map to a SaleItemEmbed
  static SaleItemEmbed fromFirestore(Map<String, dynamic> data) {
    return SaleItemEmbed(
      productId: data['productId'] as String,
      tenantId: data['tenantId'] as String,
      qty: data['qty'] as int,
      price: (data['price'] as num).toDouble(),
      name: data['name'] as String,
      sku: data['sku'] as String,
    );
  }
}
