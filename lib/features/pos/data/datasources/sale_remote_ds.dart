import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/core/constants/firestore_constants.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/pos/data/models/sale_dto.dart';
import 'package:grid_pos/features/pos/domain/entities/sale_entity.dart';

/// Provider for the SaleRemoteDataSource
final saleRemoteDataSourceProvider = Provider<SaleRemoteDataSource>((ref) {
  final firestore = FirebaseFirestore.instance;
  return SaleRemoteDataSourceImpl(firestore);
});

/// Interface for sales remote data source
abstract class SaleRemoteDataSource {
  /// Creates a new sale and returns its ID
  Future<String> createSale(String storeId, SaleEntity sale);

  /// Streams sales for a specific tenant
  Stream<List<SaleEntity>> watchSalesByTenant(String storeId, String tenantId);

  /// Streams sales for a specific store
  Stream<List<SaleEntity>> watchSalesByStore(String storeId);

  /// Gets a specific sale by ID
  Future<SaleEntity?> getSaleById(String storeId, String saleId);

  /// Updates a sale's status (e.g., to 'cancelled')
  Future<void> updateSaleStatus(String storeId, String saleId, String status);

  /// Updates a sale's printed status
  Future<void> updatePrintedStatus(String storeId, String saleId, bool printed);
}

/// Implementation of SaleRemoteDataSource
class SaleRemoteDataSourceImpl implements SaleRemoteDataSource {
  final FirebaseFirestore _firestore;

  /// Creates a SaleRemoteDataSourceImpl with the given Firestore instance
  SaleRemoteDataSourceImpl(this._firestore);

  @override
  Future<String> createSale(String storeId, SaleEntity sale) async {
    try {
      final salesRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.sales);

      final docRef = await salesRef.add(SaleDto.toFirestore(sale));
      Logger.info('Sale created with ID: ${docRef.id}');
      return docRef.id;
    } catch (e) {
      Logger.error('Failed to create sale', e);
      rethrow;
    }
  }

  @override
  Stream<List<SaleEntity>> watchSalesByTenant(String storeId, String tenantId) {
    try {
      final salesRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.sales)
          .where(FirestoreConstants.tenantId, isEqualTo: tenantId)
          .orderBy(FirestoreConstants.createdAt, descending: true);

      return salesRef.snapshots().map((snapshot) {
        return snapshot.docs.map((doc) {
          return SaleDto.fromFirestore(doc, null);
        }).toList();
      });
    } catch (e) {
      Logger.error('Failed to watch sales by tenant', e);
      rethrow;
    }
  }

  @override
  Stream<List<SaleEntity>> watchSalesByStore(String storeId) {
    try {
      final salesRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.sales)
          .orderBy(FirestoreConstants.createdAt, descending: true);

      return salesRef.snapshots().map((snapshot) {
        return snapshot.docs.map((doc) {
          return SaleDto.fromFirestore(doc, null);
        }).toList();
      });
    } catch (e) {
      Logger.error('Failed to watch sales by store', e);
      rethrow;
    }
  }

  @override
  Future<SaleEntity?> getSaleById(String storeId, String saleId) async {
    try {
      final docRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.sales)
          .doc(saleId);

      final doc = await docRef.get();
      if (!doc.exists) {
        return null;
      }

      return SaleDto.fromFirestore(doc, null);
    } catch (e) {
      Logger.error('Failed to get sale by ID', e);
      rethrow;
    }
  }

  @override
  Future<void> updateSaleStatus(
    String storeId,
    String saleId,
    String status,
  ) async {
    try {
      final docRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.sales)
          .doc(saleId);

      await docRef.update({
        FirestoreConstants.status: status,
        FirestoreConstants.updatedAt: FieldValue.serverTimestamp(),
      });

      Logger.info('Sale $saleId status updated to $status');
    } catch (e) {
      Logger.error('Failed to update sale status', e);
      rethrow;
    }
  }

  @override
  Future<void> updatePrintedStatus(
    String storeId,
    String saleId,
    bool printed,
  ) async {
    try {
      final docRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.sales)
          .doc(saleId);

      await docRef.update({
        FirestoreConstants.printed: printed,
        FirestoreConstants.updatedAt: FieldValue.serverTimestamp(),
      });

      Logger.info('Sale $saleId printed status updated to $printed');
    } catch (e) {
      Logger.error('Failed to update sale printed status', e);
      rethrow;
    }
  }
}
