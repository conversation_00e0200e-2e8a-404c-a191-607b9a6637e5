/// 此文件提供了 dart:html 的存根實現，用於非 web 平台
/// 因為在 Flutter 中，dart:html 只能在 web 平台上使用
library;
import 'dart:typed_data';

/// 存根類，模擬網頁 URL
class Url {
  /// 創建一個存根的 URL
  static String createObjectUrlFromBlob(dynamic _) => '';

  /// 釋放一個存根的 URL
  static void revokeObjectUrl(String _) {}
}

/// 存根類，模擬網頁窗口
class Window {
  /// 在新標籤頁打開 URL
  void open(String _, String __) {}

  /// 打印頁面
  void print() {}
}

/// 存根對象，模擬 window 實例
final window = Window();

/// 存根類，模擬 Blob
class Blob {
  /// 創建一個存根的 Blob
  Blob(List<dynamic> _, [String? __]);
}

/// 在非 Web 平台上創建 Blob URL (存根實現)
String createBlobUrl(Uint8List data, String mimeType) {
  return '';
}

/// 釋放 Blob URL (存根實現)
void revokeBlobUrl(String url) {
  // 在非 Web 平台上不需要執行任何操作
}

/// 在新標籤頁中打開 URL (存根實現)
void openUrlInNewTab(String url) {
  // 在非 Web 平台上不需要執行任何操作
}

/// 使用瀏覽器的打印功能 (存根實現)
void printPage() {
  // 在非 Web 平台上不需要執行任何操作
}
