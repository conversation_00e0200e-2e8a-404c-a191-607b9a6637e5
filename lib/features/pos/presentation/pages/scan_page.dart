// File: ./lib/features/pos/presentation/pages/scan_page.dart
// ================================================================================

import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:grid_pos/features/pos/domain/entities/cart_item.dart';
import 'package:grid_pos/features/pos/presentation/providers/cart_provider.dart';
import 'package:grid_pos/features/product_mgmt/presentation/providers/product_providers.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart';
import 'package:grid_pos/shared/widgets/custom_snackbar.dart'; // Import the shared snackbar

class ScanPage extends ConsumerStatefulWidget {
  const ScanPage({super.key});

  @override
  ConsumerState<ScanPage> createState() => _ScanPageState();
}

class _ScanPageState extends ConsumerState<ScanPage> with WidgetsBindingObserver {
  MobileScannerController? _controller;
  bool _isScanning = false; // Initial state, scanner will auto-start if controller is ready
  bool _flashOn = false;
  String? _lastScannedCode;
  DateTime? _lastScanTime;
  bool _isProcessing = false; // Indicates if a scanned barcode is being processed
  bool _isCartExpanded = true;

  // Define the scan window size (can be adjusted)
  static const double scanWindowSize = 250.0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeScanner();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (_controller == null) return;

    switch (state) {
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
      case AppLifecycleState.paused:
        try {
          _controller?.stop();
          if (mounted) {
            setState(() => _isScanning = false);
          }
        } catch (_) {} // Ignore errors
        break;
      case AppLifecycleState.resumed:
        if (!_isProcessing) {
          // Only restart if not currently processing a barcode
          try {
            _controller?.start();
            if (mounted) {
              setState(() => _isScanning = true);
            }
          } catch (_) {} // Ignore errors
        }
        break;
      case AppLifecycleState.inactive:
        try {
          _controller?.stop();
        } catch (_) {} // Ignore errors
        break;
    }
  }

  void _initializeScanner() {
    _controller = MobileScannerController(
      detectionSpeed: DetectionSpeed.noDuplicates,
      formats: [BarcodeFormat.qrCode, BarcodeFormat.ean13, BarcodeFormat.ean8],
      detectionTimeoutMs: 1000,
      autoStart: true, // Let MobileScanner handle auto-start
      facing: CameraFacing.back,
    );
    // No need to set _isScanning = true here, onDetect will handle it
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _isScanning = false;
    _controller?.stop(); // Attempt to stop
    _controller?.dispose();
    super.dispose();
  }

  void _onDetect(BarcodeCapture capture) async {
    if (!_isScanning || _isProcessing) return;

    final List<Barcode> barcodes = capture.barcodes;
    if (barcodes.isEmpty) return;

    final barcode = barcodes.first;
    final String? code = barcode.rawValue;

    if (code == null || code.isEmpty) return;

    final now = DateTime.now();
    if (_lastScannedCode == code &&
        _lastScanTime != null &&
        now.difference(_lastScanTime!).inMilliseconds < 3000) {
      // Reduced debounce time
      return;
    }

    _lastScannedCode = code;
    _lastScanTime = now;

    setState(() {
      _isProcessing = true; // Show loading overlay
      _isScanning = false; // Temporarily stop logical scanning
    });

    try {
      await _controller?.stop(); // Physically stop the camera
    } catch (_) {} // Ignore errors

    HapticFeedback.lightImpact();

    try {
      await _processScannedBarcode(code);
    } catch (e) {
      if (mounted) {
        showErrorSnackBar(context, '掃描處理失敗: $e');
      }
    } finally {
      if (mounted) {
        await Future.delayed(const Duration(milliseconds: 1200)); // Allow time for snackbar
        setState(() {
          _isProcessing = false;
          _isScanning = true; // Ready for next scan
        });
        try {
          await _controller?.start(); // Restart the camera
        } catch (e) {
          if (mounted) {
            showErrorSnackBar(context, '重新啟動掃描器失敗: $e');
          }
        }
      }
    }
  }

  Future<void> _processScannedBarcode(String barcode) async {
    if (!mounted) return;

    final storeId = ref.read(selectedStoreIdProvider);

    try {
      final product = await ref
          .read(productByBarcodeProvider((storeId: storeId, barcode: barcode)).future)
          .timeout(const Duration(seconds: 7), onTimeout: () => throw Exception('查詢超時'));

      if (!mounted) return;

      if (product == null) {
        showErrorSnackBar(context, '產品未找到: $barcode');
        return;
      }

      if (!product.active) {
        showErrorSnackBar(context, '產品 "${product.name}" 已停用');
        return;
      }

      if (product.stock <= 0) {
        showErrorSnackBar(context, '產品 "${product.name}" 已缺貨');
        return;
      }

      ref.read(cartProvider.notifier).addItem(product);

      final cartItemsCount = ref.read(cartProvider).length;
      if (_isCartExpanded && cartItemsCount > 1) {
        setState(() => _isCartExpanded = false);
      }

      showSuccessSnackBar(context, '已添加 "${product.name}"');
    } catch (e) {
      if (mounted) {
        showErrorSnackBar(context, '查找產品失敗: ${e.toString()}');
      }
    }
  }

  void _toggleFlash() async {
    try {
      await _controller?.toggleTorch();
      setState(() => _flashOn = !_flashOn);
    } catch (e) {
      if (mounted) {
        showErrorSnackBar(context, '無法切換閃光燈');
      }
    }
  }

  void _toggleCart() => setState(() => _isCartExpanded = !_isCartExpanded);

  Future<void> _restartScanner() async {
    if (_isProcessing) return; // Don't restart if already processing

    setState(() => _isProcessing = true); // Show loading for restart attempt

    try {
      await _controller?.stop();
      await Future.delayed(const Duration(milliseconds: 300));
      await _controller?.start();
      if (mounted) {
        setState(() {
          _isScanning = true;
          _isProcessing = false; // Hide loading
        });
        showSuccessSnackBar(context, '掃描器重新啟動成功');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isProcessing = false; // Hide loading
          _isScanning = false;
        });
        showErrorSnackBar(context, '重新啟動掃描器失敗: $e');
      }
    }
  }

  Rect _getScanWindow(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    // Adjust center Y to be slightly higher if cart is expanded
    final centerY =
        _isCartExpanded && ref.read(cartProvider).isNotEmpty
            ? screenSize.height *
                0.40 // Raise scan window if cart preview is larger
            : screenSize.height * 0.45; // Default center

    return Rect.fromCenter(
      center: Offset(screenSize.width / 2, centerY),
      width: scanWindowSize,
      height: scanWindowSize,
    );
  }

  @override
  Widget build(BuildContext context) {
    final cartItems = ref.watch(cartProvider);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: _buildAppBar(cartItems),
      body: Stack(
        children: [
          _buildScannerView(context),
          _buildScanningOverlay(context),
          if (cartItems.isNotEmpty) _buildCartPreview(cartItems), // Only show if cart not empty
          if (_isProcessing) _buildLoadingOverlay(), // Full screen loading overlay
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(List<CartItem> cartItems) {
    final totalItems = cartItems.fold<int>(0, (sum, item) => sum + item.quantity);
    return AppBar(
      backgroundColor: Colors.black,
      foregroundColor: Colors.white,
      elevation: 0,
      title: const Text('掃描商品'),
      leading: IconButton(icon: const Icon(Icons.arrow_back_ios), onPressed: () => context.pop()),
      actions: [
        IconButton(
          onPressed: _toggleFlash,
          icon: Icon(
            _flashOn ? Icons.flash_on : Icons.flash_off,
            color: _flashOn ? Colors.yellow : Colors.white,
          ),
        ),
        Stack(
          children: [
            IconButton(onPressed: () => context.pop(), icon: const Icon(Icons.shopping_cart)),
            if (totalItems > 0)
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(color: Colors.red, shape: BoxShape.circle),
                  constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
                  child: Text(
                    '$totalItems',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildScannerView(BuildContext context) {
    return Positioned.fill(
      child:
          _controller != null
              ? MobileScanner(
                controller: _controller!,
                scanWindow: _getScanWindow(context), // Dynamic scan window
                onDetect: (capture) {
                  if (mounted && !_isScanning && !_isProcessing) {
                    // This callback might be triggered when scanner starts
                    // Ensure logical scanning is enabled.
                    setState(() => _isScanning = true);
                  }
                  _onDetect(capture);
                },
                errorBuilder: (context, error) {
                  return Container(
                    color: Colors.black,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.error_outline, color: Colors.white, size: 64),
                          const SizedBox(height: 16),
                          const Text(
                            '相機啟動失敗',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            error.errorDetails?.message ?? '請檢查相機權限',
                            style: const TextStyle(color: Colors.white70, fontSize: 14),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(onPressed: _restartScanner, child: const Text('重試')),
                        ],
                      ),
                    ),
                  );
                },
                placeholderBuilder: (context) {
                  // This is called while MobileScanner is initializing.
                  // It's a good place to set _isScanning to false initially.
                  // Although _isScanning is already false by default, this ensures it.
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted && _isScanning) {
                      // Only set if it was mistakenly true
                      setState(() => _isScanning = false);
                    }
                  });
                  return Container(
                    color: Colors.black,
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(color: Colors.white),
                          SizedBox(height: 16),
                          Text('正在啟動相機...', style: TextStyle(color: Colors.white, fontSize: 16)),
                        ],
                      ),
                    ),
                  );
                },
              )
              : Container(
                color: Colors.black,
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(color: Colors.white),
                      SizedBox(height: 16),
                      Text('初始化掃描器...', style: TextStyle(color: Colors.white, fontSize: 16)),
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildScanningOverlay(BuildContext context) {
    final scanWindowRect = _getScanWindow(context);
    return GestureDetector(
      onDoubleTap: _restartScanner,
      child: Stack(
        children: [
          ColorFiltered(
            colorFilter: ColorFilter.mode(Colors.black.withOpacity(0.6), BlendMode.srcOut),
            child: Stack(
              children: [
                Container(
                  decoration: const BoxDecoration(
                    color: Colors.black,
                    backgroundBlendMode: BlendMode.dstOut,
                  ),
                ),
                Align(
                  alignment: Alignment(
                    (scanWindowRect.center.dx / MediaQuery.of(context).size.width) * 2 - 1,
                    (scanWindowRect.center.dy / MediaQuery.of(context).size.height) * 2 - 1,
                  ),
                  child: Container(
                    width: scanWindowRect.width,
                    height: scanWindowRect.height,
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Scan window border
          Align(
            alignment: Alignment(
              (scanWindowRect.center.dx / MediaQuery.of(context).size.width) * 2 - 1,
              (scanWindowRect.center.dy / MediaQuery.of(context).size.height) * 2 - 1,
            ),
            child: Container(
              width: scanWindowRect.width,
              height: scanWindowRect.height,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.greenAccent, width: 3),
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.7), // Semi-transparent background
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(valueColor: AlwaysStoppedAnimation<Color>(Colors.white)),
            SizedBox(height: 20),
            Text(
              '正在處理...',
              style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCartPreview(List<CartItem> cartItems) {
    final totalAmount = cartItems.fold<double>(
      0,
      (sum, item) => sum + (item.product.price * item.quantity),
    );
    final totalItemsCount = cartItems.fold<int>(0, (sum, item) => sum + item.quantity);

    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Main cart container with shadow and rounded corners
            AnimatedContainer(
              duration: const Duration(milliseconds: 550),
              curve: Curves.easeOutCubic,
              height:
                  _isCartExpanded
                      ? min(
                        MediaQuery.of(context).size.height * 0.4,
                        80 + min(cartItems.length * 65.0 + 90.0, 300.0),
                      )
                      : 80,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 8,
                    offset: const Offset(0, -3),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Cart header with chevron indicator
                  _buildCartHeader(totalItemsCount, totalAmount),

                  // Items list with animated size transition
                  if (_isCartExpanded)
                    Expanded(
                      child: ClipRRect(
                        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(20)),
                        child:
                            cartItems.isEmpty
                                ? const Center(child: Text('購物車是空的'))
                                : _buildCartItemsList(cartItems),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCartHeader(int itemCount, double totalAmount) {
    return InkWell(
      onTap: _toggleCart,
      child: Container(
        height: 80,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
          borderRadius:
              _isCartExpanded
                  ? const BorderRadius.vertical(top: Radius.circular(20))
                  : BorderRadius.circular(20),
        ),
        child: Row(
          children: [
            // Cart icon with item count badge
            Stack(
              clipBehavior: Clip.none,
              children: [
                Icon(
                  Icons.shopping_cart_outlined,
                  size: 28,
                  color: Theme.of(context).colorScheme.primary,
                ),
                if (itemCount > 0)
                  Positioned(
                    right: -5,
                    top: -5,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                      constraints: const BoxConstraints(minWidth: 18, minHeight: 18),
                      child: Text(
                        '$itemCount',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(width: 16),

            // Cart title and item count
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '購物車',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  Text(
                    '共 $itemCount 件商品',
                    style: TextStyle(
                      fontSize: 13,
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),

            // Total amount
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '\$${totalAmount.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child:
                      _isCartExpanded
                          ? const Icon(Icons.keyboard_arrow_down, size: 20, color: Colors.grey)
                          : const Icon(Icons.keyboard_arrow_up, size: 20, color: Colors.grey),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCartItemsList(List<CartItem> cartItems) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Items list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.only(top: 8),
            shrinkWrap: true,
            itemCount: cartItems.length,
            itemBuilder: (context, index) => _buildCartItemTile(cartItems[index]),
          ),
        ),

        // Action buttons
        Container(
          padding: const EdgeInsets.fromLTRB(16, 4, 16, 16),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: OutlinedButton.icon(
                  onPressed: () => ref.read(cartProvider.notifier).clearCart(),
                  icon: const Icon(Icons.delete_sweep_outlined, size: 18),
                  label: const Text('清空'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red.shade600,
                    side: BorderSide(color: Colors.red.shade200),
                    padding: const EdgeInsets.symmetric(vertical: 10),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 5,
                child: ElevatedButton.icon(
                  onPressed: () => context.pop(),
                  icon: const Icon(Icons.payment_outlined),
                  label: const Text('前往結帳'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 10),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCartItemTile(CartItem item) {
    final itemTotal = item.product.price * item.quantity;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          const SizedBox(width: 8),
          // Product info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.product.name,
                  style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  '單價: \$${item.product.price.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),

          // Quantity controls
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.remove, size: 16),
                  onPressed: () => ref.read(cartProvider.notifier).decrementItem(item.product.id),
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                  padding: EdgeInsets.zero,
                  visualDensity: VisualDensity.compact,
                ),
                SizedBox(
                  width: 30,
                  child: Text(
                    '${item.quantity}',
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.add, size: 16),
                  onPressed: () => ref.read(cartProvider.notifier).incrementItem(item.product.id),
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                  padding: EdgeInsets.zero,
                  visualDensity: VisualDensity.compact,
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),

          // Total price
          SizedBox(
            width: 60,
            child: Text(
              '\$${itemTotal.toStringAsFixed(2)}',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}
