import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:grid_pos/features/pos/domain/entities/cart_item.dart';
import 'package:grid_pos/features/pos/presentation/providers/cart_provider.dart';
import 'package:grid_pos/features/pos/presentation/providers/checkout_provider.dart';
import 'package:grid_pos/features/pos/presentation/widgets/cart_item_tile.dart';
import 'package:grid_pos/shared/widgets/custom_snackbar.dart';

/// The main POS (Point of Sale) page with enhanced UI/UX using SliverAppBar
class POSPage extends ConsumerStatefulWidget {
  /// Creates a POS page
  const POSPage({super.key});

  @override
  ConsumerState<POSPage> createState() => _POSPageState();
}

class _POSPageState extends ConsumerState<POSPage> with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _bounceController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    debugPrint("POSPage.initState: POSPage 初始化");
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic));

    _slideController.forward();
  }

  @override
  void dispose() {
    debugPrint("POSPage.dispose: POSPage 被銷毀");
    _slideController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint("POSPage.build: 開始構建POSPage");
    final cartItems = ref.watch(cartProvider);
    final checkoutState = ref.watch(checkoutProvider);

    debugPrint(
      "POSPage.build: 當前結帳狀態: isLoading=${checkoutState.isLoading}, hasCompletedSale=${checkoutState.completedSale != null}, errorMessage=${checkoutState.errorMessage}",
    );

    // Listen to checkout state changes
    ref.listen<CheckoutState>(
      checkoutProvider,
      (previous, current) {
        debugPrint("POSPage.listen: 結帳狀態變化檢測");
        debugPrint(
          "POSPage.listen: 之前狀態: isLoading=${previous?.isLoading}, hasCompletedSale=${previous?.completedSale != null}",
        );
        debugPrint(
          "POSPage.listen: 當前狀態: isLoading=${current.isLoading}, hasCompletedSale=${current.completedSale != null}",
        );

        if (current.errorMessage != null && (previous?.errorMessage != current.errorMessage)) {
          showErrorSnackBar(context, current.errorMessage!);
        }

        if (previous?.completedSale == null && current.completedSale != null) {
          debugPrint("POSPage.listen: 檢測到新的結帳完成，準備顯示成功對話框");
          ref.read(cartProvider.notifier).clearCart();
          _bounceController.forward().then((_) => _bounceController.reset());

          // 使用WidgetsBinding.instance.addPostFrameCallback確保在當前frame繪製完成後再顯示對話框
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              debugPrint("POSPage: 準備顯示結帳成功對話框，BuildContext mounted: $mounted");
              _showCheckoutSuccessDialog();
            } else {
              debugPrint(
                "POSPage: BuildContext is not mounted when trying to show success dialog.",
              );
            }
          });
        }
      },
      onError: (error, stackTrace) {
        debugPrint("POSPage.listen: 監聽結帳狀態時發生錯誤: $error");
      },
    );

    final totalItems = cartItems.fold<int>(0, (sum, item) => sum + item.quantity);
    final totalAmount = cartItems.fold<double>(
      0,
      (sum, item) => sum + (item.product.price * item.quantity),
    );

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Theme.of(context).primaryColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 20),
          tooltip: '返回',
          onPressed: () => context.pop(),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '銷售終端',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.white.withOpacity(0.3)),
            ),
            child: IconButton(
              icon: const Icon(Icons.cleaning_services, color: Colors.white, size: 20),
              tooltip: '清空購物車',
              onPressed:
                  cartItems.isEmpty || checkoutState.isLoading
                      ? null
                      : () => _showClearCartDialog(),
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          // 主要內容區域
          Column(
            children: [
              // 購物車狀態欄
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor, // 與 AppBar 相同的背景色
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(16),
                    bottomRight: Radius.circular(16),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatusItem(
                      icon: Icons.shopping_cart_outlined,
                      label: '商品數量',
                      value: totalItems.toString(),
                      color: Colors.white, // 改爲白色以配合深色背景
                    ),
                    Container(
                      width: 1,
                      height: 24,
                      color: Colors.white.withOpacity(0.3),
                    ), // 分隔線也改爲白色半透明
                    _buildStatusItem(
                      icon: Icons.attach_money,
                      label: '總金額',
                      value: '\$${totalAmount.toStringAsFixed(2)}',
                      color: Colors.white, // 改爲白色以配合深色背景
                    ),
                  ],
                ),
              ),
              // 可滾動內容
              Expanded(
                child:
                    cartItems.isEmpty
                        ? SlideTransition(position: _slideAnimation, child: _buildEmptyCartView())
                        : ListView.builder(
                          padding: const EdgeInsets.only(bottom: 120),
                          itemCount: cartItems.length,
                          itemBuilder: (context, index) {
                            final cartItem = cartItems[index];
                            return SlideTransition(
                              position: _slideAnimation,
                              child: AnimatedContainer(
                                duration: const Duration(milliseconds: 300),
                                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                                child: CartItemTile(
                                  cartItem: cartItem,
                                  onIncrement:
                                      checkoutState.isLoading
                                          ? () {}
                                          : () => ref
                                              .read(cartProvider.notifier)
                                              .incrementItem(cartItem.product.id),
                                  onDecrement:
                                      checkoutState.isLoading
                                          ? () {}
                                          : () => ref
                                              .read(cartProvider.notifier)
                                              .decrementItem(cartItem.product.id),
                                  onRemove:
                                      checkoutState.isLoading
                                          ? () {}
                                          : () => ref
                                              .read(cartProvider.notifier)
                                              .removeItem(cartItem.product.id),
                                ),
                              ),
                            );
                          },
                        ),
              ),
            ],
          ),
          // 固定在底部的按鈕區域
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: _buildEnhancedBottomSection(context, ref, cartItems, checkoutState.isLoading),
          ),
          // 載入覆蓋層
          if (checkoutState.isLoading) _buildLoadingOverlay(),
        ],
      ),
    );
  }

  Widget _buildStatusItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 2),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.white.withOpacity(0.8), // 使用白色半透明
            fontSize: 10,
          ),
        ),
        const SizedBox(height: 1),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color, // 保持傳入的顏色（白色）
          ),
        ),
      ],
    );
  }

  // 移除這個方法，因爲我們直接在 CustomScrollView 中處理內容

  Widget _buildEmptyCartView() {
    return Padding(
      padding: const EdgeInsets.all(32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              Icons.shopping_cart_outlined,
              size: 60,
              color: Theme.of(context).primaryColor.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            '購物車是空的',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            '掃描商品條碼開始購物',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: Colors.grey.shade500),
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  // 移除這個方法，因爲我們直接在 SliverList 中處理購物車項目

  Widget _buildEnhancedBottomSection(
    BuildContext context,
    WidgetRef ref,
    List<CartItem> cartItems,
    bool isLoading,
  ) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, -5),
          ),
        ],
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: SafeArea(
        top: false,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                if (cartItems.isNotEmpty) ...[
                  Expanded(child: _buildScanButton()),
                  const SizedBox(width: 16),
                  Expanded(child: _buildCheckoutButton(context, ref, cartItems, isLoading)),
                ] else
                  Expanded(child: _buildScanButton()),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScanButton({bool isLarge = false}) {
    return Container(
      height: isLarge ? 56 : 52,
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: [Colors.blue.shade600, Colors.blue.shade500]),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: () {
          debugPrint("POSPage:_buildScanButton: 用戶點擊了掃描按鈕，準備導航到掃描頁面");
          GoRouter.of(context).push('/scan'); // 使用GoRouter.of(context).push替代context.push
          debugPrint("POSPage:_buildScanButton: 已發送導航請求到掃描頁面");
        },
        icon: Icon(Icons.qr_code_scanner, size: isLarge ? 28 : 24, color: Colors.white),
        label: Text(
          '掃描商品',
          style: TextStyle(
            fontSize: isLarge ? 18 : 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        ),
      ),
    );
  }

  Widget _buildCheckoutButton(
    BuildContext context,
    WidgetRef ref,
    List<CartItem> cartItems,
    bool isLoading,
  ) {
    return Container(
      height: 52,
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: [Colors.green.shade600, Colors.green.shade500]),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed:
            cartItems.isEmpty || isLoading
                ? null
                : () => _showCheckoutDialog(context, ref, cartItems),
        icon: const Icon(Icons.payment, color: Colors.white),
        label: const Text(
          '結帳',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.white),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          disabledBackgroundColor: Colors.grey.shade400,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        ),
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.5),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).primaryColor),
              ),
              const SizedBox(height: 20),
              Text(
                '處理結帳中...',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              Text(
                '請稍候',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showClearCartDialog() {
    showDialog<void>(
      context: context,
      builder:
          (ctx) => AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            title: Row(
              children: [
                Icon(Icons.warning_amber_rounded, color: Colors.orange.shade600),
                const SizedBox(width: 12),
                const Text('清空購物車'),
              ],
            ),
            content: const Text('確定要清空購物車中的所有商品嗎？此操作無法撤銷。'),
            actions: [
              TextButton(child: const Text('取消'), onPressed: () => Navigator.of(ctx).pop()),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade600,
                  foregroundColor: Colors.white,
                ),
                child: const Text('清空'),
                onPressed: () {
                  ref.read(cartProvider.notifier).clearCart();
                  Navigator.of(ctx).pop();
                },
              ),
            ],
          ),
    );
  }

  void _showCheckoutDialog(BuildContext context, WidgetRef ref, List<CartItem> cartItems) {
    String paymentType = 'cash';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
              title: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(Icons.payment, color: Theme.of(context).primaryColor),
                  ),
                  const SizedBox(width: 12),
                  const Text('確認結帳'),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('商品數量:'),
                            Text(
                              '${cartItems.length}',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('總金額:'),
                            Text(
                              '\$${_calculateTotalAmount(cartItems).toStringAsFixed(2)}',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  const Text('選擇付款方式:', style: TextStyle(fontWeight: FontWeight.w600)),
                  const SizedBox(height: 12),
                  _buildPaymentOptions(paymentType, setState),
                ],
              ),
              actions: <Widget>[
                TextButton(child: const Text('取消'), onPressed: () => Navigator.of(context).pop()),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  ),
                  child: const Text('確認結帳'),
                  onPressed: () async {
                    Navigator.of(context).pop();
                    await ref.read(checkoutProvider.notifier).checkout(cartItems, paymentType);
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildPaymentOptions(String paymentType, StateSetter setState) {
    final options = [
      {'value': 'cash', 'label': '現金', 'icon': Icons.payments},
    ];

    return Column(
      children:
          options.map((option) {
            final isSelected = paymentType == option['value'];
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: InkWell(
                onTap: () => setState(() => paymentType = option['value'] as String),
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade300,
                      width: isSelected ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : null,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        option['icon'] as IconData,
                        color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade600,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        option['label'] as String,
                        style: TextStyle(
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade700,
                        ),
                      ),
                      const Spacer(),
                      if (isSelected)
                        Icon(Icons.check_circle, color: Theme.of(context).primaryColor),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
    );
  }

  // 重新設計顯示對話框的方法，不再接收參數並使用當前BuildContext
  void _showCheckoutSuccessDialog() {
    debugPrint("POSPage:_showCheckoutSuccessDialog: 開始顯示對話框");

    showSuccessSnackBar(context, '結帳完成！');

    // 使用rootNavigator確保對話框顯示在最頂層，避免被其他Navigator影響
    showDialog(
          context: context,
          barrierDismissible: false,
          useRootNavigator: true, // 確保使用根Navigator
          builder: (BuildContext dialogContext) {
            debugPrint("POSPage:_showCheckoutSuccessDialog: 對話框builder被調用");
            return WillPopScope(
              // 防止用戶通過返回鍵關閉對話框
              onWillPop: () async => false,
              child: AlertDialog(
                title: const Text('結帳成功'),
                content: const Text('您想要查看並列印收據嗎？'),
                actions: [
                  TextButton(
                    child: const Text('稍後'),
                    onPressed: () {
                      debugPrint("POSPage:_showCheckoutSuccessDialog: 用戶點擊了'稍後'按鈕");
                      // 只關閉對話框，不進行其他導航
                      Navigator.of(dialogContext).pop();
                    },
                  ),
                  FilledButton(
                    child: const Text('查看收據'),
                    onPressed: () {
                      debugPrint("POSPage:_showCheckoutSuccessDialog: 用戶點擊了'查看收據'按鈕");
                      // 先關閉對話框
                      Navigator.of(dialogContext).pop();

                      // 使用Future.microtask確保對話框完全關閉後再進行導航
                      Future.microtask(() {
                        if (mounted) {
                          // 導航到收據頁面時，使用Navigator.pushNamed而不是context.push
                          // 這可以避免GoRouter的一些潛在問題
                          debugPrint("POSPage:_showCheckoutSuccessDialog: 準備導航到收據頁面");
                          // 使用navigate方法替代context.push
                          GoRouter.of(context).push('/admin/receipts');
                          debugPrint("POSPage:_showCheckoutSuccessDialog: 已發送導航請求到收據頁面");
                        }
                      });
                    },
                  ),
                ],
              ),
            );
          },
        )
        .then((_) {
          debugPrint("POSPage:_showCheckoutSuccessDialog: 對話框已關閉");
        })
        .catchError((error) {
          debugPrint("POSPage:_showCheckoutSuccessDialog: 顯示對話框時發生錯誤: $error");
        });
  }

  double _calculateTotalAmount(List<CartItem> cartItems) {
    return cartItems.fold(0.0, (sum, item) => sum + (item.product.price * item.quantity));
  }
}
