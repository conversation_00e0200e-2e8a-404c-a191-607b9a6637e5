import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/widgets/store_selector.dart';
import 'package:intl/intl.dart';
import 'package:grid_pos/core/constants/firestore_constants.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/auth/presentation/providers/auth_provider.dart';
import 'package:grid_pos/features/pos/data/datasources/sale_remote_ds.dart';
import 'package:grid_pos/features/pos/domain/entities/sale_entity.dart';
import 'package:grid_pos/features/pos/presentation/widgets/receipt_detail_dialog.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/tenant_providers.dart';

/// 根據用戶角色獲取銷售列表的Provider
final saleListProvider = StreamProvider.autoDispose<List<SaleEntity>>((ref) {
  final storeId = ref.watch(selectedStoreIdProvider);
  final currentUser = ref.watch(currentUserProvider);

  if (storeId == null || storeId.isEmpty || storeId == kNoValidStoreSelectedId) {
    return Stream.value([]);
  }

  final saleRemoteDataSource = ref.watch(saleRemoteDataSourceProvider);

  // 根據用戶角色返回不同的銷售列表
  if (currentUser == null) {
    return Stream.value([]);
  } else if (currentUser.role == 'admin') {
    // 管理員可以查看店鋪的所有銷售
    return saleRemoteDataSource.watchSalesByStore(storeId);
  } else if (currentUser.role == 'tenant' && currentUser.tenantId != null) {
    // 租戶只能查看自己的銷售
    return saleRemoteDataSource.watchSalesByTenant(storeId, currentUser.tenantId!);
  } else if (currentUser.role == 'cashier') {
    // 收銀員可以查看自己處理的銷售
    return saleRemoteDataSource.watchSalesByStore(storeId).map((sales) {
      return sales.where((sale) => sale.cashierId == currentUser.uid).toList();
    });
  }

  return Stream.value([]);
});

/// 收據列表頁面
class ReceiptListPage extends ConsumerStatefulWidget {
  /// 創建收據列表頁面
  const ReceiptListPage({super.key});

  @override
  ConsumerState<ReceiptListPage> createState() => _ReceiptListPageState();
}

class _ReceiptListPageState extends ConsumerState<ReceiptListPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _statusFilter = 'all';
  DateTime? _startDate;
  DateTime? _endDate;
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final salesAsync = ref.watch(saleListProvider);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: _buildAppBar(theme),
      body: salesAsync.when(
        data: (sales) => _buildScrollableContent(sales, theme),
        loading:
            () => const Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [CircularProgressIndicator(), SizedBox(height: 16), Text('載入收據資料中...')],
              ),
            ),
        error: (error, stackTrace) {
          Logger.error('Error loading sales: $error', error, stackTrace);
          return _buildErrorState(error, theme);
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    if (_isSearching) {
      return AppBar(
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        automaticallyImplyLeading: false,
        actions: [IconButton(icon: const Icon(Icons.close), onPressed: _exitSearchMode)],
        title: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
          ),
          child: TextField(
            controller: _searchController,
            autofocus: true,
            decoration: InputDecoration(
              hintText: '搜尋收據ID、租戶或商品...',
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(vertical: 12),
              hintStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant, fontSize: 16),
              prefixIcon: Icon(Icons.search, color: theme.colorScheme.onSurfaceVariant, size: 20),
              suffixIcon:
                  _searchQuery.isNotEmpty
                      ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: IconButton(
                          icon: Icon(Icons.clear, color: theme.colorScheme.onSurfaceVariant),
                          onPressed: () {
                            _searchController.clear();
                            setState(() => _searchQuery = '');
                          },
                        ),
                      )
                      : null,
            ),
            style: TextStyle(color: theme.colorScheme.onSurfaceVariant, fontSize: 16),
            onChanged: (value) => setState(() => _searchQuery = value.toLowerCase()),
          ),
        ),
      );
    }

    return AppBar(
      elevation: 0,
      backgroundColor: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      title: Text(
        '收據列表',
        style: theme.textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.onSurface,
        ),
      ),
      actions: [
        // StoreSelector(),
        IconButton(
          icon: Stack(
            children: [
              const Icon(Icons.date_range),
              if (_startDate != null || _endDate != null)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                  ),
                ),
            ],
          ),
          onPressed: _showDateRangePicker,
          tooltip: _getDateRangeTooltip(),
        ),
        IconButton(
          icon: Stack(
            children: [
              const Icon(Icons.search),
              if (_searchQuery.isNotEmpty || _statusFilter != 'all')
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                  ),
                ),
            ],
          ),
          onPressed: _enterSearchMode,
        ),
      ],
    );
  }

  void _enterSearchMode() {
    setState(() => _isSearching = true);
  }

  void _exitSearchMode() {
    setState(() {
      _isSearching = false;
      _searchController.clear();
      _searchQuery = '';
    });
  }

  Widget _buildScrollableContent(List<SaleEntity> sales, ThemeData theme) {
    final filteredSales = _filterSales(sales);

    // 如果沒有收據，顯示空狀態
    if (filteredSales.isEmpty) {
      return RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(saleListProvider);
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              Padding(padding: const EdgeInsets.all(16), child: _buildSearchAndStats(sales, theme)),
              SizedBox(height: MediaQuery.of(context).size.height * 0.2),
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _hasActiveFilters() ? Icons.search_off : Icons.receipt_long_outlined,
                      size: 64,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _hasActiveFilters() ? '沒有找到符合條件的收據' : '暫無收據記錄',
                      style: theme.textTheme.titleMedium,
                    ),
                    if (_hasActiveFilters()) ...[
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _statusFilter = 'all';
                            _startDate = null;
                            _endDate = null;
                          });
                          if (_isSearching) {
                            _exitSearchMode();
                          }
                        },
                        child: const Text('清除篩選'),
                      ),
                    ] else ...[
                      const SizedBox(height: 8),
                      Text(
                        '還沒有任何銷售記錄\n開始銷售商品後，收據會出現在這裡',
                        textAlign: TextAlign.center,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    // 有收據時，使用統一的 ListView
    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(saleListProvider);
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredSales.length + 1, // +1 for the header
        itemBuilder: (context, index) {
          // 第一個項目是搜索和統計區域
          if (index == 0) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: _buildSearchAndStats(sales, theme),
            );
          }

          // 其餘項目是收據列表
          final saleIndex = index - 1;
          final sale = filteredSales[saleIndex];

          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildReceiptCard(sale, theme),
          );
        },
      ),
    );
  }

  Widget _buildSearchAndStats(List<SaleEntity> sales, ThemeData theme) {
    final filteredSales = _filterSales(sales);
    final stats = _calculateSaleStats(sales);

    return Column(
      children: [
        // Search result indicator when searching
        if (_isSearching && _searchQuery.isNotEmpty) ...[
          Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color:
                  filteredSales.isEmpty
                      ? Colors.orange.withOpacity(0.1)
                      : Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color:
                    filteredSales.isEmpty
                        ? Colors.orange.withOpacity(0.3)
                        : Colors.green.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  filteredSales.isEmpty ? Icons.search_off : Icons.check_circle_outline,
                  color: filteredSales.isEmpty ? Colors.orange : Colors.green,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    filteredSales.isEmpty
                        ? '沒有找到符合 "$_searchQuery" 的收據'
                        : '找到 ${filteredSales.length} 個符合 "$_searchQuery" 的收據',
                    style: TextStyle(
                      color: filteredSales.isEmpty ? Colors.orange.shade700 : Colors.green.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (filteredSales.isNotEmpty) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${filteredSales.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],

        // Stats Card with integrated filters
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    const Icon(Icons.receipt_long, size: 24),
                    const SizedBox(width: 8),
                    Text('收據總數: ${sales.length}', style: theme.textTheme.titleMedium),
                    if (filteredSales.length != sales.length) ...[
                      const Spacer(),
                      Text('顯示: ${filteredSales.length}'),
                    ],
                  ],
                ),
                if (sales.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  // Status stats row
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatFilterItem(
                          '全部',
                          sales.length,
                          Colors.grey,
                          'all',
                          _statusFilter == 'all',
                          () {
                            if (_statusFilter != 'all') {
                              setState(() => _statusFilter = 'all');
                            }
                          },
                          theme,
                        ),
                      ),
                      Expanded(
                        child: _buildStatFilterItem(
                          '已完成',
                          stats['completed'] ?? 0,
                          Colors.green,
                          'completed',
                          _statusFilter == 'completed',
                          () {
                            setState(
                              () =>
                                  _statusFilter =
                                      _statusFilter == 'completed' ? 'all' : 'completed',
                            );
                          },
                          theme,
                        ),
                      ),
                      Expanded(
                        child: _buildStatFilterItem(
                          '已取消',
                          stats['cancelled'] ?? 0,
                          Colors.red,
                          'cancelled',
                          _statusFilter == 'cancelled',
                          () {
                            setState(
                              () =>
                                  _statusFilter =
                                      _statusFilter == 'cancelled' ? 'all' : 'cancelled',
                            );
                          },
                          theme,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Total amount summary
                  _buildAmountSummary(filteredSales, theme),
                  // Active filter indicator
                  if (_statusFilter != 'all' || _startDate != null || _endDate != null) ...[
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      alignment: WrapAlignment.center,
                      children: [
                        if (_statusFilter != 'all')
                          Chip(
                            label: Text('狀態: ${_getStatusFilterDisplayName(_statusFilter)}'),
                            onDeleted: () => setState(() => _statusFilter = 'all'),
                            deleteIcon: const Icon(Icons.close, size: 18),
                          ),
                        if (_startDate != null || _endDate != null)
                          Chip(
                            label: Text('日期: ${_getDateRangeText()}'),
                            onDeleted:
                                () => setState(() {
                                  _startDate = null;
                                  _endDate = null;
                                }),
                            deleteIcon: const Icon(Icons.close, size: 18),
                          ),
                      ],
                    ),
                  ],
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatFilterItem(
    String label,
    int count,
    Color color,
    String filterValue,
    bool isSelected,
    VoidCallback onTap,
    ThemeData theme,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: isSelected ? Border.all(color: color, width: 2) : null,
            color: isSelected ? color.withOpacity(0.1) : null,
          ),
          child: Column(
            children: [
              Text(
                '$count',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isSelected ? color : color,
                ),
              ),
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isSelected ? color : null,
                  fontWeight: isSelected ? FontWeight.w600 : null,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAmountSummary(List<SaleEntity> filteredSales, ThemeData theme) {
    final totalAmount = filteredSales.fold<double>(0, (sum, sale) => sum + sale.totalAmount);
    final formatter = NumberFormat('#,###.##');

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.primary.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.attach_money, color: theme.colorScheme.primary, size: 24),
          const SizedBox(width: 8),
          Column(
            children: [
              Text(
                '總金額',
                style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.primary),
              ),
              Text(
                formatter.format(totalAmount),
                style: theme.textTheme.titleLarge?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildReceiptCard(SaleEntity sale, ThemeData theme) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
    final formattedDate = sale.createdAt != null ? dateFormat.format(sale.createdAt!) : '未知時間';
    final numberFormatter = NumberFormat('#,###.##');

    return Card(
      elevation: 0,
      color: theme.colorScheme.surfaceContainerLow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: theme.colorScheme.outline.withOpacity(0.1)),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () => _showReceiptDetails(sale),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 頂部：收據號和狀態
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.receipt_long,
                      size: 20,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '收據 #${sale.id?.substring(0, 8) ?? "未知"}',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          formattedDate,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusBadge(sale.status, theme),
                ],
              ),

              const SizedBox(height: 16),

              // 中間：收據詳情
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    _buildInfoRow(Icons.store_outlined, '租戶', sale.tenantId, theme),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: _buildInfoRow(
                            Icons.shopping_bag_outlined,
                            '項目',
                            '${sale.items.length} 種',
                            theme,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildInfoRow(Icons.payment, '支付', sale.paymentType, theme),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // 底部：金額和操作
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '總金額',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '\$${numberFormatter.format(sale.totalAmount)}',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      _buildPrintedBadge(sale.printed, theme),
                      const SizedBox(height: 8),
                      FilledButton.icon(
                        onPressed: () => _showReceiptDetails(sale),
                        icon: const Icon(Icons.visibility, size: 18),
                        label: const Text('查看詳情'),
                        style: FilledButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status, ThemeData theme) {
    final isCompleted = status == 'completed';
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: isCompleted ? theme.colorScheme.primaryContainer : theme.colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isCompleted ? Icons.check_circle : Icons.cancel,
            size: 16,
            color:
                isCompleted
                    ? theme.colorScheme.onPrimaryContainer
                    : theme.colorScheme.onErrorContainer,
          ),
          const SizedBox(width: 4),
          Text(
            isCompleted ? '已完成' : '已取消',
            style: theme.textTheme.bodySmall?.copyWith(
              color:
                  isCompleted
                      ? theme.colorScheme.onPrimaryContainer
                      : theme.colorScheme.onErrorContainer,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrintedBadge(bool printed, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color:
            printed
                ? theme.colorScheme.primaryContainer.withOpacity(0.5)
                : theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            printed ? Icons.print : Icons.print_disabled,
            size: 14,
            color: printed ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 4),
          Text(
            printed ? '已列印' : '未列印',
            style: theme.textTheme.bodySmall?.copyWith(
              color: printed ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value, ThemeData theme) {
    return Row(
      children: [
        Icon(icon, size: 18, color: theme.colorScheme.onSurfaceVariant),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value.length > 8 ? value.substring(0, 8) : value,
                style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurface),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(Object? error, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text('載入收據失敗: ${error.toString()}'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              ref.invalidate(saleListProvider);
            },
            child: const Text('重試'),
          ),
        ],
      ),
    );
  }

  Future<void> _showDateRangePicker() async {
    final initialDateRange =
        _startDate != null && _endDate != null
            ? DateTimeRange(start: _startDate!, end: _endDate!)
            : null;

    final pickedDateRange = await showDateRangePicker(
      context: context,
      initialDateRange: initialDateRange,
      firstDate: DateTime(2021),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: Theme.of(context).colorScheme.primary),
          ),
          child: child!,
        );
      },
    );

    if (pickedDateRange != null) {
      setState(() {
        _startDate = pickedDateRange.start;
        _endDate = pickedDateRange.end;
      });
    }
  }

  String _getStatusFilterDisplayName(String filter) {
    switch (filter) {
      case 'completed':
        return '已完成';
      case 'cancelled':
        return '已取消';
      default:
        return '全部';
    }
  }

  String _getDateRangeTooltip() {
    if (_startDate != null && _endDate != null) {
      final formatter = DateFormat('yyyy/MM/dd');
      return '${formatter.format(_startDate!)} - ${formatter.format(_endDate!)}';
    } else if (_startDate != null) {
      final formatter = DateFormat('yyyy/MM/dd');
      return '從 ${formatter.format(_startDate!)} 開始';
    } else if (_endDate != null) {
      final formatter = DateFormat('yyyy/MM/dd');
      return '截至 ${formatter.format(_endDate!)}';
    }
    return '選擇日期範圍';
  }

  String _getDateRangeText() {
    if (_startDate != null && _endDate != null) {
      final formatter = DateFormat('MM/dd');
      return '${formatter.format(_startDate!)} - ${formatter.format(_endDate!)}';
    } else if (_startDate != null) {
      final formatter = DateFormat('MM/dd');
      return '從 ${formatter.format(_startDate!)}';
    } else if (_endDate != null) {
      final formatter = DateFormat('MM/dd');
      return '至 ${formatter.format(_endDate!)}';
    }
    return '';
  }

  Map<String, int> _calculateSaleStats(List<SaleEntity> sales) {
    final stats = <String, int>{};

    int completed = 0;
    int cancelled = 0;

    for (final sale in sales) {
      if (sale.status == 'completed') {
        completed++;
      } else {
        cancelled++;
      }
    }

    stats['completed'] = completed;
    stats['cancelled'] = cancelled;

    return stats;
  }

  List<SaleEntity> _filterSales(List<SaleEntity> sales) {
    return sales.where((sale) {
      // 搜尋過濾
      if (_searchQuery.isNotEmpty) {
        final searchMatch =
            (sale.id?.toLowerCase().contains(_searchQuery) ?? false) ||
            sale.tenantId.toLowerCase().contains(_searchQuery) ||
            sale.totalAmount.toString().contains(_searchQuery) ||
            sale.items.any(
              (item) =>
                  item.name.toLowerCase().contains(_searchQuery) ||
                  item.sku.toLowerCase().contains(_searchQuery),
            );
        if (!searchMatch) return false;
      }

      // 狀態過濾
      if (_statusFilter != 'all') {
        if (sale.status != _statusFilter) return false;
      }

      // 日期範圍過濾
      if (_startDate != null) {
        if (sale.createdAt == null || sale.createdAt!.isBefore(_startDate!)) {
          return false;
        }
      }

      if (_endDate != null) {
        final endDateWithTime = DateTime(
          _endDate!.year,
          _endDate!.month,
          _endDate!.day,
          23,
          59,
          59,
        );
        if (sale.createdAt == null || sale.createdAt!.isAfter(endDateWithTime)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  bool _hasActiveFilters() {
    return _searchQuery.isNotEmpty ||
        _statusFilter != 'all' ||
        _startDate != null ||
        _endDate != null;
  }

  void _showReceiptDetails(SaleEntity sale) {
    showDialog(
      context: context,
      builder: (context) {
        return ReceiptDetailDialog(sale: sale);
      },
    );
  }
}
