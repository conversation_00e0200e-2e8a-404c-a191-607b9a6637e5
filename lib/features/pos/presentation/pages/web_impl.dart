// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html;
import 'dart:typed_data';

/// 在 Web 平台上創建 Blob URL
String createBlobUrl(Uint8List data, String mimeType) {
  final blob = html.Blob([data], mimeType);
  return html.Url.createObjectUrlFromBlob(blob);
}

/// 釋放 Blob URL
void revokeBlobUrl(String url) {
  html.Url.revokeObjectUrl(url);
}

/// 在新標籤頁中打開 URL
void openUrlInNewTab(String url) {
  html.window.open(url, '_blank');
}

/// 使用瀏覽器的打印功能
void printPage() {
  html.window.print();
}
