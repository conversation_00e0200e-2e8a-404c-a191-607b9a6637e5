import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:grid_pos/shared/widgets/custom_snackbar.dart';

// 使用條件導入，為不同平台使用不同的實現
import 'web_stub.dart' if (dart.library.html) 'web_impl.dart' as web;

/// A page for previewing and printing PDF files
class PdfPreviewPage extends StatefulWidget {
  /// The PDF data as Uint8List
  final Uint8List pdfData;

  /// An optional title for the app bar
  final String title;

  /// Optional file name for printing
  final String? filename;

  /// 列印成功後的回調函數
  final VoidCallback? onPrintSuccess;

  /// Creates a new PdfPreviewPage
  const PdfPreviewPage({
    super.key,
    required this.pdfData,
    this.title = 'PDF預覽',
    this.filename,
    this.onPrintSuccess,
  });

  @override
  State<PdfPreviewPage> createState() => _PdfPreviewPageState();
}

class _PdfPreviewPageState extends State<PdfPreviewPage> {
  String? _webViewUrl;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      _setupWebPlatform();
    } else {
      // 移動平台無需特殊初始化
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _setupWebPlatform() {
    if (kIsWeb) {
      // 在 Web 平台上，創建 Blob URL
      final url = web.createBlobUrl(widget.pdfData, 'application/pdf');
      setState(() {
        _webViewUrl = url;
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    if (kIsWeb && _webViewUrl != null) {
      // 釋放 Blob URL
      web.revokeBlobUrl(_webViewUrl!);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            tooltip: '列印',
            onPressed: () async {
              bool result = false;

              if (kIsWeb) {
                // 在 Web 平台使用瀏覽器的列印功能
                if (_webViewUrl != null) {
                  web.openUrlInNewTab(_webViewUrl!);
                  web.printPage();
                  result = true;
                }
              } else {
                // 在移動平台使用 printing 套件
                result = await Printing.layoutPdf(
                  onLayout: (_) => widget.pdfData,
                  name: widget.filename ?? 'document.pdf',
                );
              }

              // 如果列印成功且回調不為空，則呼叫回調
              if (result && widget.onPrintSuccess != null) {
                widget.onPrintSuccess!();
                if (context.mounted) {
                  showSuccessSnackBar(context, '收據已成功列印並標記為已列印');
                }
              }
            },
          ),
        ],
      ),
      body: _buildPlatformSpecificPreview(),
    );
  }

  Widget _buildPlatformSpecificPreview() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (kIsWeb) {
      // Web 平台：提供按鈕來在新標籤頁中打開 PDF
      if (_webViewUrl != null) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.picture_as_pdf, size: 72, color: Colors.blue),
              const SizedBox(height: 16),
              const Text('PDF 檔案已準備好'),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                icon: const Icon(Icons.open_in_new),
                label: const Text('在新標籤頁中查看'),
                onPressed: () {
                  web.openUrlInNewTab(_webViewUrl!);
                },
              ),
            ],
          ),
        );
      } else {
        return const Center(child: Text('無法載入 PDF'));
      }
    } else {
      // 移動平台：使用 PdfPreview
      return PdfPreview(
        build: (format) => widget.pdfData,
        initialPageFormat: PdfPageFormat.a4,
        useActions: false,
        allowPrinting: true,
        allowSharing: true,
        canChangeOrientation: false,
        canChangePageFormat: false,
      );
    }
  }
}
