import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/pos/domain/entities/cart_item.dart';
import 'package:grid_pos/features/product_mgmt/domain/entities/product_entity.dart';

/// Provider for the shopping cart
final cartProvider = StateNotifierProvider<CartNotifier, List<CartItem>>((ref) {
  return CartNotifier();
});

/// Notifier that manages the shopping cart state
class CartNotifier extends StateNotifier<List<CartItem>> {
  /// Creates a cart notifier with an empty cart
  CartNotifier() : super([]);

  /// Adds a product to the cart
  /// If the product is already in the cart, increases its quantity
  void addItem(ProductEntity product) {
    final existingIndex = state.indexWhere(
      (item) => item.product.id == product.id,
    );

    if (existingIndex >= 0) {
      // Product already in cart, increase quantity
      final existingItem = state[existingIndex];
      final updatedItem = existingItem.copyWith(
        quantity: existingItem.quantity + 1,
      );

      state = [
        ...state.sublist(0, existingIndex),
        updatedItem,
        ...state.sublist(existingIndex + 1),
      ];
    } else {
      // Product not in cart, add it with quantity 1
      state = [...state, CartItem(product: product, quantity: 1)];
    }
  }

  /// Increments the quantity of a product in the cart
  void incrementItem(String productId) {
    final existingIndex = state.indexWhere(
      (item) => item.product.id == productId,
    );

    if (existingIndex >= 0) {
      final existingItem = state[existingIndex];
      final updatedItem = existingItem.copyWith(
        quantity: existingItem.quantity + 1,
      );

      state = [
        ...state.sublist(0, existingIndex),
        updatedItem,
        ...state.sublist(existingIndex + 1),
      ];
    }
  }

  /// Decrements the quantity of a product in the cart
  /// If the quantity becomes 0, removes the item from the cart
  void decrementItem(String productId) {
    final existingIndex = state.indexWhere(
      (item) => item.product.id == productId,
    );

    if (existingIndex >= 0) {
      final existingItem = state[existingIndex];

      if (existingItem.quantity > 1) {
        // Decrease quantity
        final updatedItem = existingItem.copyWith(
          quantity: existingItem.quantity - 1,
        );

        state = [
          ...state.sublist(0, existingIndex),
          updatedItem,
          ...state.sublist(existingIndex + 1),
        ];
      } else {
        // Remove item if quantity becomes 0
        removeItem(productId);
      }
    }
  }

  /// Removes a product from the cart
  void removeItem(String productId) {
    state = state.where((item) => item.product.id != productId).toList();
  }

  /// Clears the entire cart
  void clearCart() {
    state = [];
  }

  /// Gets the total amount of the cart
  double get totalAmount =>
      state.fold(0, (sum, item) => sum + (item.product.price * item.quantity));

  /// Gets the total number of items in the cart
  int get totalItemsCount => state.fold(0, (sum, item) => sum + item.quantity);
}
