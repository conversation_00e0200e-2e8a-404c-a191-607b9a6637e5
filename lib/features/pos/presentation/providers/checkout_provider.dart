import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/auth/presentation/providers/auth_provider.dart';
import 'package:grid_pos/features/pos/domain/entities/cart_item.dart';
import 'package:grid_pos/features/pos/domain/entities/sale_entity.dart';
import 'package:grid_pos/features/pos/domain/services/checkout_service.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart';
import 'package:uuid/uuid.dart';

/// State for the checkout process
class CheckoutState {
  /// Whether the checkout is in progress
  final bool isLoading;

  /// The completed sale, if checkout was successful
  final SaleEntity? completedSale;

  /// Error message if checkout failed
  final String? errorMessage;

  /// Creates a checkout state
  CheckoutState({this.isLoading = false, this.completedSale, this.errorMessage});

  /// Creates a copy of this state with the given fields replaced
  CheckoutState copyWith({bool? isLoading, SaleEntity? completedSale, String? errorMessage}) {
    return CheckoutState(
      isLoading: isLoading ?? this.isLoading,
      completedSale: completedSale ?? this.completedSale,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// Notifier for the checkout process
class CheckoutNotifier extends StateNotifier<CheckoutState> {
  final CheckoutService _checkoutService;
  final String _storeId;
  final String _userId;
  final String _deviceId;

  /// Creates a checkout notifier
  CheckoutNotifier(this._checkoutService, this._storeId, this._userId)
    : _deviceId = const Uuid().v4(), // Generate a unique device ID for this session
      super(CheckoutState());

  /// Process the checkout for the given cart items and payment type
  Future<void> checkout(List<CartItem> items, String paymentType) async {
    Logger.debug("CheckoutNotifier.checkout: 開始結帳流程，購物車商品數量: ${items.length}");

    if (items.isEmpty) {
      state = state.copyWith(errorMessage: 'Cannot checkout with an empty cart');
      Logger.debug("CheckoutNotifier.checkout: 購物車為空，結帳失敗");
      return;
    }

    // 重置狀態以確保每次結帳都是從乾淨狀態開始
    state = CheckoutState(isLoading: true);
    Logger.debug("CheckoutNotifier.checkout: 已重置狀態並設置isLoading=true");

    try {
      final sale = await _checkoutService.performCheckout(
        _storeId,
        items,
        paymentType,
        _userId,
        _deviceId,
      );

      if (sale == null) {
        Logger.debug("CheckoutNotifier.checkout: 結帳服務返回null，結帳失敗");
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'Checkout failed for unknown reason',
        );
        return;
      }

      Logger.debug("CheckoutNotifier.checkout: 結帳成功，設置completedSale，SaleID: ${sale.id}");
      state = CheckoutState(isLoading: false, completedSale: sale);
    } catch (e) {
      Logger.error('Checkout error', e);
      Logger.debug("CheckoutNotifier.checkout: 結帳發生異常: $e");
      state = state.copyWith(isLoading: false, errorMessage: 'Checkout failed: ${e.toString()}');
    }
  }

  /// Clear the checkout state
  void clearState() {
    Logger.debug("CheckoutNotifier.clearState: 清除結帳狀態");
    state = CheckoutState();
  }
}

/// Provider for the checkout state
final checkoutProvider = StateNotifierProvider<CheckoutNotifier, CheckoutState>((ref) {
  final checkoutService = ref.watch(checkoutServiceProvider);
  final currentUser = ref.watch(currentUserProvider);
  final selectedStoreId = ref.watch(selectedStoreIdProvider);

  if (currentUser == null) {
    throw Exception('User must be logged in and a store selected to checkout');
  }

  return CheckoutNotifier(checkoutService, selectedStoreId, currentUser.uid);
});
