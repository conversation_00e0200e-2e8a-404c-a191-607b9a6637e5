import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:grid_pos/features/pos/domain/entities/sale_entity.dart';
import 'package:grid_pos/features/pos/domain/entities/sale_item_embed.dart';
import 'package:grid_pos/features/pos/domain/services/receipt_pdf_service.dart';
import 'package:grid_pos/features/pos/domain/services/printing_service.dart';

/// 收據詳情對話框
class ReceiptDetailDialog extends ConsumerWidget {
  /// 銷售數據
  final SaleEntity sale;

  /// 創建收據詳情對話框
  const ReceiptDetailDialog({super.key, required this.sale});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formatter = DateFormat('yyyy-MM-dd HH:mm:ss');
    final numberFormatter = NumberFormat('#,###.##');

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width > 600 ? 500 : double.infinity,
        constraints: const BoxConstraints(maxWidth: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(context, formatter),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [_buildItemsList(numberFormatter), _buildSummary(numberFormatter)],
                ),
              ),
            ),
            _buildFooter(context, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, DateFormat formatter) {
    // 定義只用於日期的格式器
    final dateFormatter = DateFormat('yyyy-MM-dd');
    // 定義只用於時間的格式器
    final timeFormatter = DateFormat('HH:mm:ss');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  shape: BoxShape.circle,
                ),
                child: Icon(Icons.receipt_long, color: Theme.of(context).colorScheme.onPrimary),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '收據 #${sale.id?.substring(0, 8) ?? '未知ID'}',
                      style: Theme.of(
                        context,
                      ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '日期: ${sale.createdAt != null ? dateFormatter.format(sale.createdAt!) : '未知日期'}',
                      style: Theme.of(context).textTheme.bodyMedium,
                      overflow: TextOverflow.ellipsis,
                    ),
                    // 時間顯示
                    Text(
                      '時間: ${sale.createdAt != null ? timeFormatter.format(sale.createdAt!) : '未知時間'}',
                      style: Theme.of(context).textTheme.bodyMedium,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              _buildStatusBadge(context),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color:
            sale.status == 'completed'
                ? Colors.green.withOpacity(0.2)
                : Colors.red.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        sale.status == 'completed' ? '已完成' : '已取消',
        style: TextStyle(
          color: sale.status == 'completed' ? Colors.green[800] : Colors.red[800],
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildItemsList(NumberFormat numberFormatter) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('購買項目', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
          const SizedBox(height: 12),
          ...sale.items.map((item) => _buildItemRow(item, numberFormatter)).toList(),
        ],
      ),
    );
  }

  Widget _buildItemRow(SaleItemEmbed item, NumberFormat numberFormatter) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(item.name, style: const TextStyle(fontWeight: FontWeight.w500)),
                Text('SKU: ${item.sku}', style: TextStyle(fontSize: 12, color: Colors.grey[600])),
              ],
            ),
          ),
          const SizedBox(width: 8),
          Text('x${item.qty}'),
          const SizedBox(width: 16),
          Text('\$${numberFormatter.format(item.price)}'),
        ],
      ),
    );
  }

  Widget _buildSummary(NumberFormat numberFormatter) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(color: Colors.grey[100], borderRadius: BorderRadius.circular(8)),
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildInfoRow('租戶ID', sale.tenantId),
          _buildInfoRow('支付方式', sale.paymentType),
          _buildInfoRow('打印狀態', sale.printed ? '已打印' : '未打印'),
          const Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('總金額', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
              Text(
                '\$${numberFormatter.format(sale.totalAmount)}',
                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFooter(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton.icon(
            icon: const Icon(Icons.print),
            label: const Text('打印收據'),
            onPressed: () {
              _printReceipt(context, ref);
            },
          ),
          const SizedBox(width: 8),
          TextButton(child: const Text('關閉'), onPressed: () => Navigator.of(context).pop()),
        ],
      ),
    );
  }

  Future<void> _printReceipt(BuildContext context, WidgetRef ref) async {
    try {
      final receiptPdfService = ref.read(receiptPdfServiceProvider);
      final printingService = ref.read(printingServiceProvider);

      // 顯示加載對話框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (ctx) => const AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [CircularProgressIndicator(), SizedBox(height: 16), Text('正在生成收據PDF...')],
              ),
            ),
      );

      // 生成PDF數據
      final pdfData = await receiptPdfService.generateReceiptPdf(sale);

      // 關閉加載對話框
      if (context.mounted) Navigator.pop(context);

      // 檢查打印是否支持
      if (!printingService.isSupported()) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('此設備不支持打印功能')));
        }
        return;
      }

      // 打印PDF
      final success = await printingService.printPdf(
        pdfData,
        name:
            'Receipt_${sale.id?.substring(0, 8) ?? 'unknown'}_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (context.mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('收據已成功發送到打印機')));
          Navigator.pop(context); // 關閉收據詳情對話框
        } else {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('打印失敗，請重試')));
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('打印錯誤: ${e.toString()}')));
      }
    }
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value.length > 8 ? value.substring(0, 8) : value,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }
}
