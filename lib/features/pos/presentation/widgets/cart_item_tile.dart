import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:grid_pos/features/pos/domain/entities/cart_item.dart';

/// Enhanced cart item tile with modern UI/UX
class CartItemTile extends StatefulWidget {
  /// The cart item to display
  final CartItem cartItem;

  /// Callback for incrementing the quantity
  final VoidCallback onIncrement;

  /// Callback for decrementing the quantity
  final VoidCallback onDecrement;

  /// Callback for removing the item
  final VoidCallback onRemove;

  /// Creates a cart item tile
  const CartItemTile({super.key, required this.cartItem, required this.onIncrement, required this.onDecrement, required this.onRemove});

  @override
  State<CartItemTile> createState() => _CartItemTileState();
}

class _CartItemTileState extends State<CartItemTile> with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(duration: const Duration(milliseconds: 300), vsync: this);
    _scaleController = AnimationController(duration: const Duration(milliseconds: 150), vsync: this);

    _slideAnimation = Tween<Offset>(begin: const Offset(0.5, 0), end: Offset.zero).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic));

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(CurvedAnimation(parent: _scaleController, curve: Curves.easeOutBack));

    _slideController.forward();
    _scaleController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _onQuantityChanged() {
    HapticFeedback.lightImpact();
    _scaleController.reset();
    _scaleController.forward();
  }

  @override
  Widget build(BuildContext context) {
    final product = widget.cartItem.product;
    final quantity = widget.cartItem.quantity;
    final subtotal = product.price * quantity;

    return SlideTransition(
      position: _slideAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(begin: Alignment.topLeft, end: Alignment.bottomRight, colors: [Theme.of(context).colorScheme.surface, Theme.of(context).colorScheme.surface.withOpacity(0.8)]),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.08), blurRadius: 15, offset: const Offset(0, 4), spreadRadius: 1)],
            border: Border.all(color: Theme.of(context).colorScheme.outline.withOpacity(0.1)),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product info section
                Row(
                  children: [
                    // Product details
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            product.name,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600, color: Theme.of(context).colorScheme.onSurface),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5), borderRadius: BorderRadius.circular(8)),
                            child: Text(
                              'SKU號碼: ${product.sku}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).colorScheme.onSurfaceVariant, fontWeight: FontWeight.w500),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Unit price
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text('單價', style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).colorScheme.onSurfaceVariant)),
                        Text('\$${product.price.toStringAsFixed(2)}', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600, color: Theme.of(context).primaryColor)),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Quantity controls and subtotal section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Enhanced quantity controls
                    _buildEnhancedQuantityControls(),

                    // Subtotal and remove button
                    Row(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text('小計', style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).colorScheme.onSurfaceVariant)),
                            Text('\$${subtotal.toStringAsFixed(2)}', style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: Colors.green.shade600)),
                          ],
                        ),
                        const SizedBox(width: 16),
                        _buildRemoveButton(),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedQuantityControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: [Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.6), Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.1)]),
        borderRadius: BorderRadius.circular(12),
        // border: Border.all(color: Theme.of(context).colorScheme.outline.withOpacity(0.2)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildQuantityButton(
            icon: Icons.remove,
            onPressed: () {
              _onQuantityChanged();
              widget.onDecrement();
            },
          ),
          Container(
            constraints: const BoxConstraints(minWidth: 50),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Text(
              widget.cartItem.quantity.toString(),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: Theme.of(context).colorScheme.onSurface),
              textAlign: TextAlign.center,
            ),
          ),
          _buildQuantityButton(
            icon: Icons.add,
            onPressed: () {
              _onQuantityChanged();
              widget.onIncrement();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuantityButton({required IconData icon, required VoidCallback onPressed}) {
    return Container(
      width: 44,
      height: 44,
      decoration: BoxDecoration(color: Theme.of(context).primaryColor.withOpacity(0.1), borderRadius: BorderRadius.circular(12)),
      child: Material(color: Colors.transparent, child: InkWell(borderRadius: BorderRadius.circular(12), onTap: onPressed, child: Icon(icon, color: Theme.of(context).primaryColor, size: 20))),
    );
  }

  Widget _buildRemoveButton() {
    return Container(
      width: 44,
      height: 44,
      decoration: BoxDecoration(color: Colors.red.shade50, borderRadius: BorderRadius.circular(12), border: Border.all(color: Colors.red.shade200, width: 1)),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            HapticFeedback.mediumImpact();
            _showRemoveConfirmation();
          },
          child: Icon(Icons.delete_outline, color: Colors.red.shade600, size: 20),
        ),
      ),
    );
  }

  void _showRemoveConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title: Row(children: [Icon(Icons.warning_amber_rounded, color: Colors.orange.shade600), const SizedBox(width: 12), const Text('移除商品')]),
          content: Text('確定要從購物車中移除「${widget.cartItem.product.name}」嗎？'),
          actions: [
            TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('取消')),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                widget.onRemove();
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red.shade600, foregroundColor: Colors.white),
              child: const Text('移除'),
            ),
          ],
        );
      },
    );
  }
}
