import 'dart:typed_data';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:printing/printing.dart';

/// Provider for PrintingService
final printingServiceProvider = Provider<PrintingService>((ref) {
  return PrintingServiceImpl();
});

/// Service for handling printing operations
abstract class PrintingService {
  /// Checks if printing is supported on the device
  bool isSupported();

  /// Lists available printers
  Future<List<Printer>> listPrinters();

  /// Prints PDF data to a specific printer
  Future<bool> printPdf(
    Uint8List pdfData, {
    Printer? printer,
    String name = 'Document',
  });
}

/// Implementation of PrintingService using the printing package
class PrintingServiceImpl implements PrintingService {
  @override
  bool isSupported() {
    return true; // The printing package handles platform support internally
  }

  @override
  Future<List<Printer>> listPrinters() async {
    try {
      return await Printing.listPrinters();
    } catch (e) {
      return [];
    }
  }

  @override
  Future<bool> printPdf(
    Uint8List pdfData, {
    Printer? printer,
    String name = 'Document',
  }) async {
    try {
      if (printer != null) {
        return await Printing.directPrintPdf(
          printer: printer,
          onLayout: (_) => pdfData,
          name: name,
        );
      } else {
        // Use the default printer selection dialog if no specific printer is provided
        return await Printing.layoutPdf(onLayout: (_) => pdfData, name: name);
      }
    } catch (e) {
      return false;
    }
  }
}
