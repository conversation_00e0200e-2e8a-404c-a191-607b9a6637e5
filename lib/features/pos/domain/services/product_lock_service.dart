import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/core/constants/firestore_constants.dart';
import 'package:grid_pos/core/utils/logger.dart';

/// Provider for the ProductLockService
final productLockServiceProvider = Provider<ProductLockService>((ref) {
  final firestore = FirebaseFirestore.instance;
  return ProductLockServiceImpl(firestore);
});

/// Service for managing pessimistic locks on products during checkout
abstract class ProductLockService {
  /// Acquires a lock on a product for checkout
  ///
  /// Returns true if the lock was acquired successfully, false otherwise
  Future<bool> acquireLock(
    String storeId,
    String tenantId,
    String productId,
    String uniqueLockId,
    String userId,
    String deviceId,
  );

  /// Releases a lock on a product after checkout
  Future<void> releaseLock(
    String storeId,
    String tenantId,
    String productId,
    String uniqueLockId,
  );
}

/// Implementation of ProductLockService
class ProductLockServiceImpl implements ProductLockService {
  final FirebaseFirestore _firestore;

  /// Creates a product lock service with the given Firestore instance
  ProductLockServiceImpl(this._firestore);

  @override
  Future<bool> acquireLock(
    String storeId,
    String tenantId,
    String productId,
    String uniqueLockId,
    String userId,
    String deviceId,
  ) async {
    try {
      // Reference to the lock document
      final lockRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.tenants)
          .doc(tenantId)
          .collection(FirestoreConstants.products)
          .doc(productId)
          .collection(FirestoreConstants.locks)
          .doc(uniqueLockId);

      // Try to create the lock document
      // The expiration time is set to 2 minutes from now
      await lockRef.set({
        FirestoreConstants.byUserId: userId,
        FirestoreConstants.byDeviceId: deviceId,
        FirestoreConstants.expire: FieldValue.serverTimestamp(),
      }, SetOptions(merge: false));

      Logger.info(
        'Lock acquired: $storeId/$tenantId/$productId ($uniqueLockId)',
      );
      return true;
    } catch (e) {
      Logger.error('Failed to acquire lock', e);
      return false;
    }
  }

  @override
  Future<void> releaseLock(
    String storeId,
    String tenantId,
    String productId,
    String uniqueLockId,
  ) async {
    try {
      // Reference to the lock document
      final lockRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.tenants)
          .doc(tenantId)
          .collection(FirestoreConstants.products)
          .doc(productId)
          .collection(FirestoreConstants.locks)
          .doc(uniqueLockId);

      // Delete the lock document
      await lockRef.delete();
      Logger.info(
        'Lock released: $storeId/$tenantId/$productId ($uniqueLockId)',
      );
    } catch (e) {
      // Log the error but don't throw - TTL will clean up eventually
      Logger.error('Failed to release lock', e);
    }
  }
}
