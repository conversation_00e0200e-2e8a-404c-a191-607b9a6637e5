import 'dart:typed_data';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/pos/domain/entities/sale_entity.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/// Provider for ReceiptPdfService
final receiptPdfServiceProvider = Provider<ReceiptPdfService>((ref) {
  return ReceiptPdfServiceImpl();
});

/// Service for generating receipt PDFs
abstract class ReceiptPdfService {
  /// Generates a PDF receipt from a sale entity
  Future<Uint8List> generateReceiptPdf(SaleEntity sale);
}

/// Implementation of ReceiptPdfService
class ReceiptPdfServiceImpl implements ReceiptPdfService {
  @override
  Future<Uint8List> generateReceiptPdf(SaleEntity sale) async {
    final pdf = pw.Document();
    final numberFormatter = NumberFormat('#,###.##');

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.roll80,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              // Title
              pw.Text(
                'Grid Store Receipt',
                style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 10),

              pw.Row(
                children: [
                  pw.Expanded(child: pw.Text('Receipt ID', style: pw.TextStyle(fontSize: 8))),
                  pw.Expanded(
                    child: pw.Text(sale.id ?? "Unknown", style: pw.TextStyle(fontSize: 8)),
                  ),
                ],
              ),

              pw.Row(
                children: [
                  pw.Expanded(child: pw.Text('Tenant ID', style: pw.TextStyle(fontSize: 8))),
                  pw.Expanded(child: pw.Text(sale.tenantId, style: pw.TextStyle(fontSize: 8))),
                ],
              ),

              pw.SizedBox(height: 10),

              // Item headers
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Expanded(
                    flex: 5,
                    child: pw.Text(
                      'Item',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10),
                    ),
                  ),
                  pw.Expanded(
                    flex: 2,
                    child: pw.Text(
                      'Qty',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10),
                      textAlign: pw.TextAlign.right,
                    ),
                  ),
                  pw.Expanded(
                    flex: 3,
                    child: pw.Text(
                      'Price',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10),
                      textAlign: pw.TextAlign.right,
                    ),
                  ),
                ],
              ),
              pw.Divider(thickness: 1),

              // Item list
              pw.Column(
                children:
                    sale.items.map((item) {
                      final total = item.price * item.qty;
                      return pw.Padding(
                        padding: const pw.EdgeInsets.symmetric(vertical: 3),
                        child: pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Expanded(
                              flex: 5,
                              child: pw.Text(item.name, style: const pw.TextStyle(fontSize: 9)),
                            ),
                            pw.Expanded(
                              flex: 2,
                              child: pw.Text(
                                'x${item.qty}',
                                style: const pw.TextStyle(fontSize: 9),
                                textAlign: pw.TextAlign.right,
                              ),
                            ),
                            pw.Expanded(
                              flex: 3,
                              child: pw.Text(
                                '\$${numberFormatter.format(total)}',
                                style: const pw.TextStyle(fontSize: 9),
                                textAlign: pw.TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
              ),
              pw.Divider(thickness: 1),

              // Total
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.end,
                children: [
                  pw.Text(
                    'Total:',
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12),
                  ),
                  pw.SizedBox(width: 10),
                  pw.Text(
                    '\$${numberFormatter.format(sale.totalAmount)}',
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12),
                  ),
                ],
              ),
              pw.SizedBox(height: 10),

              // Payment method
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text('Payment Method:', style: const pw.TextStyle(fontSize: 10)),
                  pw.Text(sale.paymentType, style: const pw.TextStyle(fontSize: 10)),
                ],
              ),
              pw.SizedBox(height: 20),

              // Footer
              pw.Text(
                'Thank you for your purchase!',
                style: const pw.TextStyle(fontSize: 10),
                textAlign: pw.TextAlign.center,
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                '${_formatTime(sale.createdAt)}',
                style: const pw.TextStyle(fontSize: 8),
                textAlign: pw.TextAlign.center,
              ),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  String _formatDate(DateTime? dateTime) {
    if (dateTime == null) return 'Unknown Date';
    return DateFormat('yyyy-MM-dd').format(dateTime);
  }

  String _formatTime(DateTime? dateTime) {
    if (dateTime == null) return '';
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  }
}
