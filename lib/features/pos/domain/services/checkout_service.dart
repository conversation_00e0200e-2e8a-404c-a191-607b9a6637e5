import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/core/constants/firestore_constants.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/pos/data/models/sale_dto.dart';
import 'package:grid_pos/features/pos/domain/entities/cart_item.dart';
import 'package:grid_pos/features/pos/domain/entities/sale_entity.dart';
import 'package:grid_pos/features/pos/domain/entities/sale_item_embed.dart';
import 'package:grid_pos/features/pos/domain/services/product_lock_service.dart';
import 'package:uuid/uuid.dart';

/// Provider for the CheckoutService
final checkoutServiceProvider = Provider<CheckoutService>((ref) {
  final firestore = FirebaseFirestore.instance;
  final lockService = ref.watch(productLockServiceProvider);
  return CheckoutServiceImpl(firestore, lockService);
});

/// Service for handling the checkout process
abstract class CheckoutService {
  /// Performs the checkout process
  ///
  /// [storeId] - ID of the store
  /// [items] - List of cart items to checkout
  /// [paymentType] - Type of payment (cash, card, etc)
  /// [cashierId] - ID of the user performing the checkout
  /// [deviceId] - ID of the device used for checkout
  ///
  /// Returns the created SaleEntity if successful, null otherwise
  Future<SaleEntity?> performCheckout(
    String storeId,
    List<CartItem> items,
    String paymentType,
    String cashierId,
    String deviceId,
  );
}

/// Implementation of the CheckoutService
class CheckoutServiceImpl implements CheckoutService {
  final FirebaseFirestore _firestore;
  final ProductLockService _lockService;

  /// Creates a checkout service with the given Firestore instance and lock service
  CheckoutServiceImpl(this._firestore, this._lockService);

  @override
  Future<SaleEntity?> performCheckout(
    String storeId,
    List<CartItem> items,
    String paymentType,
    String cashierId,
    String deviceId,
  ) async {
    if (items.isEmpty) {
      throw Exception('Cannot checkout with an empty cart');
    }

    // Generate a unique ID for this checkout attempt
    final checkoutId = const Uuid().v4();

    // Map to keep track of acquired locks
    final acquiredLocks = <String, CartItem>{};

    try {
      // 1. (Outside transaction) Acquire locks for all products
      Logger.info('Checkout: Acquiring locks for ${items.length} products');
      for (final cartItem in items) {
        final product = cartItem.product;
        final uniqueLockId = '$deviceId-$checkoutId-${product.id}';

        final lockAcquired = await _lockService.acquireLock(
          storeId,
          product.tenantId,
          product.id,
          uniqueLockId,
          cashierId,
          deviceId,
        );

        if (!lockAcquired) {
          throw Exception('Failed to acquire lock for product ${product.name}');
        }

        // Keep track of acquired locks for cleanup
        acquiredLocks[uniqueLockId] = cartItem;
      }

      // 2. Run Firestore transaction
      Logger.info('Checkout: Running transaction');
      final saleId = await _firestore.runTransaction<String>((
        transaction,
      ) async {
        // 2a. Read and validate product stock
        final productRefs = <DocumentReference>[];
        final productDocs = <DocumentSnapshot>[];

        // First read all product documents to verify stock
        for (final cartItem in items) {
          final product = cartItem.product;
          final productRef = _firestore
              .collection(FirestoreConstants.stores)
              .doc(storeId)
              .collection(FirestoreConstants.tenants)
              .doc(product.tenantId)
              .collection(FirestoreConstants.products)
              .doc(product.id);

          productRefs.add(productRef);
          final productDoc = await transaction.get(productRef);

          // Verify product exists
          if (!productDoc.exists) {
            throw Exception('Product ${product.name} no longer exists');
          }

          // Verify product is active
          final productData = productDoc.data() as Map<String, dynamic>;
          if (productData[FirestoreConstants.active] != true) {
            throw Exception('Product ${product.name} is not active');
          }

          // Verify enough stock
          final currentStock = productData[FirestoreConstants.stock] as int;
          if (currentStock < cartItem.quantity) {
            throw Exception(
              'Not enough stock for ${product.name}. Available: $currentStock, Requested: ${cartItem.quantity}',
            );
          }

          productDocs.add(productDoc);
        }

        // 2b. Create the sale document
        // Calculate total amount
        final totalAmount = items.fold<double>(
          0,
          (sum, item) => sum + (item.product.price * item.quantity),
        );

        // Create sale items
        final saleItems =
            items.map((cartItem) {
              return SaleItemEmbed(
                productId: cartItem.product.id,
                tenantId: cartItem.product.tenantId,
                qty: cartItem.quantity,
                price: cartItem.product.price,
                name: cartItem.product.name,
                sku: cartItem.product.sku,
              );
            }).toList();

        // Create the sale entity
        final sale = SaleEntity(
          storeId: storeId,
          tenantId:
              items
                  .first
                  .product
                  .tenantId, // Assuming all items belong to the same tenant
          cashierId: cashierId,
          status: 'completed',
          totalAmount: totalAmount,
          items: saleItems,
          paymentType: paymentType,
          printed: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Create the sale document in Firestore
        final saleRef =
            _firestore
                .collection(FirestoreConstants.stores)
                .doc(storeId)
                .collection(FirestoreConstants.sales)
                .doc(); // Auto-generate ID

        transaction.set(saleRef, SaleDto.toFirestore(sale));

        // 2c. Update product stocks
        for (var i = 0; i < items.length; i++) {
          final cartItem = items[i];
          final productRef = productRefs[i];

          // Update stock
          transaction.update(productRef, {
            FirestoreConstants.stock: FieldValue.increment(-cartItem.quantity),
            FirestoreConstants.updatedAt: FieldValue.serverTimestamp(),
          });
        }

        // Return the sale ID
        return saleRef.id;
      }, maxAttempts: 3);

      Logger.info('Checkout: Transaction successful, sale ID: $saleId');

      // 3. (Outside transaction) Release all locks
      _releaseAllLocks(storeId, acquiredLocks);

      // Return the created sale entity
      final createdSale =
          await _firestore
              .collection(FirestoreConstants.stores)
              .doc(storeId)
              .collection(FirestoreConstants.sales)
              .doc(saleId)
              .get();

      if (!createdSale.exists) {
        Logger.error(
          'Checkout: Sale document not found after successful transaction',
        );
        return null;
      }

      return SaleDto.fromFirestore(createdSale, null);
    } catch (e) {
      Logger.error('Checkout failed', e);

      // Release any acquired locks in case of failure
      if (acquiredLocks.isNotEmpty) {
        _releaseAllLocks(storeId, acquiredLocks);
      }

      // Re-throw the exception
      rethrow;
    }
  }

  // Helper to release all acquired locks
  Future<void> _releaseAllLocks(
    String storeId,
    Map<String, CartItem> acquiredLocks,
  ) async {
    Logger.info('Releasing ${acquiredLocks.length} product locks');

    for (final entry in acquiredLocks.entries) {
      final lockId = entry.key;
      final cartItem = entry.value;
      final product = cartItem.product;

      try {
        await _lockService.releaseLock(
          storeId,
          product.tenantId,
          product.id,
          lockId,
        );
      } catch (e) {
        // Log but don't throw - TTL will clean up eventually
        Logger.error('Failed to release lock $lockId', e);
      }
    }
  }
}
