import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:grid_pos/features/pos/domain/entities/sale_item_embed.dart';

part 'sale_entity.freezed.dart';
part 'sale_entity.g.dart';

/// Status of a sale
enum SaleStatus {
  /// Sale is completed successfully
  completed,

  /// Sale has been cancelled
  cancelled,
}

/// Entity representing a sale
@freezed
abstract class SaleEntity with _$SaleEntity {
  /// Creates a sale
  const factory SaleEntity({
    /// The unique ID of the sale
    String? id,

    /// The ID of the store where the sale took place
    required String storeId,

    /// The ID of the tenant (used for filtering)
    required String tenantId,

    /// The ID of the cashier who processed the sale
    required String cashierId,

    /// The status of the sale (completed or cancelled)
    @Default('completed') String status,

    /// The total amount of the sale
    required double totalAmount,

    /// The items in the sale
    required List<SaleItemEmbed> items,

    /// The payment type used (cash, card, etc.)
    required String paymentType,

    /// Whether a receipt has been printed
    @Default(false) bool printed,

    /// When the sale was created
    DateTime? createdAt,

    /// When the sale was last updated
    DateTime? updatedAt,
  }) = _SaleEntity;

  /// Creates a SaleEntity from JSON
  factory SaleEntity.fromJson(Map<String, dynamic> json) =>
      _$SaleEntityFromJson(json);
}
