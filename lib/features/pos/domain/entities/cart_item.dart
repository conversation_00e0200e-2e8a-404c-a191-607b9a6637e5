import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:grid_pos/features/product_mgmt/domain/entities/product_entity.dart';

part 'cart_item.freezed.dart';
part 'cart_item.g.dart';

/// An item in the shopping cart, composed of a product and quantity
@freezed
abstract class CartItem with _$CartItem {
  /// Creates a cart item
  const factory CartItem({
    /// The product in the cart
    required ProductEntity product,

    /// The quantity of the product in the cart
    required int quantity,
  }) = _CartItem;

  /// Creates a CartItem from JSON
  factory CartItem.fromJson(Map<String, dynamic> json) =>
      _$CartItemFromJson(json);
}
