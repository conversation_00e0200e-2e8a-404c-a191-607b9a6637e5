// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sale_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SaleEntity _$SaleEntityFromJson(Map<String, dynamic> json) => _SaleEntity(
  id: json['id'] as String?,
  storeId: json['storeId'] as String,
  tenantId: json['tenantId'] as String,
  cashierId: json['cashierId'] as String,
  status: json['status'] as String? ?? 'completed',
  totalAmount: (json['totalAmount'] as num).toDouble(),
  items:
      (json['items'] as List<dynamic>)
          .map((e) => SaleItemEmbed.fromJson(e as Map<String, dynamic>))
          .toList(),
  paymentType: json['paymentType'] as String,
  printed: json['printed'] as bool? ?? false,
  createdAt:
      json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
  updatedAt:
      json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$SaleEntityToJson(_SaleEntity instance) =>
    <String, dynamic>{
      'id': instance.id,
      'storeId': instance.storeId,
      'tenantId': instance.tenantId,
      'cashierId': instance.cashierId,
      'status': instance.status,
      'totalAmount': instance.totalAmount,
      'items': instance.items,
      'paymentType': instance.paymentType,
      'printed': instance.printed,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };
