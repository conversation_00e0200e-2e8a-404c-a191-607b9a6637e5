// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sale_item_embed.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SaleItemEmbed {

/// The ID of the product
 String get productId;/// The ID of the tenant
 String get tenantId;/// The quantity of the product sold
 int get qty;/// The price of the product when sold
 double get price;/// The name of the product when sold
 String get name;/// The SKU of the product when sold
 String get sku;
/// Create a copy of SaleItemEmbed
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SaleItemEmbedCopyWith<SaleItemEmbed> get copyWith => _$SaleItemEmbedCopyWithImpl<SaleItemEmbed>(this as SaleItemEmbed, _$identity);

  /// Serializes this SaleItemEmbed to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SaleItemEmbed&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.tenantId, tenantId) || other.tenantId == tenantId)&&(identical(other.qty, qty) || other.qty == qty)&&(identical(other.price, price) || other.price == price)&&(identical(other.name, name) || other.name == name)&&(identical(other.sku, sku) || other.sku == sku));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,tenantId,qty,price,name,sku);

@override
String toString() {
  return 'SaleItemEmbed(productId: $productId, tenantId: $tenantId, qty: $qty, price: $price, name: $name, sku: $sku)';
}


}

/// @nodoc
abstract mixin class $SaleItemEmbedCopyWith<$Res>  {
  factory $SaleItemEmbedCopyWith(SaleItemEmbed value, $Res Function(SaleItemEmbed) _then) = _$SaleItemEmbedCopyWithImpl;
@useResult
$Res call({
 String productId, String tenantId, int qty, double price, String name, String sku
});




}
/// @nodoc
class _$SaleItemEmbedCopyWithImpl<$Res>
    implements $SaleItemEmbedCopyWith<$Res> {
  _$SaleItemEmbedCopyWithImpl(this._self, this._then);

  final SaleItemEmbed _self;
  final $Res Function(SaleItemEmbed) _then;

/// Create a copy of SaleItemEmbed
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? productId = null,Object? tenantId = null,Object? qty = null,Object? price = null,Object? name = null,Object? sku = null,}) {
  return _then(_self.copyWith(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,tenantId: null == tenantId ? _self.tenantId : tenantId // ignore: cast_nullable_to_non_nullable
as String,qty: null == qty ? _self.qty : qty // ignore: cast_nullable_to_non_nullable
as int,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,sku: null == sku ? _self.sku : sku // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _SaleItemEmbed implements SaleItemEmbed {
  const _SaleItemEmbed({required this.productId, required this.tenantId, required this.qty, required this.price, required this.name, required this.sku});
  factory _SaleItemEmbed.fromJson(Map<String, dynamic> json) => _$SaleItemEmbedFromJson(json);

/// The ID of the product
@override final  String productId;
/// The ID of the tenant
@override final  String tenantId;
/// The quantity of the product sold
@override final  int qty;
/// The price of the product when sold
@override final  double price;
/// The name of the product when sold
@override final  String name;
/// The SKU of the product when sold
@override final  String sku;

/// Create a copy of SaleItemEmbed
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SaleItemEmbedCopyWith<_SaleItemEmbed> get copyWith => __$SaleItemEmbedCopyWithImpl<_SaleItemEmbed>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SaleItemEmbedToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SaleItemEmbed&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.tenantId, tenantId) || other.tenantId == tenantId)&&(identical(other.qty, qty) || other.qty == qty)&&(identical(other.price, price) || other.price == price)&&(identical(other.name, name) || other.name == name)&&(identical(other.sku, sku) || other.sku == sku));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,tenantId,qty,price,name,sku);

@override
String toString() {
  return 'SaleItemEmbed(productId: $productId, tenantId: $tenantId, qty: $qty, price: $price, name: $name, sku: $sku)';
}


}

/// @nodoc
abstract mixin class _$SaleItemEmbedCopyWith<$Res> implements $SaleItemEmbedCopyWith<$Res> {
  factory _$SaleItemEmbedCopyWith(_SaleItemEmbed value, $Res Function(_SaleItemEmbed) _then) = __$SaleItemEmbedCopyWithImpl;
@override @useResult
$Res call({
 String productId, String tenantId, int qty, double price, String name, String sku
});




}
/// @nodoc
class __$SaleItemEmbedCopyWithImpl<$Res>
    implements _$SaleItemEmbedCopyWith<$Res> {
  __$SaleItemEmbedCopyWithImpl(this._self, this._then);

  final _SaleItemEmbed _self;
  final $Res Function(_SaleItemEmbed) _then;

/// Create a copy of SaleItemEmbed
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? productId = null,Object? tenantId = null,Object? qty = null,Object? price = null,Object? name = null,Object? sku = null,}) {
  return _then(_SaleItemEmbed(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,tenantId: null == tenantId ? _self.tenantId : tenantId // ignore: cast_nullable_to_non_nullable
as String,qty: null == qty ? _self.qty : qty // ignore: cast_nullable_to_non_nullable
as int,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,sku: null == sku ? _self.sku : sku // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
