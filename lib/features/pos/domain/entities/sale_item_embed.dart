import 'package:freezed_annotation/freezed_annotation.dart';

part 'sale_item_embed.freezed.dart';
part 'sale_item_embed.g.dart';

/// An item in a sale, embedded in a SaleEntity
@freezed
abstract class SaleItemEmbed with _$SaleItemEmbed {
  /// Creates a sale item
  const factory SaleItemEmbed({
    /// The ID of the product
    required String productId,

    /// The ID of the tenant
    required String tenantId,

    /// The quantity of the product sold
    required int qty,

    /// The price of the product when sold
    required double price,

    /// The name of the product when sold
    required String name,

    /// The SKU of the product when sold
    required String sku,
  }) = _SaleItemEmbed;

  /// Creates a SaleItemEmbed from JSON
  factory SaleItemEmbed.fromJson(Map<String, dynamic> json) =>
      _$SaleItemEmbedFromJson(json);
}
