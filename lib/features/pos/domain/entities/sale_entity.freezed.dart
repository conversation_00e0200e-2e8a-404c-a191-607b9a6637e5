// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sale_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SaleEntity {

/// The unique ID of the sale
 String? get id;/// The ID of the store where the sale took place
 String get storeId;/// The ID of the tenant (used for filtering)
 String get tenantId;/// The ID of the cashier who processed the sale
 String get cashierId;/// The status of the sale (completed or cancelled)
 String get status;/// The total amount of the sale
 double get totalAmount;/// The items in the sale
 List<SaleItemEmbed> get items;/// The payment type used (cash, card, etc.)
 String get paymentType;/// Whether a receipt has been printed
 bool get printed;/// When the sale was created
 DateTime? get createdAt;/// When the sale was last updated
 DateTime? get updatedAt;
/// Create a copy of SaleEntity
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SaleEntityCopyWith<SaleEntity> get copyWith => _$SaleEntityCopyWithImpl<SaleEntity>(this as SaleEntity, _$identity);

  /// Serializes this SaleEntity to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SaleEntity&&(identical(other.id, id) || other.id == id)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.tenantId, tenantId) || other.tenantId == tenantId)&&(identical(other.cashierId, cashierId) || other.cashierId == cashierId)&&(identical(other.status, status) || other.status == status)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&const DeepCollectionEquality().equals(other.items, items)&&(identical(other.paymentType, paymentType) || other.paymentType == paymentType)&&(identical(other.printed, printed) || other.printed == printed)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,storeId,tenantId,cashierId,status,totalAmount,const DeepCollectionEquality().hash(items),paymentType,printed,createdAt,updatedAt);

@override
String toString() {
  return 'SaleEntity(id: $id, storeId: $storeId, tenantId: $tenantId, cashierId: $cashierId, status: $status, totalAmount: $totalAmount, items: $items, paymentType: $paymentType, printed: $printed, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $SaleEntityCopyWith<$Res>  {
  factory $SaleEntityCopyWith(SaleEntity value, $Res Function(SaleEntity) _then) = _$SaleEntityCopyWithImpl;
@useResult
$Res call({
 String? id, String storeId, String tenantId, String cashierId, String status, double totalAmount, List<SaleItemEmbed> items, String paymentType, bool printed, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class _$SaleEntityCopyWithImpl<$Res>
    implements $SaleEntityCopyWith<$Res> {
  _$SaleEntityCopyWithImpl(this._self, this._then);

  final SaleEntity _self;
  final $Res Function(SaleEntity) _then;

/// Create a copy of SaleEntity
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? storeId = null,Object? tenantId = null,Object? cashierId = null,Object? status = null,Object? totalAmount = null,Object? items = null,Object? paymentType = null,Object? printed = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,storeId: null == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as String,tenantId: null == tenantId ? _self.tenantId : tenantId // ignore: cast_nullable_to_non_nullable
as String,cashierId: null == cashierId ? _self.cashierId : cashierId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,items: null == items ? _self.items : items // ignore: cast_nullable_to_non_nullable
as List<SaleItemEmbed>,paymentType: null == paymentType ? _self.paymentType : paymentType // ignore: cast_nullable_to_non_nullable
as String,printed: null == printed ? _self.printed : printed // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _SaleEntity implements SaleEntity {
  const _SaleEntity({this.id, required this.storeId, required this.tenantId, required this.cashierId, this.status = 'completed', required this.totalAmount, required final  List<SaleItemEmbed> items, required this.paymentType, this.printed = false, this.createdAt, this.updatedAt}): _items = items;
  factory _SaleEntity.fromJson(Map<String, dynamic> json) => _$SaleEntityFromJson(json);

/// The unique ID of the sale
@override final  String? id;
/// The ID of the store where the sale took place
@override final  String storeId;
/// The ID of the tenant (used for filtering)
@override final  String tenantId;
/// The ID of the cashier who processed the sale
@override final  String cashierId;
/// The status of the sale (completed or cancelled)
@override@JsonKey() final  String status;
/// The total amount of the sale
@override final  double totalAmount;
/// The items in the sale
 final  List<SaleItemEmbed> _items;
/// The items in the sale
@override List<SaleItemEmbed> get items {
  if (_items is EqualUnmodifiableListView) return _items;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_items);
}

/// The payment type used (cash, card, etc.)
@override final  String paymentType;
/// Whether a receipt has been printed
@override@JsonKey() final  bool printed;
/// When the sale was created
@override final  DateTime? createdAt;
/// When the sale was last updated
@override final  DateTime? updatedAt;

/// Create a copy of SaleEntity
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SaleEntityCopyWith<_SaleEntity> get copyWith => __$SaleEntityCopyWithImpl<_SaleEntity>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SaleEntityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SaleEntity&&(identical(other.id, id) || other.id == id)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.tenantId, tenantId) || other.tenantId == tenantId)&&(identical(other.cashierId, cashierId) || other.cashierId == cashierId)&&(identical(other.status, status) || other.status == status)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&const DeepCollectionEquality().equals(other._items, _items)&&(identical(other.paymentType, paymentType) || other.paymentType == paymentType)&&(identical(other.printed, printed) || other.printed == printed)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,storeId,tenantId,cashierId,status,totalAmount,const DeepCollectionEquality().hash(_items),paymentType,printed,createdAt,updatedAt);

@override
String toString() {
  return 'SaleEntity(id: $id, storeId: $storeId, tenantId: $tenantId, cashierId: $cashierId, status: $status, totalAmount: $totalAmount, items: $items, paymentType: $paymentType, printed: $printed, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$SaleEntityCopyWith<$Res> implements $SaleEntityCopyWith<$Res> {
  factory _$SaleEntityCopyWith(_SaleEntity value, $Res Function(_SaleEntity) _then) = __$SaleEntityCopyWithImpl;
@override @useResult
$Res call({
 String? id, String storeId, String tenantId, String cashierId, String status, double totalAmount, List<SaleItemEmbed> items, String paymentType, bool printed, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class __$SaleEntityCopyWithImpl<$Res>
    implements _$SaleEntityCopyWith<$Res> {
  __$SaleEntityCopyWithImpl(this._self, this._then);

  final _SaleEntity _self;
  final $Res Function(_SaleEntity) _then;

/// Create a copy of SaleEntity
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? storeId = null,Object? tenantId = null,Object? cashierId = null,Object? status = null,Object? totalAmount = null,Object? items = null,Object? paymentType = null,Object? printed = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_SaleEntity(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,storeId: null == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as String,tenantId: null == tenantId ? _self.tenantId : tenantId // ignore: cast_nullable_to_non_nullable
as String,cashierId: null == cashierId ? _self.cashierId : cashierId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,items: null == items ? _self._items : items // ignore: cast_nullable_to_non_nullable
as List<SaleItemEmbed>,paymentType: null == paymentType ? _self.paymentType : paymentType // ignore: cast_nullable_to_non_nullable
as String,printed: null == printed ? _self.printed : printed // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
