import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/admin/presentation/providers/users_provider.dart';
import 'package:grid_pos/features/admin/presentation/widgets/user_list_item.dart';
import 'package:grid_pos/features/auth/domain/entities/user_app_model.dart';
import 'package:grid_pos/shared/widgets/custom_snackbar.dart';

/// A page for managing users by administrators
class UserManagementPage extends ConsumerStatefulWidget {
  /// Creates a user management page
  const UserManagementPage({super.key});

  @override
  ConsumerState<UserManagementPage> createState() => _UserManagementPageState();
}

class _UserManagementPageState extends ConsumerState<UserManagementPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  UserRole? _selectedRoleFilter;
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final usersAsyncValue = ref.watch(usersStreamProvider);
    final updateUserRoleState = ref.watch(updateUserRoleProvider);

    // Listen for errors in the update user role state
    ref.listen<UpdateUserRoleState>(updateUserRoleProvider, (previous, current) {
      if (current.errorMessage != null &&
          (previous == null || previous.errorMessage != current.errorMessage)) {
        showErrorSnackBar(context, current.errorMessage!);
      }
    });

    return Scaffold(
      appBar: _buildAppBar(),
      body: usersAsyncValue.when(
        data: (users) => _buildScrollableContent(users, updateUserRoleState),
        loading:
            () => const Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [CircularProgressIndicator(), SizedBox(height: 16), Text('載入用戶資料中...')],
              ),
            ),
        error:
            (error, stack) => Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('載入用戶時發生錯誤: $error'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => ref.refresh(usersStreamProvider),
                    child: const Text('重試'),
                  ),
                ],
              ),
            ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    if (_isSearching) {
      return AppBar(
        automaticallyImplyLeading: false,
        actions: [IconButton(icon: const Icon(Icons.close), onPressed: _exitSearchMode)],
        title: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
          ),
          child: TextField(
            controller: _searchController,
            autofocus: true,
            decoration: InputDecoration(
              hintText: '搜尋用戶名稱或電子郵件...',
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(vertical: 12),
              hintStyle: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontSize: 16,
              ),
              prefixIcon: Icon(
                Icons.search,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                size: 20,
              ),
              suffixIcon:
                  _searchQuery.isNotEmpty
                      ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: IconButton(
                          icon: const Text('clear'),
                          onPressed: () {
                            _searchController.clear();
                            setState(() => _searchQuery = '');
                          },
                        ),
                      )
                      : null,
            ),
            style: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant, fontSize: 16),
            onChanged: (value) => setState(() => _searchQuery = value.toLowerCase()),
          ),
        ),
      );
    }

    return AppBar(
      title: const Text('用戶管理', style: TextStyle(fontWeight: FontWeight.bold)),
      actions: [
        IconButton(
          icon: Stack(
            children: [
              const Icon(Icons.search),
              if (_searchQuery.isNotEmpty)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                  ),
                ),
            ],
          ),
          onPressed: _enterSearchMode,
        ),
      ],
    );
  }

  void _enterSearchMode() {
    setState(() => _isSearching = true);
  }

  void _exitSearchMode() {
    setState(() {
      _isSearching = false;
      _searchController.clear();
      _searchQuery = '';
    });
  }

  /// 統一的滾動內容，包含搜索統計和用戶列表
  Widget _buildScrollableContent(
    List<UserAppModel> users,
    UpdateUserRoleState updateUserRoleState,
  ) {
    final filteredUsers = _filterUsers(users);

    // 如果沒有用戶，顯示空狀態
    if (filteredUsers.isEmpty) {
      return RefreshIndicator(
        onRefresh: () async => ref.refresh(usersStreamProvider),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              Padding(padding: const EdgeInsets.all(16), child: _buildSearchAndStats(users)),
              SizedBox(height: MediaQuery.of(context).size.height * 0.2),
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _searchQuery.isNotEmpty || _selectedRoleFilter != null
                          ? Icons.search_off
                          : Icons.people_outline,
                      size: 64,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _searchQuery.isNotEmpty || _selectedRoleFilter != null
                          ? '沒有找到符合條件的用戶'
                          : '暫無用戶',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    if (_searchQuery.isNotEmpty || _selectedRoleFilter != null) ...[
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          setState(() => _selectedRoleFilter = null);
                          if (_isSearching) {
                            _exitSearchMode();
                          }
                        },
                        child: const Text('清除篩選'),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    // 有用戶時，使用統一的 ListView
    return RefreshIndicator(
      onRefresh: () async => ref.refresh(usersStreamProvider),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredUsers.length + 1, // +1 for the header
        itemBuilder: (context, index) {
          // 第一個項目是搜索和統計區域
          if (index == 0) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: _buildSearchAndStats(users),
            );
          }

          // 其餘項目是用戶列表
          final userIndex = index - 1;
          final user = filteredUsers[userIndex];
          final itemIsLoading = updateUserRoleState.processingUserId == user.uid;

          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: UserListItem(key: Key(user.uid), user: user, isLoading: itemIsLoading),
          );
        },
      ),
    );
  }

  Widget _buildSearchAndStats(List<UserAppModel> users) {
    final filteredUsers = _filterUsers(users);
    final stats = _calculateRoleStats(users);

    return Column(
      children: [
        // Search result indicator when searching
        if (_isSearching && _searchQuery.isNotEmpty) ...[
          Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color:
                  filteredUsers.isEmpty
                      ? Colors.orange.withOpacity(0.1)
                      : Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color:
                    filteredUsers.isEmpty
                        ? Colors.orange.withOpacity(0.3)
                        : Colors.green.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  filteredUsers.isEmpty ? Icons.search_off : Icons.check_circle_outline,
                  color: filteredUsers.isEmpty ? Colors.orange : Colors.green,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    filteredUsers.isEmpty
                        ? '沒有找到符合 "$_searchQuery" 的用戶'
                        : '找到 ${filteredUsers.length} 個符合 "$_searchQuery" 的用戶',
                    style: TextStyle(
                      color: filteredUsers.isEmpty ? Colors.orange.shade700 : Colors.green.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (filteredUsers.isNotEmpty) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${filteredUsers.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
        // Stats Card with integrated filters
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    const Icon(Icons.people, size: 24),
                    const SizedBox(width: 8),
                    Text('用戶總數: ${users.length}', style: Theme.of(context).textTheme.titleMedium),
                    if (filteredUsers.length != users.length) ...[
                      const Spacer(),
                      Text('顯示: ${filteredUsers.length}'),
                    ],
                  ],
                ),
                const SizedBox(height: 12),
                // Combined stats and filter row
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildStatFilterItem('全部', users.length, Colors.grey, null),
                    _buildStatFilterItem(
                      '管理員',
                      stats[UserRole.admin] ?? 0,
                      Colors.red,
                      UserRole.admin,
                    ),
                    _buildStatFilterItem(
                      '租戶',
                      stats[UserRole.tenant] ?? 0,
                      Colors.blue,
                      UserRole.tenant,
                    ),
                    _buildStatFilterItem(
                      '收銀員',
                      stats[UserRole.cashier] ?? 0,
                      Colors.green,
                      UserRole.cashier,
                    ),
                    _buildStatFilterItem(
                      '待審核',
                      stats[UserRole.pendingApproval] ?? 0,
                      Colors.orange,
                      UserRole.pendingApproval,
                    ),
                  ],
                ),
                // Active filter indicator
                if (_selectedRoleFilter != null || _searchQuery.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (_selectedRoleFilter != null)
                        Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: Chip(
                            label: Text('角色: ${_getRoleDisplayName(_selectedRoleFilter!)}'),
                            onDeleted: () => setState(() => _selectedRoleFilter = null),
                            deleteIcon: const Icon(Icons.close, size: 18),
                          ),
                        ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatFilterItem(String label, int count, Color color, UserRole? role) {
    final isSelected = _selectedRoleFilter == role;

    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () => setState(() => _selectedRoleFilter = isSelected ? null : role),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: isSelected ? Border.all(color: color, width: 2) : null,
              color: isSelected ? color.withOpacity(0.1) : null,
            ),
            child: Column(
              children: [
                Text(
                  '$count',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isSelected ? color : color,
                  ),
                ),
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: isSelected ? color : null,
                    fontWeight: isSelected ? FontWeight.w600 : null,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return '管理員';
      case UserRole.tenant:
        return '租戶';
      case UserRole.cashier:
        return '收銀員';
      case UserRole.pendingApproval:
        return '待審核';
    }
  }

  List<UserAppModel> _filterUsers(List<UserAppModel> users) {
    var filteredUsers = users;

    // Search filter
    if (_searchQuery.isNotEmpty) {
      filteredUsers =
          filteredUsers.where((user) {
            final displayName = user.displayName?.toLowerCase() ?? '';
            final email = user.email.toLowerCase();
            return displayName.contains(_searchQuery) || email.contains(_searchQuery);
          }).toList();
    }

    // Role filter
    if (_selectedRoleFilter != null) {
      filteredUsers =
          filteredUsers.where((user) {
            return user.role == _selectedRoleFilter!.value;
          }).toList();
    }

    // Sort by name
    filteredUsers.sort((a, b) {
      final aName = a.displayName ?? a.email;
      final bName = b.displayName ?? b.email;
      return aName.toLowerCase().compareTo(bName.toLowerCase());
    });

    return filteredUsers;
  }

  Map<UserRole, int> _calculateRoleStats(List<UserAppModel> users) {
    final stats = <UserRole, int>{};
    for (final role in UserRole.values) {
      stats[role] = 0;
    }
    for (final user in users) {
      final role = UserRole.fromValue(user.role);
      stats[role] = (stats[role] ?? 0) + 1;
    }
    return stats;
  }
}
