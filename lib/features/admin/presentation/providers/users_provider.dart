import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/auth/domain/entities/user_app_model.dart';
import 'package:grid_pos/features/auth/data/models/user_app_model_dto.dart';

/// Provider for the Firestore instance
final firestoreProvider = Provider<FirebaseFirestore>((ref) {
  return FirebaseFirestore.instance;
});

/// Provider for the users collection
final usersCollectionProvider = Provider<CollectionReference<Map<String, dynamic>>>((ref) {
  final firestore = ref.watch(firestoreProvider);
  return firestore.collection('users');
});

/// Provider that streams all users
final usersStreamProvider = StreamProvider<List<UserAppModel>>((ref) {
  final usersCollection = ref.watch(usersCollectionProvider);

  return usersCollection.snapshots().map((snapshot) {
    return snapshot.docs.map((doc) {
      return UserAppModelDto.fromFirestore(doc);
    }).toList();
  });
});

/// User role options
enum UserRole {
  /// Pending approval role
  pendingApproval('pending_approval', '待審核'),

  /// Admin role
  admin('admin', '管理員'),

  /// Tenant role
  tenant('tenant', '租戶'),

  /// Cashier role
  cashier('cashier', '收銀員');

  /// Constructor
  const UserRole(this.value, this.displayName);

  /// String value of the role
  final String value;

  /// Display name of the role
  final String displayName;

  /// Get role from string value
  static UserRole fromValue(String value) {
    return UserRole.values.firstWhere((role) => role.value == value, orElse: () => UserRole.pendingApproval);
  }
}

/// State for updating user role
class UpdateUserRoleState {
  /// Whether the update is in progress
  final bool isLoading;

  /// Error message if any
  final String? errorMessage;

  /// ID of the user being processed
  final String? processingUserId;

  /// Constructor
  const UpdateUserRoleState({this.isLoading = false, this.errorMessage, this.processingUserId});

  /// Copy with new values
  UpdateUserRoleState copyWith({bool? isLoading, String? errorMessage, String? processingUserId, bool clearErrorMessage = false, bool clearProcessingUserId = false}) {
    return UpdateUserRoleState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: clearErrorMessage ? null : errorMessage ?? this.errorMessage,
      processingUserId: clearProcessingUserId ? null : processingUserId ?? this.processingUserId,
    );
  }
}

/// Notifier for updating user role
class UpdateUserRoleNotifier extends StateNotifier<UpdateUserRoleState> {
  /// Firestore collection reference
  final CollectionReference<Map<String, dynamic>> _usersCollection;

  /// Constructor
  UpdateUserRoleNotifier(this._usersCollection) : super(const UpdateUserRoleState());

  /// Update a user's role
  Future<void> updateUserRole(String userId, String newRole) async {
    try {
      state = state.copyWith(isLoading: true, processingUserId: userId, clearErrorMessage: true);

      await _usersCollection.doc(userId).update({'role': newRole});

      state = state.copyWith(isLoading: false, clearProcessingUserId: true);
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: 'Failed to update user role: ${e.toString()}', clearProcessingUserId: true);
      rethrow;
    }
  }
}

/// Provider for the update user role notifier
final updateUserRoleProvider = StateNotifierProvider<UpdateUserRoleNotifier, UpdateUserRoleState>((ref) {
  final usersCollection = ref.watch(usersCollectionProvider);
  return UpdateUserRoleNotifier(usersCollection);
});
