import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/admin/presentation/providers/users_provider.dart';
import 'package:grid_pos/features/auth/domain/entities/user_app_model.dart';
import 'package:intl/intl.dart';

/// A widget that displays a user in the admin panel
class UserListItem extends ConsumerWidget {
  /// The user to display
  final UserAppModel user;

  /// Whether the item is loading
  final bool isLoading;

  /// Create a user list item
  const UserListItem({super.key, required this.user, this.isLoading = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final roleEnum = UserRole.fromValue(user.role);
    final dateFormat = DateFormat('MM-dd HH:mm');

    return Card(
      elevation: isLoading ? 4 : 2,
      shadowColor: isLoading ? Theme.of(context).colorScheme.primary.withOpacity(0.3) : null,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12), side: isLoading ? BorderSide(color: Theme.of(context).colorScheme.primary, width: 2) : BorderSide.none),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showUserDetails(context),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Main user info row
              Row(children: [_buildUserAvatar(context, roleEnum), const SizedBox(width: 16), Expanded(child: _buildUserInfo(context)), _buildRoleDropdown(context, ref)]),

              // Additional info row
              const SizedBox(height: 12),
              _buildAdditionalInfo(context, dateFormat),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserAvatar(BuildContext context, UserRole role) {
    final roleColor = _getRoleColor(role);
    final initial = user.email.isNotEmpty ? user.email[0].toUpperCase() : '?';

    return Stack(
      children: [
        CircleAvatar(radius: 28, backgroundColor: roleColor, child: Text(initial, style: const TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold))),
        // Role indicator dot
        Positioned(
          bottom: 0,
          right: 0,
          child: Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(color: roleColor, shape: BoxShape.circle, border: Border.all(color: Colors.white, width: 2)),
            child: Icon(_getRoleIcon(role), size: 8, color: Colors.white),
          ),
        ),
      ],
    );
  }

  Widget _buildUserInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display name
        Text(user.displayName ?? user.email, style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600), maxLines: 1, overflow: TextOverflow.ellipsis),
        const SizedBox(height: 4),
        // Email
        Text(user.email, style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.onSurfaceVariant), maxLines: 1, overflow: TextOverflow.ellipsis),
        const SizedBox(height: 4),
        // Role badge
        _buildRoleBadge(context),
      ],
    );
  }

  Widget _buildRoleBadge(BuildContext context) {
    final roleEnum = UserRole.fromValue(user.role);
    final roleColor = _getRoleColor(roleEnum);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(color: roleColor.withOpacity(0.1), borderRadius: BorderRadius.circular(12), border: Border.all(color: roleColor.withOpacity(0.3))),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getRoleIcon(roleEnum), size: 12, color: roleColor),
          const SizedBox(width: 4),
          Text(roleEnum.displayName, style: TextStyle(fontSize: 12, color: roleColor, fontWeight: FontWeight.w500)),
        ],
      ),
    );
  }

  Widget _buildRoleDropdown(BuildContext context, WidgetRef ref) {
    final updateUserRoleState = ref.watch(updateUserRoleProvider);
    final isUpdating = updateUserRoleState.isLoading && isLoading;

    return Container(
      decoration: BoxDecoration(color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5), borderRadius: BorderRadius.circular(8)),
      child: DropdownButton<UserRole>(
        value: UserRole.fromValue(user.role),
        onChanged:
            isUpdating
                ? null
                : (UserRole? newRole) {
                  if (newRole != null && newRole.value != user.role) {
                    _showRoleChangeDialog(context, ref, newRole);
                  }
                },
        underline: const SizedBox(),
        isDense: true,
        icon: isUpdating ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)) : const Icon(Icons.arrow_drop_down),
        items:
            UserRole.values.map((role) {
              return DropdownMenuItem<UserRole>(
                value: role,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [Icon(_getRoleIcon(role), size: 16, color: _getRoleColor(role)), const SizedBox(width: 8), Text(role.displayName, style: const TextStyle(fontSize: 14))],
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildAdditionalInfo(BuildContext context, DateFormat dateFormat) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3), borderRadius: BorderRadius.circular(8)),
      child: Row(
        children: [
          // Created date
          Expanded(
            child: Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Theme.of(context).colorScheme.onSurfaceVariant),
                const SizedBox(width: 6),
                Text(dateFormat.format(user.createdAt), style: Theme.of(context).textTheme.bodySmall),
              ],
            ),
          ),

          // Store ID (if exists)
          if (user.storeId != null) ...[
            Container(width: 1, height: 16, color: Theme.of(context).colorScheme.outline.withOpacity(0.3)),
            const SizedBox(width: 12),
            Expanded(
              child: Row(
                children: [
                  Icon(Icons.store, size: 16, color: Theme.of(context).colorScheme.onSurfaceVariant),
                  const SizedBox(width: 6),
                  Flexible(child: Text(user.storeId!, style: Theme.of(context).textTheme.bodySmall, overflow: TextOverflow.ellipsis)),
                ],
              ),
            ),
          ],

          // Tenant ID (if exists)
          if (user.tenantId != null) ...[
            Container(width: 1, height: 16, color: Theme.of(context).colorScheme.outline.withOpacity(0.3)),
            const SizedBox(width: 12),
            Expanded(
              child: Row(
                children: [
                  Icon(Icons.business, size: 16, color: Theme.of(context).colorScheme.onSurfaceVariant),
                  const SizedBox(width: 6),
                  Flexible(child: Text(user.tenantId!, style: Theme.of(context).textTheme.bodySmall, overflow: TextOverflow.ellipsis)),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showRoleChangeDialog(BuildContext context, WidgetRef ref, UserRole newRole) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('確認角色變更'),
            content: Text('將 "${user.displayName ?? user.email}" 的角色從 "${UserRole.fromValue(user.role).displayName}" 變更為 "${newRole.displayName}"？'),
            actions: [
              TextButton(onPressed: () => Navigator.pop(context), child: const Text('取消')),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ref.read(updateUserRoleProvider.notifier).updateUserRole(user.uid, newRole.value);
                },
                child: const Text('確認'),
              ),
            ],
          ),
    );
  }

  void _showUserDetails(BuildContext context) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                CircleAvatar(backgroundColor: _getRoleColor(UserRole.fromValue(user.role)), child: Text(user.email[0].toUpperCase(), style: const TextStyle(color: Colors.white))),
                const SizedBox(width: 12),
                Expanded(child: Text(user.displayName ?? user.email, style: const TextStyle(fontSize: 18))),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow('電子郵件', user.email, Icons.email),
                const SizedBox(height: 8),
                _buildDetailRow('角色', UserRole.fromValue(user.role).displayName, Icons.admin_panel_settings),
                const SizedBox(height: 8),
                _buildDetailRow('建立時間', dateFormat.format(user.createdAt), Icons.access_time),
                if (user.storeId != null) ...[const SizedBox(height: 8), _buildDetailRow('店舖ID', user.storeId!, Icons.store)],
                if (user.tenantId != null) ...[const SizedBox(height: 8), _buildDetailRow('租戶ID', user.tenantId!, Icons.business)],
              ],
            ),
            actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('關閉'))],
          ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Row(
      children: [Icon(icon, size: 16), const SizedBox(width: 8), Text('$label: '), Expanded(child: Text(value, style: const TextStyle(fontWeight: FontWeight.w500), overflow: TextOverflow.ellipsis))],
    );
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return Colors.red;
      case UserRole.tenant:
        return Colors.blue;
      case UserRole.cashier:
        return Colors.green;
      case UserRole.pendingApproval:
        return Colors.orange;
    }
  }

  IconData _getRoleIcon(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return Icons.security;
      case UserRole.tenant:
        return Icons.store;
      case UserRole.cashier:
        return Icons.point_of_sale;
      case UserRole.pendingApproval:
        return Icons.pending;
    }
  }
}
