import 'package:firebase_auth/firebase_auth.dart';

/// The interface for authentication remote data source
abstract class AuthRemoteDataSource {
  /// Signs in a user with email and password
  Future<User> signIn({required String email, required String password});

  /// Creates a new user with email and password
  Future<User> signUp({required String email, required String password});

  /// Creates a new user account without signing in as that user
  /// This is useful for admin operations
  Future<User> createUserWithoutSigningIn({required String email, required String password});

  /// Signs out the current user
  Future<void> signOut();

  /// Sends a password reset email
  Future<void> resetPassword({required String email});

  /// Gets the currently signed in user
  User? getCurrentUser();

  /// Stream of auth state changes
  Stream<User?> authStateChanges();
}

/// Implementation of [AuthRemoteDataSource] using Firebase Auth
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  /// Firebase auth instance
  final FirebaseAuth _firebaseAuth;

  /// Creates an [AuthRemoteDataSourceImpl] with the given [FirebaseAuth] instance
  AuthRemoteDataSourceImpl({FirebaseAuth? firebaseAuth}) : _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance;

  @override
  Future<User> signIn({required String email, required String password}) async {
    final userCredential = await _firebaseAuth.signInWithEmailAndPassword(email: email, password: password);

    return userCredential.user!;
  }

  @override
  Future<User> signUp({required String email, required String password}) async {
    final userCredential = await _firebaseAuth.createUserWithEmailAndPassword(email: email, password: password);

    return userCredential.user!;
  }

  @override
  Future<User> createUserWithoutSigningIn({required String email, required String password}) async {
    // Save current admin user credentials
    final adminUser = _firebaseAuth.currentUser;
    if (adminUser == null) {
      throw Exception('No user is currently signed in to perform this operation');
    }

    // Create a new auth instance to avoid affecting the current session
    final auth = FirebaseAuth.instance;

    try {
      // Sign out from the temporary instance (not affecting the main session)
      await auth.signOut();

      // Create the new user
      final userCredential = await auth.createUserWithEmailAndPassword(email: email, password: password);

      // Return the newly created user
      if (userCredential.user == null) {
        throw Exception('Failed to create new user');
      }

      return userCredential.user!;
    } finally {
      // Make sure to sign out from the temporary auth instance
      await auth.signOut();
    }
  }

  @override
  Future<void> signOut() async {
    await _firebaseAuth.signOut();
  }

  @override
  Future<void> resetPassword({required String email}) async {
    await _firebaseAuth.sendPasswordResetEmail(email: email);
  }

  @override
  User? getCurrentUser() {
    return _firebaseAuth.currentUser;
  }

  @override
  Stream<User?> authStateChanges() {
    return _firebaseAuth.authStateChanges();
  }
}
