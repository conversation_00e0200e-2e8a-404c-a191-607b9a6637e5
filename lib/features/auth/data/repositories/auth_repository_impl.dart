import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/auth/data/datasources/auth_remote_datasource.dart';
import 'package:grid_pos/features/auth/data/models/user_app_model_dto.dart';
import 'package:grid_pos/features/auth/domain/entities/user_app_model.dart';
import 'package:grid_pos/features/auth/domain/repositories/auth_repository.dart';

/// Implementation of [AuthRepository]
class AuthRepositoryImpl implements AuthRepository {
  /// Firebase auth data source
  final AuthRemoteDataSource _authRemoteDataSource;

  /// Firestore instance
  final FirebaseFirestore _firestore;

  /// Creates an [AuthRepositoryImpl] with the given dependencies
  AuthRepositoryImpl({required AuthRemoteDataSource authRemoteDataSource, FirebaseFirestore? firestore})
    : _authRemoteDataSource = authRemoteDataSource,
      _firestore = firestore ?? FirebaseFirestore.instance;

  @override
  Future<UserAppModel> signIn({required String email, required String password}) async {
    try {
      final user = await _authRemoteDataSource.signIn(email: email, password: password);

      // Fetch the user data from Firestore
      final userDoc = await _firestore.collection('users').doc(user.uid).get();

      if (userDoc.exists) {
        return UserAppModelDto.fromFirestore(userDoc);
      } else {
        // If user document doesn't exist, create a new one
        return _createUserDocument(user);
      }
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<UserAppModel> signUp({required String email, required String password}) async {
    try {
      final user = await _authRemoteDataSource.signUp(email: email, password: password);

      // Create a new user document in Firestore
      return _createUserDocument(user);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> signOut() async {
    await _authRemoteDataSource.signOut();
  }

  @override
  Future<void> resetPassword({required String email}) async {
    await _authRemoteDataSource.resetPassword(email: email);
  }

  @override
  UserAppModel? getCurrentUser() {
    final user = _authRemoteDataSource.getCurrentUser();
    if (user == null) return null;

    // Return empty user with UID until Firestore data is loaded
    return UserAppModel(uid: user.uid, email: user.email ?? '', role: 'pending_approval', createdAt: DateTime.now());
  }

  @override
  Stream<UserAppModel?> authStateChanges() {
    return _authRemoteDataSource.authStateChanges().asyncMap((user) async {
      if (user == null) return null;

      try {
        // Fetch user data from Firestore
        final userDoc = await _firestore.collection('users').doc(user.uid).get();

        if (userDoc.exists) {
          return UserAppModelDto.fromFirestore(userDoc);
        } else {
          // If user document doesn't exist, create a new one
          return _createUserDocument(user);
        }
      } catch (e) {
        // Log a detailed error message
        Logger.error('Failed to fetch user data from Firestore in authStateChanges', e, StackTrace.current);

        // Return basic user object with UID if Firestore fails
        return UserAppModel(uid: user.uid, email: user.email ?? '', role: 'pending_approval', createdAt: DateTime.now());
      }
    });
  }

  /// Creates a new user document in Firestore
  Future<UserAppModel> _createUserDocument(User user) async {
    final userModel = UserAppModel(
      uid: user.uid,
      email: user.email ?? '',
      role: 'pending_approval', // Default role for new users
      createdAt: DateTime.now(),
    );

    // Create the user document in Firestore
    await _firestore.collection('users').doc(user.uid).set(UserAppModelDto.toFirestore(userModel));

    return userModel;
  }
}
