import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:grid_pos/features/auth/domain/entities/user_app_model.dart';

/// Data Transfer Object for UserAppModel to handle conversions between
/// domain entity and Firestore document
class UserAppModelDto {
  /// Converts a Firestore document to a UserAppModel
  static UserAppModel fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data() ?? {};
    return UserAppModel(
      uid: doc.id,
      email: data['email'] as String? ?? '',
      role: data['role'] as String? ?? 'pending_approval',
      displayName: data['displayName'] as String?,
      tenantId: data['tenantId'] as String?,
      storeId: data['storeId'] as String?,
      createdAt: data['createdAt'] is Timestamp ? (data['createdAt'] as Timestamp).toDate() : DateTime.now(),
    );
  }

  /// Converts a UserAppModel to a Map for Firestore
  static Map<String, dynamic> toFirestore(UserAppModel model) {
    return {
      'email': model.email,
      'role': model.role,
      if (model.displayName != null) 'displayName': model.displayName,
      if (model.tenantId != null) 'tenantId': model.tenantId,
      if (model.storeId != null) 'storeId': model.storeId,
      'createdAt': Timestamp.fromDate(model.createdAt),
    };
  }
}
