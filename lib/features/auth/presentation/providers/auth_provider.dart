import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/auth/data/datasources/auth_remote_datasource.dart';
import 'package:grid_pos/features/auth/data/repositories/auth_repository_impl.dart';
import 'package:grid_pos/features/auth/domain/entities/user_app_model.dart';
import 'package:grid_pos/features/auth/domain/repositories/auth_repository.dart';

/// Provider for the auth remote data source
final authRemoteDataSourceProvider = Provider<AuthRemoteDataSource>((ref) {
  return AuthRemoteDataSourceImpl();
});

/// Provider for the auth repository
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final authRemoteDataSource = ref.watch(authRemoteDataSourceProvider);
  return AuthRepositoryImpl(authRemoteDataSource: authRemoteDataSource);
});

/// Provider for the auth state
final authStateProvider = StreamProvider<UserAppModel?>((ref) {
  return ref.watch(authRepositoryProvider).authStateChanges();
});

/// Provider for the current user
final currentUserProvider = Provider<UserAppModel?>((ref) {
  return ref
      .watch(authStateProvider)
      .when(data: (user) => user, loading: () => null, error: (_, __) => null);
});

/// Provider that indicates if the user is authenticated
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(currentUserProvider) != null;
});

/// Provider for user role
final userRoleProvider = Provider<String>((ref) {
  return ref.watch(currentUserProvider)?.role ?? 'pending_approval';
});

/// Auth state
class AuthState {
  /// Current user
  final UserAppModel? user;

  /// Is loading state
  final bool isLoading;

  /// Error message
  final String? errorMessage;

  /// Creates an auth state
  const AuthState({this.user, this.isLoading = false, this.errorMessage});

  /// Creates a copy of this state with the given fields replaced
  AuthState copyWith({
    UserAppModel? user,
    bool? isLoading,
    String? errorMessage,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }
}

/// Auth notifier that manages authentication state
class AuthNotifier extends StateNotifier<AuthState> {
  /// Auth repository
  final AuthRepository _authRepository;

  /// Creates an auth notifier
  AuthNotifier(this._authRepository) : super(const AuthState());

  /// Sign in with email and password
  Future<void> signIn({required String email, required String password}) async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);
      final user = await _authRepository.signIn(
        email: email,
        password: password,
      );
      state = state.copyWith(user: user, isLoading: false);
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: _mapFirebaseAuthErrorToMessage(e),
      );
      // Rethrow to allow UI to handle if needed
      rethrow;
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: '發生意外錯誤。請重試。');
      rethrow;
    }
  }

  /// Sign up with email and password
  Future<void> signUp({required String email, required String password}) async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);
      final user = await _authRepository.signUp(
        email: email,
        password: password,
      );
      state = state.copyWith(user: user, isLoading: false);
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: _mapFirebaseAuthErrorToMessage(e),
      );
      rethrow;
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: '發生意外錯誤。請重試。');
      rethrow;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);
      await _authRepository.signOut();
      state = state.copyWith(user: null, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: '退出登錄失敗。請重試。');
      rethrow;
    }
  }

  /// Reset password
  Future<void> resetPassword({required String email}) async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);
      await _authRepository.resetPassword(email: email);
      state = state.copyWith(isLoading: false);
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: _mapFirebaseAuthErrorToMessage(e),
      );
      rethrow;
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: '密碼重設失敗。請重試。');
      rethrow;
    }
  }

  /// Check if the user is authenticated
  bool get isAuthenticated => state.user != null;

  /// Maps Firebase Auth exceptions to user-friendly messages
  String _mapFirebaseAuthErrorToMessage(FirebaseAuthException exception) {
    switch (exception.code) {
      case 'user-not-found':
      case 'wrong-password':
        return '無效的電子郵件或密碼。請重試。';
      case 'email-already-in-use':
        return '此電子郵件地址已被使用。';
      case 'weak-password':
        return '密碼太弱。請使用更強的密碼。';
      case 'invalid-email':
        return '電子郵件地址無效。';
      case 'user-disabled':
        return '此帳戶已被禁用。請聯繫支援。';
      case 'operation-not-allowed':
        return '不允許此操作。請聯繫支援。';
      case 'too-many-requests':
        return '嘗試次數過多。請稍後再試。';
      default:
        return exception.message ?? '發生錯誤。請重試。';
    }
  }
}

/// Provider for the auth notifier
final authNotifierProvider = StateNotifierProvider<AuthNotifier, AuthState>((
  ref,
) {
  final authRepository = ref.watch(authRepositoryProvider);
  return AuthNotifier(authRepository);
});
