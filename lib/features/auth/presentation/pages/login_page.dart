import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/auth/presentation/pages/forgot_password_page.dart';
import 'package:grid_pos/features/auth/presentation/pages/signup_page.dart';
import 'package:grid_pos/features/auth/presentation/providers/auth_provider.dart';
import 'package:grid_pos/features/auth/presentation/widgets/auth_form.dart';
import 'package:grid_pos/shared/widgets/custom_snackbar.dart';

/// The login page that allows users to sign in with email and password
class LoginPage extends ConsumerWidget {
  /// Creates a login page
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<AuthState>(authNotifierProvider, (previous, current) {
      // Show error message if there is one
      if (current.errorMessage != null && previous?.errorMessage != current.errorMessage) {
        showErrorSnackBar(context, current.errorMessage!);
      }
    });

    return Scaffold(
      appBar: AppBar(title: const Text('登入')),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 40),

              // App logo or icon
              const Icon(Icons.grid_view_rounded, size: 80, color: Colors.blue),

              const SizedBox(height: 16),

              // App name
              const Text('格仔舖管理系統', textAlign: TextAlign.center, style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold)),

              const SizedBox(height: 48),

              // Login form
              AuthForm(
                formType: AuthFormType.login,
                onSubmit: (email, password) async {
                  try {
                    // Handle login with the auth provider
                    await ref.read(authNotifierProvider.notifier).signIn(email: email, password: password);
                  } on FirebaseAuthException catch (_) {
                    // Error is already handled by the listener above
                  } catch (e) {
                    if (context.mounted) {
                      showErrorSnackBar(context, '登入失敗: ${e.toString()}');
                    }
                  }
                },
                onForgotPassword: () {
                  // Navigate to forgot password page
                  Navigator.of(context).push(MaterialPageRoute(builder: (_) => const ForgotPasswordPage()));
                },
                onToggleForm: () {
                  // Navigate to signup page
                  Navigator.of(context).push(MaterialPageRoute(builder: (_) => const SignupPage()));
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
