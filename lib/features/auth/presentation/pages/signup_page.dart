import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/auth/presentation/providers/auth_provider.dart';
import 'package:grid_pos/features/auth/presentation/widgets/auth_form.dart';
import 'package:grid_pos/shared/widgets/custom_snackbar.dart';

/// The signup page that allows users to register with email and password
class SignupPage extends ConsumerWidget {
  /// Creates a signup page
  const SignupPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<AuthState>(authNotifierProvider, (previous, current) {
      // Show error message if there is one
      if (current.errorMessage != null && previous?.errorMessage != current.errorMessage) {
        showErrorSnackBar(context, current.errorMessage!);
      }
    });

    return Scaffold(
      appBar: AppBar(title: const Text('註冊')),
      body: <PERSON><PERSON><PERSON>(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 40),

              // App logo or icon
              const Icon(Icons.grid_view_rounded, size: 80, color: Colors.blue),

              const SizedBox(height: 16),

              // App name
              const Text('建立帳戶', textAlign: TextAlign.center, style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold)),

              const SizedBox(height: 48),

              // Signup form
              AuthForm(
                formType: AuthFormType.signup,
                onSubmit: (email, password) async {
                  try {
                    // Handle signup with the auth provider
                    await ref.read(authNotifierProvider.notifier).signUp(email: email, password: password);

                    if (context.mounted) {
                      showSuccessSnackBar(context, '帳戶建立成功。請登入。');
                      Navigator.of(context).pop(); // Return to login page after signup
                    }
                  } on FirebaseAuthException catch (_) {
                    // Error is already handled by the listener above
                  } catch (e) {
                    if (context.mounted) {
                      showErrorSnackBar(context, '註冊失敗: ${e.toString()}');
                    }
                  }
                },
                onToggleForm: () {
                  // Go back to login page
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
