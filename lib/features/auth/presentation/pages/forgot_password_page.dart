import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/auth/presentation/providers/auth_provider.dart';
import 'package:grid_pos/features/auth/presentation/widgets/auth_form.dart';
import 'package:grid_pos/shared/widgets/custom_snackbar.dart';

/// A page that allows users to reset their password
class ForgotPasswordPage extends ConsumerWidget {
  /// Creates a forgot password page
  const ForgotPasswordPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<AuthState>(authNotifierProvider, (previous, current) {
      // Show error message if there is one
      if (current.errorMessage != null && previous?.errorMessage != current.errorMessage) {
        showErrorSnackBar(context, current.errorMessage!);
      }
    });

    return Scaffold(
      appBar: AppBar(title: const Text('忘記密碼')),
      body: Safe<PERSON><PERSON>(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 40),

              // Icon
              const Icon(Icons.lock_reset_rounded, size: 80, color: Colors.blue),

              const SizedBox(height: 16),

              // Title
              const Text('重設密碼', textAlign: TextAlign.center, style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold)),

              const SizedBox(height: 16),

              // Description
              const Text('請輸入您的電子郵件地址，我們將向您發送重設密碼的連結。', textAlign: TextAlign.center, style: TextStyle(fontSize: 16)),

              const SizedBox(height: 48),

              // Forgot password form
              AuthForm(
                formType: AuthFormType.forgotPassword,
                onSubmit: (email, _) async {
                  try {
                    // Handle password reset
                    await ref.read(authNotifierProvider.notifier).resetPassword(email: email);

                    if (context.mounted) {
                      // Show success snackbar
                      showSuccessSnackBar(context, '密碼重設郵件已發送。請檢查您的收件箱。');

                      // Go back to login page
                      Navigator.of(context).pop();
                    }
                  } on FirebaseAuthException catch (_) {
                    // Error is already handled by the listener above
                  } catch (e) {
                    if (context.mounted) {
                      showErrorSnackBar(context, '密碼重設失敗: ${e.toString()}');
                    }
                  }
                },
                onToggleForm: () {
                  // Go back to login page
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
