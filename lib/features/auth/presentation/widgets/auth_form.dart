import 'package:flutter/material.dart';
import 'package:grid_pos/shared/widgets/app_button.dart';

/// Form type for authentication
enum AuthFormType {
  /// Login form
  login,

  /// Signup form
  signup,

  /// Forgot password form
  forgotPassword,
}

/// A reusable form widget for authentication screens
class AuthForm extends StatefulWidget {
  /// The type of form (login, signup, forgotPassword)
  final AuthFormType formType;

  /// Callback when form is submitted
  final Future<void> Function(String email, String password)? onSubmit;

  /// Callback when forgot password is pressed
  final VoidCallback? onForgotPassword;

  /// Callback when toggle between login and signup is pressed
  final VoidCallback? onToggleForm;

  /// Create an authentication form
  const AuthForm({super.key, required this.formType, this.onSubmit, this.onForgotPassword, this.onToggleForm});

  @override
  State<AuthForm> createState() => _AuthFormState();
}

class _AuthFormState extends State<AuthForm> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // Get form button text based on form type
  String get _submitButtonText {
    switch (widget.formType) {
      case AuthFormType.login:
        return '登入';
      case AuthFormType.signup:
        return '註冊';
      case AuthFormType.forgotPassword:
        return '重設密碼';
    }
  }

  // Get toggle button text based on form type
  String get _toggleButtonText {
    switch (widget.formType) {
      case AuthFormType.login:
        return '建立帳戶';
      case AuthFormType.signup:
        return '已有帳戶？登入';
      case AuthFormType.forgotPassword:
        return '返回登入';
    }
  }

  // Validate email format
  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return '電子郵件為必填項';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return '請輸入有效的電子郵件地址';
    }
    return null;
  }

  // Validate password (only for login and signup)
  String? _validatePassword(String? value) {
    if (widget.formType == AuthFormType.forgotPassword) {
      return null;
    }
    if (value == null || value.isEmpty) {
      return '密碼為必填項';
    }
    if (widget.formType == AuthFormType.signup && value.length < 6) {
      return '密碼長度至少為6個字元';
    }
    return null;
  }

  // Submit the form
  Future<void> _submitForm() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    final email = _emailController.text.trim();
    final password = _passwordController.text;

    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.formType == AuthFormType.forgotPassword) {
        // In case of forgot password, email is sent to the provided address
        await widget.onSubmit?.call(email, '');
      } else {
        await widget.onSubmit?.call(email, password);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Email field
          TextFormField(
            controller: _emailController,
            decoration: const InputDecoration(labelText: '電子郵件', prefixIcon: Icon(Icons.email_outlined)),
            keyboardType: TextInputType.emailAddress,
            validator: _validateEmail,
            textInputAction: widget.formType == AuthFormType.forgotPassword ? TextInputAction.done : TextInputAction.next,
          ),
          const SizedBox(height: 16),

          // Password field (not shown for forgot password)
          if (widget.formType != AuthFormType.forgotPassword) ...[
            TextFormField(
              controller: _passwordController,
              decoration: InputDecoration(
                labelText: '密碼',
                prefixIcon: const Icon(Icons.lock_outline),
                suffixIcon: IconButton(
                  icon: Icon(_obscurePassword ? Icons.visibility_outlined : Icons.visibility_off_outlined),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
              ),
              obscureText: _obscurePassword,
              validator: _validatePassword,
              textInputAction: TextInputAction.done,
            ),
            const SizedBox(height: 8),
          ],

          // Forgot password link (only for login)
          if (widget.formType == AuthFormType.login) ...[Align(alignment: Alignment.centerRight, child: TextButton(onPressed: widget.onForgotPassword, child: const Text('忘記密碼？')))],

          const SizedBox(height: 24),

          // Submit button
          AppButton(text: _submitButtonText, onPressed: _isLoading ? null : _submitForm, isLoading: _isLoading, width: double.infinity),

          const SizedBox(height: 16),

          // Toggle form button
          AppButton(text: _toggleButtonText, onPressed: widget.onToggleForm, variant: AppButtonVariant.text, width: double.infinity),
        ],
      ),
    );
  }
}
