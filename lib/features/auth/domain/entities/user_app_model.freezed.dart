// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_app_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserAppModel {

 String get uid; String get email; String get role; String? get displayName; String? get tenantId; String? get storeId;@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime get createdAt;
/// Create a copy of UserAppModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserAppModelCopyWith<UserAppModel> get copyWith => _$UserAppModelCopyWithImpl<UserAppModel>(this as UserAppModel, _$identity);

  /// Serializes this UserAppModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserAppModel&&(identical(other.uid, uid) || other.uid == uid)&&(identical(other.email, email) || other.email == email)&&(identical(other.role, role) || other.role == role)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.tenantId, tenantId) || other.tenantId == tenantId)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,uid,email,role,displayName,tenantId,storeId,createdAt);

@override
String toString() {
  return 'UserAppModel(uid: $uid, email: $email, role: $role, displayName: $displayName, tenantId: $tenantId, storeId: $storeId, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $UserAppModelCopyWith<$Res>  {
  factory $UserAppModelCopyWith(UserAppModel value, $Res Function(UserAppModel) _then) = _$UserAppModelCopyWithImpl;
@useResult
$Res call({
 String uid, String email, String role, String? displayName, String? tenantId, String? storeId,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime createdAt
});




}
/// @nodoc
class _$UserAppModelCopyWithImpl<$Res>
    implements $UserAppModelCopyWith<$Res> {
  _$UserAppModelCopyWithImpl(this._self, this._then);

  final UserAppModel _self;
  final $Res Function(UserAppModel) _then;

/// Create a copy of UserAppModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? uid = null,Object? email = null,Object? role = null,Object? displayName = freezed,Object? tenantId = freezed,Object? storeId = freezed,Object? createdAt = null,}) {
  return _then(_self.copyWith(
uid: null == uid ? _self.uid : uid // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,role: null == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as String,displayName: freezed == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String?,tenantId: freezed == tenantId ? _self.tenantId : tenantId // ignore: cast_nullable_to_non_nullable
as String?,storeId: freezed == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _UserAppModel implements UserAppModel {
  const _UserAppModel({required this.uid, required this.email, required this.role, this.displayName, this.tenantId, this.storeId, @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required this.createdAt});
  factory _UserAppModel.fromJson(Map<String, dynamic> json) => _$UserAppModelFromJson(json);

@override final  String uid;
@override final  String email;
@override final  String role;
@override final  String? displayName;
@override final  String? tenantId;
@override final  String? storeId;
@override@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) final  DateTime createdAt;

/// Create a copy of UserAppModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserAppModelCopyWith<_UserAppModel> get copyWith => __$UserAppModelCopyWithImpl<_UserAppModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserAppModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserAppModel&&(identical(other.uid, uid) || other.uid == uid)&&(identical(other.email, email) || other.email == email)&&(identical(other.role, role) || other.role == role)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.tenantId, tenantId) || other.tenantId == tenantId)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,uid,email,role,displayName,tenantId,storeId,createdAt);

@override
String toString() {
  return 'UserAppModel(uid: $uid, email: $email, role: $role, displayName: $displayName, tenantId: $tenantId, storeId: $storeId, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$UserAppModelCopyWith<$Res> implements $UserAppModelCopyWith<$Res> {
  factory _$UserAppModelCopyWith(_UserAppModel value, $Res Function(_UserAppModel) _then) = __$UserAppModelCopyWithImpl;
@override @useResult
$Res call({
 String uid, String email, String role, String? displayName, String? tenantId, String? storeId,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime createdAt
});




}
/// @nodoc
class __$UserAppModelCopyWithImpl<$Res>
    implements _$UserAppModelCopyWith<$Res> {
  __$UserAppModelCopyWithImpl(this._self, this._then);

  final _UserAppModel _self;
  final $Res Function(_UserAppModel) _then;

/// Create a copy of UserAppModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? uid = null,Object? email = null,Object? role = null,Object? displayName = freezed,Object? tenantId = freezed,Object? storeId = freezed,Object? createdAt = null,}) {
  return _then(_UserAppModel(
uid: null == uid ? _self.uid : uid // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,role: null == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as String,displayName: freezed == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String?,tenantId: freezed == tenantId ? _self.tenantId : tenantId // ignore: cast_nullable_to_non_nullable
as String?,storeId: freezed == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
