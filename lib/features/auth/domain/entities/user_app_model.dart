import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'user_app_model.freezed.dart';
part 'user_app_model.g.dart';

/// The application user model
@freezed
abstract class UserAppModel with _$UserAppModel {
  /// Factory constructor for creating a new [UserAppModel]
  const factory UserAppModel({
    required String uid,
    required String email,
    required String role,
    String? displayName,
    String? tenantId,
    String? storeId,
    @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required DateTime createdAt,
  }) = _UserAppModel;

  /// Creates an instance of [UserAppModel] from a Map.
  factory UserAppModel.fromJson(Map<String, dynamic> json) => _$UserAppModelFromJson(json);

  /// Empty user which represents an unauthenticated user
  factory UserAppModel.empty() => UserAppModel(uid: '', email: '', role: 'pending_approval', createdAt: DateTime.now());
}

/// Helper function to convert Timestamp to DateTime
DateTime _timestampFromJson(dynamic timestamp) {
  if (timestamp is Timestamp) {
    return timestamp.toDate();
  }
  return DateTime.now();
}

/// Helper function to convert DateTime to Timestamp
dynamic _timestampToJson(DateTime dateTime) {
  return Timestamp.fromDate(dateTime);
}
