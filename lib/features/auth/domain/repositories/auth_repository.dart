import 'package:grid_pos/features/auth/domain/entities/user_app_model.dart';

/// The interface for authentication repository
abstract class AuthRepository {
  /// Signs in a user with email and password
  Future<UserAppModel> signIn({required String email, required String password});

  /// Creates a new user with email and password
  Future<UserAppModel> signUp({required String email, required String password});

  /// Signs out the current user
  Future<void> signOut();

  /// Sends a password reset email
  Future<void> resetPassword({required String email});

  /// Gets the currently signed in user
  UserAppModel? getCurrentUser();

  /// Stream of auth state changes
  Stream<UserAppModel?> authStateChanges();
}
