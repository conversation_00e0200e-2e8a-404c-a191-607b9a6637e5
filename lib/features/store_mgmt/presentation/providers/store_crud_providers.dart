import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:grid_pos/features/store_mgmt/data/datasources/store_remote_ds.dart';
import 'package:grid_pos/features/store_mgmt/data/repositories/store_repository_impl.dart';
import 'package:grid_pos/features/store_mgmt/domain/entities/store_entity.dart';
import 'package:grid_pos/features/store_mgmt/domain/repositories/store_repository.dart';

part 'store_crud_providers.freezed.dart';

/// Provider for the Firestore instance
final firestoreProvider = Provider<FirebaseFirestore>((ref) {
  return FirebaseFirestore.instance;
});

/// Provider for StoreRemoteDataSource
final storeRemoteDataSourceProvider = Provider<StoreRemoteDataSource>((ref) {
  final firestore = ref.watch(firestoreProvider);
  return StoreRemoteDataSourceImpl(firestore: firestore);
});

/// Provider for StoreRepository
final storeRepositoryProvider = Provider<StoreRepository>((ref) {
  final dataSource = ref.watch(storeRemoteDataSourceProvider);
  final firestore = ref.watch(firestoreProvider);
  return StoreRepositoryImpl(remoteDataSource: dataSource, firestore: firestore);
});

/// Provider for available stores
final availableStoresProvider = StreamProvider<List<StoreEntity>>((ref) {
  final repository = ref.watch(storeRepositoryProvider);
  return repository.watchAllStores();
});

/// State class for store CRUD operations
@freezed
class StoreCrudState with _$StoreCrudState {
  const StoreCrudState._();

  /// Initial state
  const factory StoreCrudState.initial() = _Initial;

  /// Loading state
  const factory StoreCrudState.loading() = _Loading;

  /// Success state with optional success message
  const factory StoreCrudState.success([String? message]) = _Success;

  /// Error state with error message
  const factory StoreCrudState.error(String message) = _Error;
}

/// Notifier for store CRUD operations
class StoreCrudNotifier extends StateNotifier<StoreCrudState> {
  /// Store repository
  final StoreRepository _repository;
  final Ref _ref; // To access other providers if needed

  /// Constructor
  StoreCrudNotifier(this._repository, this._ref) : super(const StoreCrudState.initial());

  /// Create a new store
  Future<StoreEntity?> createStore(String name, String address, String timezone) async {
    state = const StoreCrudState.loading();
    try {
      final newStore = StoreEntity(
        id: '', // ID will be assigned by Firestore
        name: name,
        address: address,
        timezone: timezone,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final result = await _repository.createStore(newStore);
      state = const StoreCrudState.success('Store created successfully');
      _autoClearStateMessagesAfterDelay();
      return result;
    } catch (e) {
      state = StoreCrudState.error('Failed to create store: ${e.toString()}');
      _autoClearStateMessagesAfterDelay();
      return null;
    }
  }

  /// Update an existing store
  Future<bool> updateStore(StoreEntity store) async {
    state = const StoreCrudState.loading();
    try {
      await _repository.updateStore(store.copyWith(updatedAt: DateTime.now()));
      state = const StoreCrudState.success('Store updated successfully');
      _autoClearStateMessagesAfterDelay();
      return true;
    } catch (e) {
      state = StoreCrudState.error('Failed to update store: ${e.toString()}');
      _autoClearStateMessagesAfterDelay();
      return false;
    }
  }

  /// Delete a store
  Future<bool> deleteStore(String storeId) async {
    state = const StoreCrudState.loading();
    try {
      await _repository.deleteStore(storeId);
      state = const StoreCrudState.success('Store deleted successfully');
      _autoClearStateMessagesAfterDelay();
      return true;
    } catch (e) {
      state = StoreCrudState.error('Failed to delete store: ${e.toString()}');
      _autoClearStateMessagesAfterDelay();
      return false;
    }
  }

  void _autoClearStateMessagesAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        // Only reset state if current state is success or error
        if (state is _Success || state is _Error) {
          state = const StoreCrudState.initial();
        }
      }
    });
  }

  /// Reset state to initial
  void resetState() {
    state = const StoreCrudState.initial();
  }
}

/// Provider for StoreCrudNotifier
final storeCrudNotifierProvider = StateNotifierProvider<StoreCrudNotifier, StoreCrudState>((ref) {
  final repository = ref.watch(storeRepositoryProvider);
  return StoreCrudNotifier(repository, ref);
});
