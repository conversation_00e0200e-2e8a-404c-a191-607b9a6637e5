import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:grid_pos/core/constants/timezone_constants.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/store_mgmt/domain/entities/store_entity.dart';
import 'package:grid_pos/features/store_mgmt/presentation/providers/store_crud_providers.dart';
import 'package:grid_pos/shared/widgets/custom_snackbar.dart';

/// A form page for creating or editing a store with improved UI/UX
class StoreFormPage extends ConsumerStatefulWidget {
  /// The store to edit, null for creating a new store
  final StoreEntity? store;

  /// Creates a store form page
  const StoreFormPage({super.key, this.store});

  @override
  ConsumerState<StoreFormPage> createState() => _StoreFormPageState();
}

class _StoreFormPageState extends ConsumerState<StoreFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();

  String _selectedTimezone = 'Asia/Hong_Kong';

  bool get _isEditMode => widget.store != null;
  bool _isSubmitting = false;
  bool _isNavigatingAway = false;

  @override
  void initState() {
    super.initState();

    // If in edit mode, populate the form fields
    if (_isEditMode) {
      _nameController.text = widget.store!.name;
      _addressController.text = widget.store!.address;
      _selectedTimezone = widget.store!.timezone;

      // If the store's timezone is not in the available options, use the default
      if (!TimezoneConstants.commonTimezones.contains(_selectedTimezone)) {
        _selectedTimezone = TimezoneConstants.commonTimezones.first;
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  // Safe navigation method to prevent "nothing to pop" errors
  void _safeNavigateBack() {
    if (!_isNavigatingAway && mounted && context.mounted) {
      Logger.debug('Navigating back from StoreFormPage');
      setState(() {
        _isNavigatingAway = true;
      });

      try {
        if (Navigator.canPop(context)) {
          Navigator.of(context).pop();
        } else {
          context.go('/admin/stores');
        }
      } catch (e) {
        Logger.error('Navigation error: $e');
        context.go('/admin/stores');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Listen for store CRUD state changes
    ref.listen<StoreCrudState>(storeCrudNotifierProvider, (previous, current) {
      if (!mounted) return;

      if (current.toString().contains('success')) {
        final messageMatch = RegExp(r'message: ([^)]+)').firstMatch(current.toString());
        final message = messageMatch?.group(1);

        if (message != null && message != 'null' && context.mounted) {
          showSuccessSnackBar(context, message);
        }
      } else if (current.toString().contains('error')) {
        final messageMatch = RegExp(r'message: ([^)]+)').firstMatch(current.toString());
        final message = messageMatch?.group(1);

        if (message != null && context.mounted) {
          showErrorSnackBar(context, message);
        }
      }
    });

    final isLoading =
        ref.watch(storeCrudNotifierProvider).toString().contains('loading') || _isSubmitting;

    return PopScope(
      canPop: true,
      onPopInvoked: (didPop) {
        if (didPop) {
          setState(() {
            _isNavigatingAway = true;
          });
        }
      },
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black87,
          title: Text(
            _isEditMode ? '編輯商店' : '創建商店',
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
          leading: IconButton(icon: const Icon(Icons.arrow_back_ios), onPressed: _safeNavigateBack),
        ),
        body: Form(
          key: _formKey,
          child: Column(
            children: [
              // Progress indicator
              if (isLoading)
                const LinearProgressIndicator(
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
                ),

              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header section
                      _buildHeaderSection(context),
                      const SizedBox(height: 24),

                      // Basic Information Card
                      _buildBasicInfoCard(context, isLoading),
                      const SizedBox(height: 20),

                      // Location Information Card
                      _buildLocationInfoCard(context, isLoading),
                      const SizedBox(height: 20),

                      // Settings Card
                      _buildSettingsCard(context, isLoading),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),

              // Bottom action bar
              _buildBottomActionBar(context, isLoading),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange[600]!, Colors.orange[400]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(_isEditMode ? Icons.edit : Icons.storefront, color: Colors.white, size: 28),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _isEditMode ? '編輯商店資料' : '創建新商店',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _isEditMode ? '更新下方的商店詳細資料' : '填寫表單來創建新的商店',
                  style: TextStyle(color: Colors.white.withOpacity(0.9), fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoCard(BuildContext context, bool isLoading) {
    return _buildSectionCard(
      title: '基本資料',
      icon: Icons.business,
      child: Column(
        children: [
          _buildStyledTextField(
            controller: _nameController,
            label: '商店名稱',
            hint: '請輸入商店名稱',
            icon: Icons.store,
            enabled: !isLoading,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '商店名稱為必填項';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLocationInfoCard(BuildContext context, bool isLoading) {
    return _buildSectionCard(
      title: '位置資料',
      icon: Icons.location_on,
      child: Column(
        children: [
          _buildStyledTextField(
            controller: _addressController,
            label: '商店地址',
            hint: '請輸入商店完整地址',
            icon: Icons.location_city,
            enabled: !isLoading,
            maxLines: 1,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '商店地址為必填項';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsCard(BuildContext context, bool isLoading) {
    return _buildSectionCard(
      title: '系統設置',
      icon: Icons.settings,
      child: Column(
        children: [
          _buildStyledDropdown(
            label: '時區設置',
            hint: '選擇商店所在時區',
            icon: Icons.access_time,
            value: _selectedTimezone,
            items:
                TimezoneConstants.commonTimezones.map((timezone) {
                  return DropdownMenuItem<String>(
                    value: timezone,
                    child: Text(timezone, style: const TextStyle(fontSize: 14)),
                  );
                }).toList(),
            onChanged: (value) => setState(() => _selectedTimezone = value ?? 'Asia/Hong_Kong'),
          ),
          const SizedBox(height: 16),
          _buildInfoCard(),
        ],
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '時區說明',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.blue[800],
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '時區設置會影響商店的營業時間顯示和報表統計',
                  style: TextStyle(color: Colors.blue[700], fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({required String title, required IconData icon, required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: Colors.orange[700], size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
          Padding(padding: const EdgeInsets.all(20), child: child),
        ],
      ),
    );
  }

  Widget _buildStyledTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    required IconData icon,
    bool enabled = true,
    int? maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      enabled: enabled,
      maxLines: maxLines,
      validator: validator,
      style: const TextStyle(fontSize: 16),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.grey[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.orange[600]!, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: enabled ? Colors.grey[50] : Colors.grey[100],
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
    );
  }

  Widget _buildStyledDropdown({
    required String label,
    String? hint,
    required IconData icon,
    required String value,
    required List<DropdownMenuItem<String>> items,
    required Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.grey[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.orange[600]!, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey[50],
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
      value: value,
      items: items,
      onChanged: onChanged,
      isExpanded: true,
      menuMaxHeight: 300,
    );
  }

  Widget _buildBottomActionBar(BuildContext context, bool isLoading) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: isLoading ? null : _submitForm,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange[600],
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            child:
                isLoading
                    ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(_isEditMode ? Icons.update : Icons.add),
                        const SizedBox(width: 8),
                        Text(_isEditMode ? '更新商店' : '創建商店'),
                      ],
                    ),
          ),
        ),
      ),
    );
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      if (!mounted) return;

      setState(() {
        _isSubmitting = true;
      });

      final name = _nameController.text.trim();
      final address = _addressController.text.trim();

      try {
        bool success = false;

        if (_isEditMode) {
          final updatedStore = widget.store!.copyWith(
            name: name,
            address: address,
            timezone: _selectedTimezone,
          );
          success = await ref.read(storeCrudNotifierProvider.notifier).updateStore(updatedStore);
        } else {
          final result = await ref
              .read(storeCrudNotifierProvider.notifier)
              .createStore(name, address, _selectedTimezone);
          success = result != null;
        }

        if (success && mounted) {
          if (context.mounted) {
            showSuccessSnackBar(context, _isEditMode ? '商店更新成功' : '商店創建成功');
          }

          _safeNavigateBack();
        }
      } catch (e) {
        if (mounted && context.mounted) {
          showErrorSnackBar(context, '錯誤: ${e.toString()}');
        }
      } finally {
        if (mounted) {
          setState(() {
            _isSubmitting = false;
          });
        }
      }
    }
  }
}
