import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/store_mgmt/domain/entities/store_entity.dart';
import 'package:grid_pos/features/store_mgmt/presentation/providers/store_crud_providers.dart';
import 'package:grid_pos/shared/widgets/custom_snackbar.dart';
import 'package:intl/intl.dart';

/// A page that displays a list of stores for admin management with improved UI/UX
class StoreListPage extends ConsumerStatefulWidget {
  /// Creates a store list page
  const StoreListPage({super.key});

  @override
  ConsumerState<StoreListPage> createState() => _StoreListPageState();
}

class _StoreListPageState extends ConsumerState<StoreListPage> with TickerProviderStateMixin {
  late AnimationController _refreshController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _refreshController = AnimationController(duration: const Duration(milliseconds: 1000), vsync: this);
  }

  @override
  void dispose() {
    _refreshController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Listen for store CRUD state changes
    ref.listen<StoreCrudState>(storeCrudNotifierProvider, (previous, current) {
      _handleCrudStateChange(current, context, ref);
    });

    final storesAsyncValue = ref.watch(availableStoresProvider);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: _buildAppBar(theme),
      body: storesAsyncValue.when(
        data: (stores) => _buildScrollableContent(stores, theme),
        loading: () => const Center(child: Column(mainAxisSize: MainAxisSize.min, children: [CircularProgressIndicator(), SizedBox(height: 16), Text('載入商店資料中...')])),
        error:
            (error, stackTrace) => Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('載入商店時發生錯誤: $error'),
                  const SizedBox(height: 16),
                  ElevatedButton(onPressed: () => ref.refresh(availableStoresProvider), child: const Text('重試')),
                ],
              ),
            ),
      ),
      floatingActionButton: Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(16), boxShadow: [BoxShadow(color: theme.colorScheme.primary.withOpacity(0.3), blurRadius: 12, offset: const Offset(0, 4))]),
        child: FloatingActionButton.extended(
          onPressed: () => context.push('/admin/stores/new'),
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          elevation: 0,
          icon: const Icon(Icons.add),
          label: const Text('新增商店'),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    if (_isSearching) {
      return AppBar(
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        automaticallyImplyLeading: false,
        actions: [IconButton(icon: const Icon(Icons.close), onPressed: _exitSearchMode)],
        title: Container(
          decoration: BoxDecoration(color: theme.colorScheme.surfaceContainerHighest, borderRadius: BorderRadius.circular(12)),
          child: TextField(
            controller: _searchController,
            autofocus: true,
            decoration: InputDecoration(
              hintText: '搜尋商店名稱或地址...',
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(vertical: 12),
              hintStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant, fontSize: 16),
              prefixIcon: Icon(Icons.search, color: theme.colorScheme.onSurfaceVariant, size: 20),
              suffixIcon:
                  _searchQuery.isNotEmpty
                      ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: IconButton(
                          icon: Icon(Icons.clear, color: theme.colorScheme.onSurfaceVariant),
                          onPressed: () {
                            _searchController.clear();
                            setState(() => _searchQuery = '');
                          },
                        ),
                      )
                      : null,
            ),
            style: TextStyle(color: theme.colorScheme.onSurfaceVariant, fontSize: 16),
            onChanged: (value) => setState(() => _searchQuery = value.toLowerCase()),
          ),
        ),
      );
    }

    return AppBar(
      elevation: 0,
      backgroundColor: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      title: Text('商店管理', style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface)),
      leading: IconButton(icon: Icon(Icons.arrow_back_ios_new, color: theme.colorScheme.onSurface), onPressed: () => context.go('/')),
      actions: [
        IconButton(
          icon: Stack(
            children: [
              const Icon(Icons.search),
              if (_searchQuery.isNotEmpty)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(width: 8, height: 8, decoration: BoxDecoration(color: Colors.orange, shape: BoxShape.circle, border: Border.all(color: Colors.white, width: 1))),
                ),
            ],
          ),
          onPressed: _enterSearchMode,
        ),
      ],
    );
  }

  void _enterSearchMode() {
    setState(() => _isSearching = true);
  }

  void _exitSearchMode() {
    setState(() {
      _isSearching = false;
      _searchController.clear();
      _searchQuery = '';
    });
  }

  Widget _buildScrollableContent(List<StoreEntity> stores, ThemeData theme) {
    final filteredStores = _filterStores(stores);

    // 如果沒有商店，顯示空狀態
    if (filteredStores.isEmpty) {
      return RefreshIndicator(
        onRefresh: () async => ref.refresh(availableStoresProvider),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              Padding(padding: const EdgeInsets.all(16), child: _buildSearchAndStats(stores, theme)),
              SizedBox(height: MediaQuery.of(context).size.height * 0.2),
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(_searchQuery.isNotEmpty ? Icons.search_off : Icons.store_outlined, size: 64, color: Colors.grey),
                    const SizedBox(height: 16),
                    Text(_searchQuery.isNotEmpty ? '沒有找到符合條件的商店' : '還沒有商店', style: theme.textTheme.titleMedium),
                    if (_searchQuery.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          if (_isSearching) {
                            _exitSearchMode();
                          }
                        },
                        child: const Text('清除搜尋'),
                      ),
                    ] else ...[
                      const SizedBox(height: 8),
                      Text('開始建立您的第一個商店，\n開啟您的生意之旅', textAlign: TextAlign.center, style: theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.onSurfaceVariant, height: 1.5)),
                      const SizedBox(height: 32),
                      FilledButton.icon(
                        onPressed: () => context.push('/admin/stores/new'),
                        icon: const Icon(Icons.add),
                        label: const Text('建立第一個商店'),
                        style: FilledButton.styleFrom(padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12)),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    // 有商店時，使用統一的 ListView
    return RefreshIndicator(
      onRefresh: () async => ref.refresh(availableStoresProvider),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredStores.length + 1, // +1 for the header
        itemBuilder: (context, index) {
          // 第一個項目是搜索和統計區域
          if (index == 0) {
            return Padding(padding: const EdgeInsets.only(bottom: 16), child: _buildSearchAndStats(stores, theme));
          }

          // 其餘項目是商店列表
          final storeIndex = index - 1;
          final store = filteredStores[storeIndex];

          return AnimatedContainer(
            duration: Duration(milliseconds: 200 + (storeIndex * 50)),
            curve: Curves.easeOutBack,
            margin: const EdgeInsets.only(bottom: 16),
            child: _StoreListItem(store: store, index: storeIndex),
          );
        },
      ),
    );
  }

  Widget _buildSearchAndStats(List<StoreEntity> stores, ThemeData theme) {
    final filteredStores = _filterStores(stores);

    return Column(
      children: [
        // Search result indicator when searching
        if (_isSearching && _searchQuery.isNotEmpty) ...[
          Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: filteredStores.isEmpty ? Colors.orange.withOpacity(0.1) : Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: filteredStores.isEmpty ? Colors.orange.withOpacity(0.3) : Colors.green.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(filteredStores.isEmpty ? Icons.search_off : Icons.check_circle_outline, color: filteredStores.isEmpty ? Colors.orange : Colors.green, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    filteredStores.isEmpty ? '沒有找到符合 "$_searchQuery" 的商店' : '找到 ${filteredStores.length} 個符合 "$_searchQuery" 的商店',
                    style: TextStyle(color: filteredStores.isEmpty ? Colors.orange.shade700 : Colors.green.shade700, fontWeight: FontWeight.w500),
                  ),
                ),
                if (filteredStores.isNotEmpty) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(color: Colors.green, borderRadius: BorderRadius.circular(12)),
                    child: Text('${filteredStores.length}', style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12)),
                  ),
                ],
              ],
            ),
          ),
        ],
        // Stats Card
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    const Icon(Icons.store, size: 24),
                    const SizedBox(width: 8),
                    Text('商店總數: ${stores.length}', style: theme.textTheme.titleMedium),
                    if (filteredStores.length != stores.length) ...[const Spacer(), Text('顯示: ${filteredStores.length}')],
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  List<StoreEntity> _filterStores(List<StoreEntity> stores) {
    if (_searchQuery.isEmpty) return stores;

    return stores.where((store) {
      return store.name.toLowerCase().contains(_searchQuery) || store.address.toLowerCase().contains(_searchQuery);
    }).toList();
  }

  void _handleCrudStateChange(StoreCrudState current, BuildContext context, WidgetRef ref) {
    final stateString = current.toString();

    if (stateString.startsWith('StoreCrudState.success')) {
      final messageMatch = RegExp(r'message: ([^),]+)').firstMatch(stateString);
      final message = messageMatch?.group(1);

      if (message != null && message != 'null' && context.mounted) {
        showSuccessSnackBar(context, message);
      }
      ref.invalidate(availableStoresProvider);
    } else if (stateString.startsWith('StoreCrudState.error')) {
      final messageMatch = RegExp(r'message: ([^),]+)').firstMatch(stateString);
      final message = messageMatch?.group(1);

      if (message != null && context.mounted) {
        showErrorSnackBar(context, message);
      }
    }
  }
}

/// A list item widget for a store with improved design
class _StoreListItem extends ConsumerWidget {
  final StoreEntity store;
  final int index;

  const _StoreListItem({required this.store, required this.index});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('yyyy-MM-dd');
    final deleteInProgress = ref.watch(storeCrudNotifierProvider).toString().contains('loading');

    return Card(
      elevation: 0,
      color: theme.colorScheme.surfaceContainerLow,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16), side: BorderSide(color: theme.colorScheme.outline.withOpacity(0.1))),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () {
          context.push('/admin/stores/${store.id}/edit', extra: store);
        },
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with store name and actions
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(color: theme.colorScheme.primaryContainer, borderRadius: BorderRadius.circular(12)),
                    child: Icon(Icons.store, size: 24, color: theme.colorScheme.onPrimaryContainer),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [Text(store.name, style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: theme.colorScheme.onSurface), overflow: TextOverflow.ellipsis)],
                    ),
                  ),
                  // Action buttons
                  _buildActionButtons(context, ref, theme, deleteInProgress),
                ],
              ),

              const SizedBox(height: 20),

              // Store information
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.5), borderRadius: BorderRadius.circular(12)),
                child: Column(
                  children: [
                    _buildInfoRow(context, Icons.location_on_outlined, '地址', store.address, theme),
                    const SizedBox(height: 12),
                    _buildInfoRow(context, Icons.access_time, '時區', store.timezone, theme),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(child: _buildInfoRow(context, Icons.calendar_today, '建立', dateFormat.format(store.createdAt), theme)),
                        const SizedBox(width: 16),
                        Expanded(child: _buildInfoRow(context, Icons.update, '更新', dateFormat.format(store.updatedAt), theme)),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, WidgetRef ref, ThemeData theme, bool deleteInProgress) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          decoration: BoxDecoration(color: theme.colorScheme.primaryContainer.withOpacity(0.5), borderRadius: BorderRadius.circular(8)),
          child: IconButton(
            icon: Icon(Icons.edit_outlined, color: theme.colorScheme.primary),
            tooltip: '編輯商店',
            onPressed: () {
              context.push('/admin/stores/${store.id}/edit', extra: store);
            },
          ),
        ),
        const SizedBox(width: 8),
        Container(
          decoration: BoxDecoration(color: theme.colorScheme.errorContainer.withOpacity(0.5), borderRadius: BorderRadius.circular(8)),
          child: IconButton(
            icon:
                deleteInProgress
                    ? SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2, color: theme.colorScheme.error))
                    : Icon(Icons.delete_outline, color: theme.colorScheme.error),
            tooltip: '刪除商店',
            onPressed:
                deleteInProgress
                    ? null
                    : () {
                      _showDeleteConfirmationDialog(context, ref);
                    },
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(BuildContext context, IconData icon, String label, String value, ThemeData theme) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 18, color: theme.colorScheme.onSurfaceVariant),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(label, style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant, fontWeight: FontWeight.w500)),
              const SizedBox(height: 2),
              Text(value, style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurface)),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _showDeleteConfirmationDialog(BuildContext context, WidgetRef ref) async {
    final theme = Theme.of(context);

    // Show a modern loading dialog
    bool isLoadingDialogMounted = true;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title: Row(children: [Icon(Icons.search, color: theme.colorScheme.primary), const SizedBox(width: 12), const Text('檢查商店數據')]),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 16),
              CircularProgressIndicator(color: theme.colorScheme.primary),
              const SizedBox(height: 24),
              Text('正在檢查關聯數據...', style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
            ],
          ),
        );
      },
    ).then((_) => isLoadingDialogMounted = false);

    final repository = ref.read(storeRepositoryProvider);
    bool hasAssociatedData = false;
    Map<String, int> counts = {};

    try {
      hasAssociatedData = await repository.hasAssociatedData(store.id);
      if (hasAssociatedData) {
        counts = await repository.getAssociatedDataCounts(store.id);
      }
    } catch (e, stackTrace) {
      Logger.error('Error checking associated data for store deletion', e, stackTrace);
      if (context.mounted) {
        if (isLoadingDialogMounted && Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
          isLoadingDialogMounted = false;
        }
        showErrorSnackBar(context, '檢查商店數據時出錯: ${e.toString()}');
      }
      return;
    }

    // Close the loading dialog
    if (isLoadingDialogMounted && context.mounted && Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
      isLoadingDialogMounted = false;
    }

    if (hasAssociatedData && context.mounted) {
      _showCannotDeleteDialog(context, counts, theme);
    } else if (context.mounted) {
      _showConfirmDeleteDialog(context, ref, theme);
    }
  }

  void _showCannotDeleteDialog(BuildContext context, Map<String, int> counts, ThemeData theme) {
    List<String> details = [];
    if (counts.containsKey('tenants') && counts['tenants']! > 0) {
      details.add('${counts['tenants']} 個租戶');
    }
    if (counts.containsKey('grids') && counts['grids']! > 0) {
      details.add('${counts['grids']} 個格位');
    }
    if (counts.containsKey('sales') && counts['sales']! > 0) {
      details.add('${counts['sales']} 個銷售記錄');
    }

    String detailsText = details.isEmpty ? "未知關聯數據（獲取詳細信息時出錯）" : details.join('、');

    showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title: Row(children: [Icon(Icons.warning_amber, color: theme.colorScheme.error), const SizedBox(width: 12), const Text('無法刪除商店')]),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('商店 "${store.name}" 還有必須先移除的關聯數據:', style: theme.textTheme.bodyMedium),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(color: theme.colorScheme.errorContainer.withOpacity(0.3), borderRadius: BorderRadius.circular(12)),
                  child: Text(detailsText, style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold, color: theme.colorScheme.onErrorContainer)),
                ),
                const SizedBox(height: 16),
                Text('請先移除所有關聯數據，然後再刪除此商店。', style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
              ],
            ),
          ),
          actions: [FilledButton(onPressed: () => Navigator.of(context).pop(), child: const Text('我了解了'))],
        );
      },
    );
  }

  void _showConfirmDeleteDialog(BuildContext context, WidgetRef ref, ThemeData theme) {
    showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title: Row(children: [Icon(Icons.delete_forever, color: theme.colorScheme.error), const SizedBox(width: 12), const Text('刪除商店')]),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('確定要刪除商店 "${store.name}" 嗎？', style: theme.textTheme.bodyLarge),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(color: theme.colorScheme.errorContainer.withOpacity(0.3), borderRadius: BorderRadius.circular(8)),
                child: Row(
                  children: [
                    Icon(Icons.warning, size: 20, color: theme.colorScheme.error),
                    const SizedBox(width: 8),
                    const Expanded(child: Text('此操作無法撤銷', style: TextStyle(fontWeight: FontWeight.bold))),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('取消')),
            FilledButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await ref.read(storeCrudNotifierProvider.notifier).deleteStore(store.id);
              },
              style: FilledButton.styleFrom(backgroundColor: theme.colorScheme.error, foregroundColor: theme.colorScheme.onError),
              child: const Text('刪除'),
            ),
          ],
        );
      },
    );
  }
}
