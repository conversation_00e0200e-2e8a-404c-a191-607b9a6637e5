import 'package:grid_pos/features/store_mgmt/domain/entities/store_entity.dart';

/// Repository interface for store management operations
abstract class StoreRepository {
  /// Create a new store
  Future<StoreEntity> createStore(StoreEntity store);

  /// Get a stream of all stores
  Stream<List<StoreEntity>> watchAllStores();

  /// Get a specific store by ID
  Future<StoreEntity?> getStore(String storeId);

  /// Watch a specific store by ID
  Stream<StoreEntity?> watchStore(String storeId);

  /// Update an existing store
  Future<void> updateStore(StoreEntity store);

  /// Delete a store by ID
  /// Throws an exception if the store has associated data
  Future<void> deleteStore(String storeId);

  /// Check if a store has associated data (tenants, grids, etc.)
  /// Returns true if the store has any associated data
  Future<bool> hasAssociatedData(String storeId);

  /// Get counts of associated data for a store
  /// Returns a map with counts of tenants, grids, and sales
  Future<Map<String, int>> getAssociatedDataCounts(String storeId);
}
