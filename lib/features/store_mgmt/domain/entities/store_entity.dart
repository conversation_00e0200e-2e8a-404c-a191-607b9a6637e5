import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'store_entity.freezed.dart';
part 'store_entity.g.dart';

/// Store entity representing a physical store location
@freezed
abstract class StoreEntity with _$StoreEntity {
  /// Factory constructor for creating a new [StoreEntity]
  const factory StoreEntity({
    required String id,
    required String name,
    required String address,
    @Default('Asia/Taipei') String timezone,
    @Default(0) int gridCount,
    @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required DateTime createdAt,
    @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required DateTime updatedAt,
  }) = _StoreEntity;

  /// Creates an instance of [StoreEntity] from a Map.
  factory StoreEntity.fromJson(Map<String, dynamic> json) => _$StoreEntityFromJson(json);

  /// Empty store which represents a non-existent store
  factory StoreEntity.empty() => StoreEntity(id: '', name: '', address: '', createdAt: DateTime.now(), updatedAt: DateTime.now());
}

/// Helper function to convert Timestamp to DateTime
DateTime _timestampFromJson(dynamic timestamp) {
  if (timestamp is Timestamp) {
    return timestamp.toDate();
  }
  return DateTime.now();
}

/// Helper function to convert DateTime to Timestamp
dynamic _timestampToJson(DateTime dateTime) {
  return Timestamp.fromDate(dateTime);
}
