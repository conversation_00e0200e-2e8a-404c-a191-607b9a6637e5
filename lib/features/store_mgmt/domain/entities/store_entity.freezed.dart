// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'store_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$StoreEntity {

 String get id; String get name; String get address; String get timezone; int get gridCount;@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime get createdAt;@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime get updatedAt;
/// Create a copy of StoreEntity
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StoreEntityCopyWith<StoreEntity> get copyWith => _$StoreEntityCopyWithImpl<StoreEntity>(this as StoreEntity, _$identity);

  /// Serializes this StoreEntity to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StoreEntity&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.address, address) || other.address == address)&&(identical(other.timezone, timezone) || other.timezone == timezone)&&(identical(other.gridCount, gridCount) || other.gridCount == gridCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,address,timezone,gridCount,createdAt,updatedAt);

@override
String toString() {
  return 'StoreEntity(id: $id, name: $name, address: $address, timezone: $timezone, gridCount: $gridCount, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $StoreEntityCopyWith<$Res>  {
  factory $StoreEntityCopyWith(StoreEntity value, $Res Function(StoreEntity) _then) = _$StoreEntityCopyWithImpl;
@useResult
$Res call({
 String id, String name, String address, String timezone, int gridCount,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime createdAt,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime updatedAt
});




}
/// @nodoc
class _$StoreEntityCopyWithImpl<$Res>
    implements $StoreEntityCopyWith<$Res> {
  _$StoreEntityCopyWithImpl(this._self, this._then);

  final StoreEntity _self;
  final $Res Function(StoreEntity) _then;

/// Create a copy of StoreEntity
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? address = null,Object? timezone = null,Object? gridCount = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,timezone: null == timezone ? _self.timezone : timezone // ignore: cast_nullable_to_non_nullable
as String,gridCount: null == gridCount ? _self.gridCount : gridCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _StoreEntity implements StoreEntity {
  const _StoreEntity({required this.id, required this.name, required this.address, this.timezone = 'Asia/Taipei', this.gridCount = 0, @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required this.createdAt, @JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) required this.updatedAt});
  factory _StoreEntity.fromJson(Map<String, dynamic> json) => _$StoreEntityFromJson(json);

@override final  String id;
@override final  String name;
@override final  String address;
@override@JsonKey() final  String timezone;
@override@JsonKey() final  int gridCount;
@override@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) final  DateTime createdAt;
@override@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) final  DateTime updatedAt;

/// Create a copy of StoreEntity
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StoreEntityCopyWith<_StoreEntity> get copyWith => __$StoreEntityCopyWithImpl<_StoreEntity>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StoreEntityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StoreEntity&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.address, address) || other.address == address)&&(identical(other.timezone, timezone) || other.timezone == timezone)&&(identical(other.gridCount, gridCount) || other.gridCount == gridCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,address,timezone,gridCount,createdAt,updatedAt);

@override
String toString() {
  return 'StoreEntity(id: $id, name: $name, address: $address, timezone: $timezone, gridCount: $gridCount, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$StoreEntityCopyWith<$Res> implements $StoreEntityCopyWith<$Res> {
  factory _$StoreEntityCopyWith(_StoreEntity value, $Res Function(_StoreEntity) _then) = __$StoreEntityCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String address, String timezone, int gridCount,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime createdAt,@JsonKey(toJson: _timestampToJson, fromJson: _timestampFromJson) DateTime updatedAt
});




}
/// @nodoc
class __$StoreEntityCopyWithImpl<$Res>
    implements _$StoreEntityCopyWith<$Res> {
  __$StoreEntityCopyWithImpl(this._self, this._then);

  final _StoreEntity _self;
  final $Res Function(_StoreEntity) _then;

/// Create a copy of StoreEntity
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? address = null,Object? timezone = null,Object? gridCount = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_StoreEntity(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,timezone: null == timezone ? _self.timezone : timezone // ignore: cast_nullable_to_non_nullable
as String,gridCount: null == gridCount ? _self.gridCount : gridCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
