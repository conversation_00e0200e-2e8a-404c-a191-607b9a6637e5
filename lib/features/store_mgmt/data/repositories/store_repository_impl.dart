import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/store_mgmt/data/datasources/store_remote_ds.dart';
import 'package:grid_pos/features/store_mgmt/domain/entities/store_entity.dart';
import 'package:grid_pos/features/store_mgmt/domain/repositories/store_repository.dart';

/// Implementation of [StoreRepository]
class StoreRepositoryImpl implements StoreRepository {
  /// Data source for store operations
  final StoreRemoteDataSource _remoteDataSource;

  /// Firestore instance for checking associated data
  final FirebaseFirestore _firestore;

  /// Constructor
  StoreRepositoryImpl({
    required StoreRemoteDataSource remoteDataSource,
    FirebaseFirestore? firestore,
  }) : _remoteDataSource = remoteDataSource,
       _firestore = firestore ?? FirebaseFirestore.instance;

  @override
  Future<StoreEntity> createStore(StoreEntity store) async {
    try {
      return await _remoteDataSource.createStore(store);
    } catch (e, stackTrace) {
      Logger.error('Failed to create store', e, stackTrace);
      rethrow;
    }
  }

  @override
  Stream<List<StoreEntity>> watchAllStores() {
    try {
      return _remoteDataSource.watchStores();
    } catch (e, stackTrace) {
      Logger.error('Failed to watch stores', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<StoreEntity?> getStore(String storeId) async {
    try {
      return await _remoteDataSource.getStore(storeId);
    } catch (e, stackTrace) {
      Logger.error('Failed to get store with ID: $storeId', e, stackTrace);
      rethrow;
    }
  }

  @override
  Stream<StoreEntity?> watchStore(String storeId) {
    try {
      return _remoteDataSource.watchStore(storeId);
    } catch (e, stackTrace) {
      Logger.error('Failed to watch store with ID: $storeId', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> updateStore(StoreEntity store) async {
    try {
      await _remoteDataSource.updateStore(store);
    } catch (e, stackTrace) {
      Logger.error(
        'Failed to update store with ID: ${store.id}',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> deleteStore(String storeId) async {
    try {
      // First check if the store has any associated data
      final hasData = await hasAssociatedData(storeId);

      if (hasData) {
        // Get detailed counts of associated data
        final counts = await getAssociatedDataCounts(storeId);

        final List<String> details = [];
        if (counts['tenants']! > 0) {
          details.add('${counts['tenants']} tenant(s)');
        }
        if (counts['grids']! > 0) {
          details.add('${counts['grids']} grid(s)');
        }
        if (counts['sales']! > 0) {
          details.add('${counts['sales']} sales record(s)');
        }

        final detailsText = details.join(', ');
        throw Exception(
          'Cannot delete store with associated data: $detailsText. Please remove all associated data first.',
        );
      }

      await _remoteDataSource.deleteStore(storeId);
    } catch (e, stackTrace) {
      Logger.error('Failed to delete store with ID: $storeId', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<bool> hasAssociatedData(String storeId) async {
    try {
      // Check if there are any tenants associated with this store
      final tenantsQuery =
          await _firestore
              .collection('stores')
              .doc(storeId)
              .collection('tenants')
              .limit(1)
              .get();

      if (tenantsQuery.docs.isNotEmpty) {
        return true;
      }

      // Check if there are any grids associated with this store
      final gridsQuery =
          await _firestore
              .collection('stores')
              .doc(storeId)
              .collection('grids')
              .limit(1)
              .get();

      return gridsQuery.docs.isNotEmpty;
    } catch (e, stackTrace) {
      Logger.error(
        'Failed to check for associated data for store ID: $storeId',
        e,
        stackTrace,
      );
      // In case of error, assume there's associated data to prevent accidental deletion
      return true;
    }
  }

  /// Gets a detailed report of associated data for a store
  @override
  Future<Map<String, int>> getAssociatedDataCounts(String storeId) async {
    final result = <String, int>{'tenants': 0, 'grids': 0, 'sales': 0};

    try {
      // Count tenants
      final tenantsQuery =
          await _firestore
              .collection('stores')
              .doc(storeId)
              .collection('tenants')
              .count()
              .get();
      result['tenants'] = tenantsQuery.count ?? 0;

      // Count grids
      final gridsQuery =
          await _firestore
              .collection('stores')
              .doc(storeId)
              .collection('grids')
              .count()
              .get();
      result['grids'] = gridsQuery.count ?? 0;

      // Count sales
      final salesQuery =
          await _firestore
              .collection('stores')
              .doc(storeId)
              .collection('sales')
              .count()
              .get();
      result['sales'] = salesQuery.count ?? 0;

      return result;
    } catch (e, stackTrace) {
      Logger.error(
        'Failed to get associated data counts for store ID: $storeId',
        e,
        stackTrace,
      );
      return result; // Return the default map with zeros
    }
  }
}
