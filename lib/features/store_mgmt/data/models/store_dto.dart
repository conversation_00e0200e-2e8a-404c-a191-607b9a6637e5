import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:grid_pos/features/store_mgmt/domain/entities/store_entity.dart';

/// Data Transfer Object for StoreEntity to handle conversions between
/// domain entity and Firestore document
class StoreDto {
  /// Converts a Firestore document to a StoreEntity
  static StoreEntity fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data() ?? {};
    return StoreEntity(
      id: doc.id,
      name: data['name'] as String? ?? '',
      address: data['address'] as String? ?? '',
      timezone: data['timezone'] as String? ?? 'Asia/Taipei',
      gridCount: data['gridCount'] as int? ?? 0,
      createdAt: data['createdAt'] is Timestamp ? (data['createdAt'] as Timestamp).toDate() : DateTime.now(),
      updatedAt: data['updatedAt'] is Timestamp ? (data['updatedAt'] as Timestamp).toDate() : DateTime.now(),
    );
  }

  /// Converts a StoreEntity to a Map for Firestore
  static Map<String, dynamic> toFirestore(StoreEntity store, {bool newStore = false}) {
    final map = <String, dynamic>{'name': store.name, 'address': store.address, 'timezone': store.timezone, 'gridCount': store.gridCount, 'updatedAt': Timestamp.fromDate(DateTime.now())};

    // Only add createdAt for new stores
    if (newStore) {
      map['createdAt'] = Timestamp.fromDate(DateTime.now());
    }

    return map;
  }
}
