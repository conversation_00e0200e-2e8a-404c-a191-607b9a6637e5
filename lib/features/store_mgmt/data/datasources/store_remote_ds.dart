import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:grid_pos/features/store_mgmt/data/models/store_dto.dart';
import 'package:grid_pos/features/store_mgmt/domain/entities/store_entity.dart';

/// Interface for Store remote data source
abstract class StoreRemoteDataSource {
  /// Create a new store in Firestore
  Future<StoreEntity> createStore(StoreEntity store);

  /// Get a stream of all stores
  Stream<List<StoreEntity>> watchStores();

  /// Get a specific store by ID
  Future<StoreEntity?> getStore(String storeId);

  /// Watch a specific store by ID
  Stream<StoreEntity?> watchStore(String storeId);

  /// Update an existing store
  Future<void> updateStore(StoreEntity store);

  /// Delete a store by ID
  Future<void> deleteStore(String storeId);
}

/// Implementation of [StoreRemoteDataSource] using Firebase Firestore
class StoreRemoteDataSourceImpl implements StoreRemoteDataSource {
  /// Firebase Firestore instance
  final FirebaseFirestore _firestore;

  /// Collection reference for stores
  late final CollectionReference<Map<String, dynamic>> _storesCollection;

  /// Constructor
  StoreRemoteDataSourceImpl({FirebaseFirestore? firestore})
    : _firestore = firestore ?? FirebaseFirestore.instance {
    _storesCollection = _firestore.collection('stores');
  }

  @override
  Future<StoreEntity> createStore(StoreEntity store) async {
    // Create a document with auto-generated ID
    final docRef = _storesCollection.doc();

    // Prepare store data for Firestore
    final storeData = StoreDto.toFirestore(store, newStore: true);

    // Write to Firestore
    await docRef.set(storeData);

    // Return the created store with the generated ID
    return store.copyWith(id: docRef.id);
  }

  @override
  Stream<List<StoreEntity>> watchStores() {
    return _storesCollection.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) => StoreDto.fromFirestore(doc)).toList();
    });
  }

  @override
  Future<StoreEntity?> getStore(String storeId) async {
    final docSnapshot = await _storesCollection.doc(storeId).get();

    if (!docSnapshot.exists) {
      return null;
    }

    return StoreDto.fromFirestore(docSnapshot);
  }

  @override
  Stream<StoreEntity?> watchStore(String storeId) {
    return _storesCollection.doc(storeId).snapshots().map((docSnapshot) {
      if (!docSnapshot.exists) {
        return null;
      }
      return StoreDto.fromFirestore(docSnapshot);
    });
  }

  @override
  Future<void> updateStore(StoreEntity store) async {
    // Ensure store ID is valid
    if (store.id.isEmpty) {
      throw Exception('Cannot update store with empty ID');
    }

    // Prepare store data for Firestore
    final storeData = StoreDto.toFirestore(store);

    // Update the store document
    await _storesCollection.doc(store.id).update(storeData);
  }

  @override
  Future<void> deleteStore(String storeId) async {
    // Ensure store ID is valid
    if (storeId.isEmpty) {
      throw Exception('Cannot delete store with empty ID');
    }

    // Delete the store document
    await _storesCollection.doc(storeId).delete();
  }
}
