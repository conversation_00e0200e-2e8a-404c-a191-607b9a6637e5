import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/utils/logger.dart';
import '../models/daily_summary_dto.dart';
import '../../domain/entities/daily_summary_entity.dart';

/// 每日摘要遠程數據源接口
abstract class DailySummaryRemoteDataSource {
  /// 獲取指定店鋪和日期的每日摘要
  Future<DailySummaryDto?> getDailySummary(String storeId, DateTime date);

  /// 監聽指定店鋪和日期的每日摘要
  Stream<DailySummaryDto?> watchDailySummary(String storeId, DateTime date);

  /// 獲取指定店鋪的每日摘要列表
  Future<List<DailySummaryDto>> getDailySummaries(
    String storeId, {
    DateTime? startDate,
    DateTime? endDate,
    int limit = 30,
  });

  /// 監聽指定店鋪的每日摘要列表
  Stream<List<DailySummaryDto>> watchDailySummaries(
    String storeId, {
    DateTime? startDate,
    DateTime? endDate,
    int limit = 30,
  });

  /// 創建或更新每日摘要
  Future<String> createOrUpdateDailySummary(DailySummaryDto summary);

  /// 刪除指定店鋪和日期的每日摘要
  Future<void> deleteDailySummary(String storeId, DateTime date);

  /// 檢查指定店鋪和日期的每日摘要是否存在
  Future<bool> dailySummaryExists(String storeId, DateTime date);
}

/// 每日摘要遠程數據源實現
class DailySummaryRemoteDataSourceImpl implements DailySummaryRemoteDataSource {
  final FirebaseFirestore _firestore;

  DailySummaryRemoteDataSourceImpl(this._firestore);

  /// 獲取每日摘要集合引用
  CollectionReference<Map<String, dynamic>> get _dailySummariesRef =>
      _firestore.collection('daily_summaries');

  @override
  Future<DailySummaryDto?> getDailySummary(String storeId, DateTime date) async {
    try {
      final docId = DailySummaryEntity.generateDocumentId(date, storeId);
      Logger.debug('[DailySummaryRemoteDS] Getting daily summary for docId: $docId');

      final doc = await _dailySummariesRef.doc(docId).get();
      
      if (!doc.exists) {
        Logger.debug('[DailySummaryRemoteDS] Daily summary not found for docId: $docId');
        return null;
      }

      final summary = DailySummaryDto.fromFirestore(doc);
      Logger.debug('[DailySummaryRemoteDS] Daily summary found: ${summary.totalSales}');
      return summary;
    } catch (e, stackTrace) {
      Logger.error('[DailySummaryRemoteDS] Error getting daily summary', e, stackTrace);
      rethrow;
    }
  }

  @override
  Stream<DailySummaryDto?> watchDailySummary(String storeId, DateTime date) {
    try {
      final docId = DailySummaryEntity.generateDocumentId(date, storeId);
      Logger.debug('[DailySummaryRemoteDS] Watching daily summary for docId: $docId');

      return _dailySummariesRef.doc(docId).snapshots().map((doc) {
        if (!doc.exists) {
          Logger.debug('[DailySummaryRemoteDS] Daily summary not found for docId: $docId');
          return null;
        }

        final summary = DailySummaryDto.fromFirestore(doc);
        Logger.debug('[DailySummaryRemoteDS] Daily summary updated: ${summary.totalSales}');
        return summary;
      });
    } catch (e, stackTrace) {
      Logger.error('[DailySummaryRemoteDS] Error watching daily summary', e, stackTrace);
      return Stream.error(e, stackTrace);
    }
  }

  @override
  Future<List<DailySummaryDto>> getDailySummaries(
    String storeId, {
    DateTime? startDate,
    DateTime? endDate,
    int limit = 30,
  }) async {
    try {
      Logger.debug('[DailySummaryRemoteDS] Getting daily summaries for storeId: $storeId');

      Query<Map<String, dynamic>> query = _dailySummariesRef;

      // 添加店鋪過濾條件
      query = query.where('storeId', isEqualTo: storeId);

      // 添加日期範圍過濾條件
      if (startDate != null) {
        final startDateStr = _formatDate(startDate);
        query = query.where('date', isGreaterThanOrEqualTo: startDateStr);
      }

      if (endDate != null) {
        final endDateStr = _formatDate(endDate);
        query = query.where('date', isLessThanOrEqualTo: endDateStr);
      }

      // 按日期降序排列並限制數量
      query = query.orderBy('date', descending: true).limit(limit);

      final snapshot = await query.get();
      
      final summaries = snapshot.docs.map((doc) {
        return DailySummaryDto.fromFirestore(doc);
      }).toList();

      Logger.debug('[DailySummaryRemoteDS] Found ${summaries.length} daily summaries');
      return summaries;
    } catch (e, stackTrace) {
      Logger.error('[DailySummaryRemoteDS] Error getting daily summaries', e, stackTrace);
      rethrow;
    }
  }

  @override
  Stream<List<DailySummaryDto>> watchDailySummaries(
    String storeId, {
    DateTime? startDate,
    DateTime? endDate,
    int limit = 30,
  }) {
    try {
      Logger.debug('[DailySummaryRemoteDS] Watching daily summaries for storeId: $storeId');

      Query<Map<String, dynamic>> query = _dailySummariesRef;

      // 添加店鋪過濾條件
      query = query.where('storeId', isEqualTo: storeId);

      // 添加日期範圍過濾條件
      if (startDate != null) {
        final startDateStr = _formatDate(startDate);
        query = query.where('date', isGreaterThanOrEqualTo: startDateStr);
      }

      if (endDate != null) {
        final endDateStr = _formatDate(endDate);
        query = query.where('date', isLessThanOrEqualTo: endDateStr);
      }

      // 按日期降序排列並限制數量
      query = query.orderBy('date', descending: true).limit(limit);

      return query.snapshots().map((snapshot) {
        final summaries = snapshot.docs.map((doc) {
          return DailySummaryDto.fromFirestore(doc);
        }).toList();

        Logger.debug('[DailySummaryRemoteDS] Watched ${summaries.length} daily summaries');
        return summaries;
      });
    } catch (e, stackTrace) {
      Logger.error('[DailySummaryRemoteDS] Error watching daily summaries', e, stackTrace);
      return Stream.error(e, stackTrace);
    }
  }

  @override
  Future<String> createOrUpdateDailySummary(DailySummaryDto summary) async {
    try {
      final docId = DailySummaryEntity.generateDocumentId(
        DateTime.parse(summary.date),
        summary.storeId,
      );
      
      Logger.debug('[DailySummaryRemoteDS] Creating/updating daily summary for docId: $docId');

      await _dailySummariesRef.doc(docId).set(summary.toFirestore());
      
      Logger.debug('[DailySummaryRemoteDS] Daily summary created/updated successfully');
      return docId;
    } catch (e, stackTrace) {
      Logger.error('[DailySummaryRemoteDS] Error creating/updating daily summary', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> deleteDailySummary(String storeId, DateTime date) async {
    try {
      final docId = DailySummaryEntity.generateDocumentId(date, storeId);
      Logger.debug('[DailySummaryRemoteDS] Deleting daily summary for docId: $docId');

      await _dailySummariesRef.doc(docId).delete();
      
      Logger.debug('[DailySummaryRemoteDS] Daily summary deleted successfully');
    } catch (e, stackTrace) {
      Logger.error('[DailySummaryRemoteDS] Error deleting daily summary', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<bool> dailySummaryExists(String storeId, DateTime date) async {
    try {
      final docId = DailySummaryEntity.generateDocumentId(date, storeId);
      Logger.debug('[DailySummaryRemoteDS] Checking if daily summary exists for docId: $docId');

      final doc = await _dailySummariesRef.doc(docId).get();
      final exists = doc.exists;
      
      Logger.debug('[DailySummaryRemoteDS] Daily summary exists: $exists');
      return exists;
    } catch (e, stackTrace) {
      Logger.error('[DailySummaryRemoteDS] Error checking daily summary existence', e, stackTrace);
      rethrow;
    }
  }

  /// 格式化日期為 YYYY-MM-DD 字符串
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
