import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';
import 'package:excel/excel.dart';
import '../../../../core/constants/firestore_constants.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/services/report_generator_service.dart';
import '../../domain/entities/daily_summary_entity.dart';
import '../../../product_mgmt/domain/entities/product_entity.dart';
import '../models/daily_summary_dto.dart';
import '../../../product_mgmt/data/models/product_dto.dart';

/// PDF 報告生成服務實現
class ReportGeneratorServiceImpl implements ReportGeneratorService {
  final FirebaseFirestore _firestore;

  ReportGeneratorServiceImpl(this._firestore);

  @override
  Future<Uint8List> generateDailySalesReportPdf(String storeId, DateTime date) async {
    try {
      Logger.info(
        '[ReportGenerator] Generating daily sales report for store: $storeId, date: $date',
      );

      // 嘗試從每日摘要獲取數據
      final summaryData = await _getDailySummaryData(storeId, date);

      // 如果沒有摘要數據，則從銷售記錄計算
      final salesData = summaryData ?? await _calculateDailySalesData(storeId, date);

      // 獲取詳細的銷售交易數據
      final transactions = await _getDailySalesTransactions(storeId, date);

      // 生成 PDF
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build:
              (pw.Context context) => [
                _buildSalesReportHeader(date),
                pw.SizedBox(height: 20),
                _buildSalesReportSummary(salesData),
                pw.SizedBox(height: 20),
                _buildSalesTransactionsTable(transactions),
              ],
        ),
      );

      Logger.info('[ReportGenerator] Daily sales report generated successfully');
      return await pdf.save();
    } catch (e, stackTrace) {
      Logger.error('[ReportGenerator] Error generating daily sales report', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<Uint8List> generateInventoryReportPdf(String storeId, {String? tenantId}) async {
    try {
      Logger.info(
        '[ReportGenerator] Generating inventory report for store: $storeId, tenant: $tenantId',
      );

      // 獲取產品數據
      final products = await _getInventoryData(storeId, tenantId: tenantId);

      // 計算庫存統計
      final inventoryStats = _calculateInventoryStats(products);

      // 生成 PDF
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build:
              (pw.Context context) => [
                _buildInventoryReportHeader(tenantId),
                pw.SizedBox(height: 20),
                _buildInventoryReportSummary(inventoryStats),
                pw.SizedBox(height: 20),
                _buildInventoryTable(products),
              ],
        ),
      );

      Logger.info('[ReportGenerator] Inventory report generated successfully');
      return await pdf.save();
    } catch (e, stackTrace) {
      Logger.error('[ReportGenerator] Error generating inventory report', e, stackTrace);
      rethrow;
    }
  }

  /// 獲取每日摘要數據
  Future<Map<String, dynamic>?> _getDailySummaryData(String storeId, DateTime date) async {
    try {
      final documentId = DailySummaryEntity.generateDocumentId(date, storeId);

      final doc =
          await _firestore.collection(FirestoreConstants.dailySummaries).doc(documentId).get();

      if (doc.exists) {
        final summaryDto = DailySummaryDto.fromFirestore(doc);
        return {
          'totalSales': summaryDto.totalSales,
          'transactionsCount': summaryDto.transactionsCount,
          'lowStockProductsCount': summaryDto.lowStockProductsCount,
        };
      }
      return null;
    } catch (e) {
      Logger.warning(
        '[ReportGenerator] Could not fetch daily summary, will calculate from sales data: $e',
      );
      return null;
    }
  }

  /// 從銷售記錄計算每日數據
  Future<Map<String, dynamic>> _calculateDailySalesData(String storeId, DateTime date) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final salesRef = _firestore
        .collection(FirestoreConstants.stores)
        .doc(storeId)
        .collection(FirestoreConstants.sales)
        .where(FirestoreConstants.createdAt, isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
        .where(FirestoreConstants.createdAt, isLessThan: Timestamp.fromDate(endOfDay));

    final salesSnapshot = await salesRef.get();

    double totalSales = 0.0;
    int transactionsCount = 0;

    for (final doc in salesSnapshot.docs) {
      final data = doc.data();
      final status = data[FirestoreConstants.status] as String? ?? '';

      if (status == 'completed') {
        totalSales += (data[FirestoreConstants.totalAmount] as num?)?.toDouble() ?? 0.0;
        transactionsCount++;
      }
    }

    return {
      'totalSales': totalSales,
      'transactionsCount': transactionsCount,
      'lowStockProductsCount': 0, // 這裡可以添加低庫存計算邏輯
    };
  }

  /// 獲取每日銷售交易詳情
  Future<List<Map<String, dynamic>>> _getDailySalesTransactions(
    String storeId,
    DateTime date,
  ) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final salesRef = _firestore
        .collection(FirestoreConstants.stores)
        .doc(storeId)
        .collection(FirestoreConstants.sales)
        .where(FirestoreConstants.createdAt, isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
        .where(FirestoreConstants.createdAt, isLessThan: Timestamp.fromDate(endOfDay))
        .where(FirestoreConstants.status, isEqualTo: 'completed')
        .orderBy(FirestoreConstants.createdAt, descending: true);

    final salesSnapshot = await salesRef.get();

    return salesSnapshot.docs.map((doc) {
      final data = doc.data();
      return {
        'id': doc.id,
        'totalAmount': (data[FirestoreConstants.totalAmount] as num?)?.toDouble() ?? 0.0,
        'createdAt': (data[FirestoreConstants.createdAt] as Timestamp?)?.toDate() ?? DateTime.now(),
        'items': data[FirestoreConstants.items] as List<dynamic>? ?? [],
      };
    }).toList();
  }

  /// 獲取庫存數據
  Future<List<ProductEntity>> _getInventoryData(String storeId, {String? tenantId}) async {
    if (tenantId != null) {
      // 獲取特定租戶的產品
      final productsRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.tenants)
          .doc(tenantId)
          .collection(FirestoreConstants.products)
          .where(FirestoreConstants.active, isEqualTo: true);

      final productsSnapshot = await productsRef.get();

      return productsSnapshot.docs.map((doc) => ProductDto.fromFirestore(doc)).toList();
    } else {
      // 獲取全店產品
      final productsRef = _firestore
          .collectionGroup(FirestoreConstants.products)
          .where(FirestoreConstants.storeId, isEqualTo: storeId)
          .where(FirestoreConstants.active, isEqualTo: true);

      final productsSnapshot = await productsRef.get();

      return productsSnapshot.docs.map((doc) => ProductDto.fromFirestore(doc)).toList();
    }
  }

  /// 計算庫存統計
  Map<String, dynamic> _calculateInventoryStats(List<ProductEntity> products) {
    int totalProducts = products.length;
    int lowStockProducts = products.where((p) => p.stock <= p.lowStockLevel).length;
    double totalValue = products.fold(0.0, (total, p) => total + (p.price * p.stock));
    int totalStock = products.fold(0, (total, p) => total + p.stock);

    return {
      'totalProducts': totalProducts,
      'lowStockProducts': lowStockProducts,
      'totalValue': totalValue,
      'totalStock': totalStock,
    };
  }

  /// Build sales report header
  pw.Widget _buildSalesReportHeader(DateTime date) {
    final dateFormat = DateFormat('yyyy-MM-dd');

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Daily Sales Report',
          style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 8),
        pw.Text('Report Date: ${dateFormat.format(date)}', style: pw.TextStyle(fontSize: 14)),
        pw.Text(
          'Generated: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now())}',
          style: pw.TextStyle(fontSize: 12, color: PdfColors.grey600),
        ),
      ],
    );
  }

  /// Build sales report summary
  pw.Widget _buildSalesReportSummary(Map<String, dynamic> salesData) {
    final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$', decimalDigits: 0);

    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Sales Summary',
            style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 12),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('Total Sales:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                currencyFormat.format(salesData['totalSales']),
                style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('Transactions:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                '${salesData['transactionsCount']}',
                style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('Average Amount:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                salesData['transactionsCount'] > 0
                    ? currencyFormat.format(
                      salesData['totalSales'] / salesData['transactionsCount'],
                    )
                    : '\$0',
                style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build sales transactions table
  pw.Widget _buildSalesTransactionsTable(List<Map<String, dynamic>> transactions) {
    final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$', decimalDigits: 0);
    final timeFormat = DateFormat('HH:mm:ss');

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Transaction Details',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 12),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FlexColumnWidth(2),
            1: const pw.FlexColumnWidth(2),
            2: const pw.FlexColumnWidth(3),
            3: const pw.FlexColumnWidth(2),
          },
          children: [
            // Table header
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey100),
              children: [
                _buildTableCell('Time', isHeader: true),
                _buildTableCell('Transaction ID', isHeader: true),
                _buildTableCell('Items Qty', isHeader: true),
                _buildTableCell('Amount', isHeader: true),
              ],
            ),
            // Data rows
            ...transactions.map((transaction) {
              final items = transaction['items'] as List<dynamic>;
              final itemCount = items.fold<int>(
                0,
                (total, item) => total + ((item['qty'] as int?) ?? 0),
              );

              return pw.TableRow(
                children: [
                  _buildTableCell(timeFormat.format(transaction['createdAt'])),
                  _buildTableCell(transaction['id'].substring(0, 8)),
                  _buildTableCell('$itemCount items'),
                  _buildTableCell(currencyFormat.format(transaction['totalAmount'])),
                ],
              );
            }),
          ],
        ),
      ],
    );
  }

  /// Build inventory report header
  pw.Widget _buildInventoryReportHeader(String? tenantId) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          tenantId != null ? 'Tenant Inventory Report' : 'Store Inventory Report',
          style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 8),
        if (tenantId != null) pw.Text('Tenant ID: $tenantId', style: pw.TextStyle(fontSize: 14)),
        pw.Text(
          'Generated: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now())}',
          style: pw.TextStyle(fontSize: 12, color: PdfColors.grey600),
        ),
      ],
    );
  }

  /// Build inventory report summary
  pw.Widget _buildInventoryReportSummary(Map<String, dynamic> stats) {
    final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$', decimalDigits: 0);

    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Inventory Summary',
            style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 12),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('Total Products:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                '${stats['totalProducts']} items',
                style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('Total Stock:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                '${stats['totalStock']} units',
                style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('Total Value:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                currencyFormat.format(stats['totalValue']),
                style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('Low Stock Items:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                '${stats['lowStockProducts']} items',
                style: pw.TextStyle(
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                  color: stats['lowStockProducts'] > 0 ? PdfColors.red : PdfColors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build inventory table
  pw.Widget _buildInventoryTable(List<ProductEntity> products) {
    final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$', decimalDigits: 0);

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Product Details',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 12),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FlexColumnWidth(3),
            1: const pw.FlexColumnWidth(2),
            2: const pw.FlexColumnWidth(2),
            3: const pw.FlexColumnWidth(2),
            4: const pw.FlexColumnWidth(2),
          },
          children: [
            // Table header
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey100),
              children: [
                _buildTableCell('Product Name', isHeader: true),
                _buildTableCell('SKU', isHeader: true),
                _buildTableCell('Stock', isHeader: true),
                _buildTableCell('Unit Price', isHeader: true),
                _buildTableCell('Total Value', isHeader: true),
              ],
            ),
            // Data rows
            ...products.map((product) {
              final totalValue = product.price * product.stock;
              final isLowStock = product.stock <= product.lowStockLevel;

              return pw.TableRow(
                decoration: isLowStock ? const pw.BoxDecoration(color: PdfColors.red50) : null,
                children: [
                  _buildTableCell(product.name),
                  _buildTableCell(product.sku),
                  _buildTableCell('${product.stock}', textColor: isLowStock ? PdfColors.red : null),
                  _buildTableCell(currencyFormat.format(product.price)),
                  _buildTableCell(currencyFormat.format(totalValue)),
                ],
              );
            }),
          ],
        ),
      ],
    );
  }

  /// Build table cell
  pw.Widget _buildTableCell(String text, {bool isHeader = false, PdfColor? textColor}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: textColor,
        ),
      ),
    );
  }

  @override
  Future<Uint8List> generateDailySalesReportExcel(String storeId, DateTime date) async {
    try {
      Logger.info(
        '[ReportGenerator] Generating daily sales Excel report for store: $storeId, date: $date',
      );

      // 嘗試從每日摘要獲取數據
      final summaryData = await _getDailySummaryData(storeId, date);

      // 如果沒有摘要數據，則從銷售記錄計算
      final salesData = summaryData ?? await _calculateDailySalesData(storeId, date);

      // 獲取詳細的銷售交易數據
      final transactions = await _getDailySalesTransactions(storeId, date);

      // 創建 Excel 文件
      final excel = Excel.createExcel();
      final sheet = excel['Daily Sales Report'];

      // 設置標題和基本信息
      _buildExcelSalesHeader(sheet, date);

      // 添加摘要數據
      _buildExcelSalesSummary(sheet, salesData);

      // 添加交易詳情表格
      _buildExcelSalesTransactions(sheet, transactions);

      // 刪除默認的 Sheet1
      excel.delete('Sheet1');

      Logger.info('[ReportGenerator] Daily sales Excel report generated successfully');
      return Uint8List.fromList(excel.save()!);
    } catch (e, stackTrace) {
      Logger.error('[ReportGenerator] Error generating daily sales Excel report', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<Uint8List> generateInventoryReportExcel(String storeId, {String? tenantId}) async {
    try {
      Logger.info(
        '[ReportGenerator] Generating inventory Excel report for store: $storeId, tenant: $tenantId',
      );

      // 獲取產品數據
      final products = await _getInventoryData(storeId, tenantId: tenantId);

      // 計算庫存統計
      final inventoryStats = _calculateInventoryStats(products);

      // 創建 Excel 文件
      final excel = Excel.createExcel();
      final sheet = excel[tenantId != null ? 'Tenant Inventory' : 'Store Inventory'];

      // 設置標題和基本信息
      _buildExcelInventoryHeader(sheet, tenantId);

      // 添加摘要數據
      _buildExcelInventorySummary(sheet, inventoryStats);

      // 添加產品詳情表格
      _buildExcelInventoryTable(sheet, products);

      // 刪除默認的 Sheet1
      excel.delete('Sheet1');

      Logger.info('[ReportGenerator] Inventory Excel report generated successfully');
      return Uint8List.fromList(excel.save()!);
    } catch (e, stackTrace) {
      Logger.error('[ReportGenerator] Error generating inventory Excel report', e, stackTrace);
      rethrow;
    }
  }

  /// 構建 Excel 銷售報告標題
  void _buildExcelSalesHeader(Sheet sheet, DateTime date) {
    final dateFormat = DateFormat('yyyy-MM-dd');

    // 設置標題
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Daily Sales Report');
    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue(
      'Report Date: ${dateFormat.format(date)}',
    );
    sheet.cell(CellIndex.indexByString('A3')).value = TextCellValue(
      'Generated: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now())}',
    );

    // 設置標題樣式
    sheet.cell(CellIndex.indexByString('A1')).cellStyle = CellStyle(bold: true, fontSize: 16);
  }

  /// 構建 Excel 銷售摘要
  void _buildExcelSalesSummary(Sheet sheet, Map<String, dynamic> salesData) {
    final currencyFormat = NumberFormat.currency(symbol: '\$', decimalDigits: 0);

    // 摘要標題
    sheet.cell(CellIndex.indexByString('A5')).value = TextCellValue('Sales Summary');
    sheet.cell(CellIndex.indexByString('A5')).cellStyle = CellStyle(bold: true, fontSize: 14);

    // 摘要數據
    sheet.cell(CellIndex.indexByString('A7')).value = TextCellValue('Total Sales:');
    sheet.cell(CellIndex.indexByString('B7')).value = TextCellValue(
      currencyFormat.format(salesData['totalSales']),
    );

    sheet.cell(CellIndex.indexByString('A8')).value = TextCellValue('Transactions:');
    sheet.cell(CellIndex.indexByString('B8')).value = IntCellValue(salesData['transactionsCount']);

    sheet.cell(CellIndex.indexByString('A9')).value = TextCellValue('Average Amount:');
    final avgAmount =
        salesData['transactionsCount'] > 0
            ? salesData['totalSales'] / salesData['transactionsCount']
            : 0.0;
    sheet.cell(CellIndex.indexByString('B9')).value = TextCellValue(
      currencyFormat.format(avgAmount),
    );
  }

  /// 構建 Excel 銷售交易表格
  void _buildExcelSalesTransactions(Sheet sheet, List<Map<String, dynamic>> transactions) {
    final currencyFormat = NumberFormat.currency(symbol: '\$', decimalDigits: 0);
    final timeFormat = DateFormat('HH:mm:ss');

    // 表格標題
    sheet.cell(CellIndex.indexByString('A11')).value = TextCellValue('Transaction Details');
    sheet.cell(CellIndex.indexByString('A11')).cellStyle = CellStyle(bold: true, fontSize: 14);

    // 表格標頭
    final headers = ['Time', 'Transaction ID', 'Items Qty', 'Amount'];
    for (int i = 0; i < headers.length; i++) {
      final cellIndex = CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 12);
      sheet.cell(cellIndex).value = TextCellValue(headers[i]);
      sheet.cell(cellIndex).cellStyle = CellStyle(bold: true);
    }

    // 交易數據
    for (int i = 0; i < transactions.length; i++) {
      final transaction = transactions[i];
      final items = transaction['items'] as List<dynamic>;
      final itemCount = items.fold<int>(0, (total, item) => total + ((item['qty'] as int?) ?? 0));
      final rowIndex = 13 + i;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex))
          .value = TextCellValue(timeFormat.format(transaction['createdAt']));
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex))
          .value = TextCellValue(transaction['id'].substring(0, 8));
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex))
          .value = TextCellValue('$itemCount items');
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex))
          .value = TextCellValue(currencyFormat.format(transaction['totalAmount']));
    }
  }

  /// 構建 Excel 庫存報告標題
  void _buildExcelInventoryHeader(Sheet sheet, String? tenantId) {
    // 設置標題
    final title = tenantId != null ? 'Tenant Inventory Report' : 'Store Inventory Report';
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue(title);

    if (tenantId != null) {
      sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue('Tenant ID: $tenantId');
    }

    sheet.cell(CellIndex.indexByString('A3')).value = TextCellValue(
      'Generated: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now())}',
    );

    // 設置標題樣式
    sheet.cell(CellIndex.indexByString('A1')).cellStyle = CellStyle(bold: true, fontSize: 16);
  }

  /// 構建 Excel 庫存摘要
  void _buildExcelInventorySummary(Sheet sheet, Map<String, dynamic> stats) {
    final currencyFormat = NumberFormat.currency(symbol: '\$', decimalDigits: 0);

    // 摘要標題
    sheet.cell(CellIndex.indexByString('A5')).value = TextCellValue('Inventory Summary');
    sheet.cell(CellIndex.indexByString('A5')).cellStyle = CellStyle(bold: true, fontSize: 14);

    // 摘要數據
    sheet.cell(CellIndex.indexByString('A7')).value = TextCellValue('Total Products:');
    sheet.cell(CellIndex.indexByString('B7')).value = IntCellValue(stats['totalProducts']);

    sheet.cell(CellIndex.indexByString('A8')).value = TextCellValue('Total Stock:');
    sheet.cell(CellIndex.indexByString('B8')).value = IntCellValue(stats['totalStock']);

    sheet.cell(CellIndex.indexByString('A9')).value = TextCellValue('Total Value:');
    sheet.cell(CellIndex.indexByString('B9')).value = TextCellValue(
      currencyFormat.format(stats['totalValue']),
    );

    sheet.cell(CellIndex.indexByString('A10')).value = TextCellValue('Low Stock Items:');
    sheet.cell(CellIndex.indexByString('B10')).value = IntCellValue(stats['lowStockProducts']);
  }

  /// 構建 Excel 庫存表格
  void _buildExcelInventoryTable(Sheet sheet, List<ProductEntity> products) {
    final currencyFormat = NumberFormat.currency(symbol: '\$', decimalDigits: 0);

    // 表格標題
    sheet.cell(CellIndex.indexByString('A12')).value = TextCellValue('Product Details');
    sheet.cell(CellIndex.indexByString('A12')).cellStyle = CellStyle(bold: true, fontSize: 14);

    // 表格標頭
    final headers = ['Product Name', 'SKU', 'Stock', 'Unit Price', 'Total Value'];
    for (int i = 0; i < headers.length; i++) {
      final cellIndex = CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 13);
      sheet.cell(cellIndex).value = TextCellValue(headers[i]);
      sheet.cell(cellIndex).cellStyle = CellStyle(bold: true);
    }

    // 產品數據
    for (int i = 0; i < products.length; i++) {
      final product = products[i];
      final totalValue = product.price * product.stock;
      final rowIndex = 14 + i;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex))
          .value = TextCellValue(product.name);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex))
          .value = TextCellValue(product.sku);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex))
          .value = IntCellValue(product.stock);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex))
          .value = TextCellValue(currencyFormat.format(product.price));
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: rowIndex))
          .value = TextCellValue(currencyFormat.format(totalValue));
    }
  }
}
