import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';
import '../../../../core/constants/firestore_constants.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/services/report_generator_service.dart';
import '../../domain/entities/daily_summary_entity.dart';
import '../../../product_mgmt/domain/entities/product_entity.dart';
import '../models/daily_summary_dto.dart';
import '../../../product_mgmt/data/models/product_dto.dart';

/// PDF 報告生成服務實現
class ReportGeneratorServiceImpl implements ReportGeneratorService {
  final FirebaseFirestore _firestore;

  ReportGeneratorServiceImpl(this._firestore);

  @override
  Future<Uint8List> generateDailySalesReportPdf(String storeId, DateTime date) async {
    try {
      Logger.info(
        '[ReportGenerator] Generating daily sales report for store: $storeId, date: $date',
      );

      // 嘗試從每日摘要獲取數據
      final summaryData = await _getDailySummaryData(storeId, date);

      // 如果沒有摘要數據，則從銷售記錄計算
      final salesData = summaryData ?? await _calculateDailySalesData(storeId, date);

      // 獲取詳細的銷售交易數據
      final transactions = await _getDailySalesTransactions(storeId, date);

      // 生成 PDF
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build:
              (pw.Context context) => [
                _buildSalesReportHeader(date),
                pw.SizedBox(height: 20),
                _buildSalesReportSummary(salesData),
                pw.SizedBox(height: 20),
                _buildSalesTransactionsTable(transactions),
              ],
        ),
      );

      Logger.info('[ReportGenerator] Daily sales report generated successfully');
      return await pdf.save();
    } catch (e, stackTrace) {
      Logger.error('[ReportGenerator] Error generating daily sales report', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<Uint8List> generateInventoryReportPdf(String storeId, {String? tenantId}) async {
    try {
      Logger.info(
        '[ReportGenerator] Generating inventory report for store: $storeId, tenant: $tenantId',
      );

      // 獲取產品數據
      final products = await _getInventoryData(storeId, tenantId: tenantId);

      // 計算庫存統計
      final inventoryStats = _calculateInventoryStats(products);

      // 生成 PDF
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build:
              (pw.Context context) => [
                _buildInventoryReportHeader(tenantId),
                pw.SizedBox(height: 20),
                _buildInventoryReportSummary(inventoryStats),
                pw.SizedBox(height: 20),
                _buildInventoryTable(products),
              ],
        ),
      );

      Logger.info('[ReportGenerator] Inventory report generated successfully');
      return await pdf.save();
    } catch (e, stackTrace) {
      Logger.error('[ReportGenerator] Error generating inventory report', e, stackTrace);
      rethrow;
    }
  }

  /// 獲取每日摘要數據
  Future<Map<String, dynamic>?> _getDailySummaryData(String storeId, DateTime date) async {
    try {
      final documentId = DailySummaryEntity.generateDocumentId(date, storeId);

      final doc =
          await _firestore.collection(FirestoreConstants.dailySummaries).doc(documentId).get();

      if (doc.exists) {
        final summaryDto = DailySummaryDto.fromFirestore(doc);
        return {
          'totalSales': summaryDto.totalSales,
          'transactionsCount': summaryDto.transactionsCount,
          'lowStockProductsCount': summaryDto.lowStockProductsCount,
        };
      }
      return null;
    } catch (e) {
      Logger.warning(
        '[ReportGenerator] Could not fetch daily summary, will calculate from sales data: $e',
      );
      return null;
    }
  }

  /// 從銷售記錄計算每日數據
  Future<Map<String, dynamic>> _calculateDailySalesData(String storeId, DateTime date) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final salesRef = _firestore
        .collection(FirestoreConstants.stores)
        .doc(storeId)
        .collection(FirestoreConstants.sales)
        .where(FirestoreConstants.createdAt, isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
        .where(FirestoreConstants.createdAt, isLessThan: Timestamp.fromDate(endOfDay));

    final salesSnapshot = await salesRef.get();

    double totalSales = 0.0;
    int transactionsCount = 0;

    for (final doc in salesSnapshot.docs) {
      final data = doc.data();
      final status = data[FirestoreConstants.status] as String? ?? '';

      if (status == 'completed') {
        totalSales += (data[FirestoreConstants.totalAmount] as num?)?.toDouble() ?? 0.0;
        transactionsCount++;
      }
    }

    return {
      'totalSales': totalSales,
      'transactionsCount': transactionsCount,
      'lowStockProductsCount': 0, // 這裡可以添加低庫存計算邏輯
    };
  }

  /// 獲取每日銷售交易詳情
  Future<List<Map<String, dynamic>>> _getDailySalesTransactions(
    String storeId,
    DateTime date,
  ) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final salesRef = _firestore
        .collection(FirestoreConstants.stores)
        .doc(storeId)
        .collection(FirestoreConstants.sales)
        .where(FirestoreConstants.createdAt, isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
        .where(FirestoreConstants.createdAt, isLessThan: Timestamp.fromDate(endOfDay))
        .where(FirestoreConstants.status, isEqualTo: 'completed')
        .orderBy(FirestoreConstants.createdAt, descending: true);

    final salesSnapshot = await salesRef.get();

    return salesSnapshot.docs.map((doc) {
      final data = doc.data();
      return {
        'id': doc.id,
        'totalAmount': (data[FirestoreConstants.totalAmount] as num?)?.toDouble() ?? 0.0,
        'createdAt': (data[FirestoreConstants.createdAt] as Timestamp?)?.toDate() ?? DateTime.now(),
        'items': data[FirestoreConstants.items] as List<dynamic>? ?? [],
      };
    }).toList();
  }

  /// 獲取庫存數據
  Future<List<ProductEntity>> _getInventoryData(String storeId, {String? tenantId}) async {
    if (tenantId != null) {
      // 獲取特定租戶的產品
      final productsRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.tenants)
          .doc(tenantId)
          .collection(FirestoreConstants.products)
          .where(FirestoreConstants.active, isEqualTo: true);

      final productsSnapshot = await productsRef.get();

      return productsSnapshot.docs.map((doc) => ProductDto.fromFirestore(doc)).toList();
    } else {
      // 獲取全店產品
      final productsRef = _firestore
          .collectionGroup(FirestoreConstants.products)
          .where(FirestoreConstants.storeId, isEqualTo: storeId)
          .where(FirestoreConstants.active, isEqualTo: true);

      final productsSnapshot = await productsRef.get();

      return productsSnapshot.docs.map((doc) => ProductDto.fromFirestore(doc)).toList();
    }
  }

  /// 計算庫存統計
  Map<String, dynamic> _calculateInventoryStats(List<ProductEntity> products) {
    int totalProducts = products.length;
    int lowStockProducts = products.where((p) => p.stock <= p.lowStockLevel).length;
    double totalValue = products.fold(0.0, (total, p) => total + (p.price * p.stock));
    int totalStock = products.fold(0, (total, p) => total + p.stock);

    return {
      'totalProducts': totalProducts,
      'lowStockProducts': lowStockProducts,
      'totalValue': totalValue,
      'totalStock': totalStock,
    };
  }

  /// 構建銷售報告標題
  pw.Widget _buildSalesReportHeader(DateTime date) {
    final dateFormat = DateFormat('yyyy年MM月dd日', 'zh_TW');

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text('每日銷售報告', style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold)),
        pw.SizedBox(height: 8),
        pw.Text('報告日期: ${dateFormat.format(date)}', style: pw.TextStyle(fontSize: 14)),
        pw.Text(
          '生成時間: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now())}',
          style: pw.TextStyle(fontSize: 12, color: PdfColors.grey600),
        ),
      ],
    );
  }

  /// 構建銷售報告摘要
  pw.Widget _buildSalesReportSummary(Map<String, dynamic> salesData) {
    final currencyFormat = NumberFormat.currency(locale: 'zh_TW', symbol: 'NT\$', decimalDigits: 0);

    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text('銷售摘要', style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold)),
          pw.SizedBox(height: 12),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('總銷售額:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                currencyFormat.format(salesData['totalSales']),
                style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('交易筆數:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                '${salesData['transactionsCount']} 筆',
                style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('平均交易金額:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                salesData['transactionsCount'] > 0
                    ? currencyFormat.format(
                      salesData['totalSales'] / salesData['transactionsCount'],
                    )
                    : 'NT\$0',
                style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建銷售交易表格
  pw.Widget _buildSalesTransactionsTable(List<Map<String, dynamic>> transactions) {
    final currencyFormat = NumberFormat.currency(locale: 'zh_TW', symbol: 'NT\$', decimalDigits: 0);
    final timeFormat = DateFormat('HH:mm:ss');

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text('交易明細', style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold)),
        pw.SizedBox(height: 12),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FlexColumnWidth(2),
            1: const pw.FlexColumnWidth(2),
            2: const pw.FlexColumnWidth(3),
            3: const pw.FlexColumnWidth(2),
          },
          children: [
            // 表頭
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey100),
              children: [
                _buildTableCell('時間', isHeader: true),
                _buildTableCell('交易ID', isHeader: true),
                _buildTableCell('商品數量', isHeader: true),
                _buildTableCell('金額', isHeader: true),
              ],
            ),
            // 數據行
            ...transactions.map((transaction) {
              final items = transaction['items'] as List<dynamic>;
              final itemCount = items.fold<int>(
                0,
                (total, item) => total + ((item['qty'] as int?) ?? 0),
              );

              return pw.TableRow(
                children: [
                  _buildTableCell(timeFormat.format(transaction['createdAt'])),
                  _buildTableCell(transaction['id'].substring(0, 8)),
                  _buildTableCell('$itemCount 件'),
                  _buildTableCell(currencyFormat.format(transaction['totalAmount'])),
                ],
              );
            }),
          ],
        ),
      ],
    );
  }

  /// 構建庫存報告標題
  pw.Widget _buildInventoryReportHeader(String? tenantId) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          tenantId != null ? '租戶庫存報告' : '店鋪庫存報告',
          style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 8),
        if (tenantId != null) pw.Text('租戶ID: $tenantId', style: pw.TextStyle(fontSize: 14)),
        pw.Text(
          '生成時間: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now())}',
          style: pw.TextStyle(fontSize: 12, color: PdfColors.grey600),
        ),
      ],
    );
  }

  /// 構建庫存報告摘要
  pw.Widget _buildInventoryReportSummary(Map<String, dynamic> stats) {
    final currencyFormat = NumberFormat.currency(locale: 'zh_TW', symbol: 'NT\$', decimalDigits: 0);

    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text('庫存摘要', style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold)),
          pw.SizedBox(height: 12),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('商品總數:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                '${stats['totalProducts']} 種',
                style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('總庫存量:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                '${stats['totalStock']} 件',
                style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('庫存總價值:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                currencyFormat.format(stats['totalValue']),
                style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('低庫存商品:', style: pw.TextStyle(fontSize: 14)),
              pw.Text(
                '${stats['lowStockProducts']} 種',
                style: pw.TextStyle(
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                  color: stats['lowStockProducts'] > 0 ? PdfColors.red : PdfColors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建庫存表格
  pw.Widget _buildInventoryTable(List<ProductEntity> products) {
    final currencyFormat = NumberFormat.currency(locale: 'zh_TW', symbol: 'NT\$', decimalDigits: 0);

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text('商品明細', style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold)),
        pw.SizedBox(height: 12),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FlexColumnWidth(3),
            1: const pw.FlexColumnWidth(2),
            2: const pw.FlexColumnWidth(2),
            3: const pw.FlexColumnWidth(2),
            4: const pw.FlexColumnWidth(2),
          },
          children: [
            // 表頭
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey100),
              children: [
                _buildTableCell('商品名稱', isHeader: true),
                _buildTableCell('SKU', isHeader: true),
                _buildTableCell('庫存', isHeader: true),
                _buildTableCell('單價', isHeader: true),
                _buildTableCell('總價值', isHeader: true),
              ],
            ),
            // 數據行
            ...products.map((product) {
              final totalValue = product.price * product.stock;
              final isLowStock = product.stock <= product.lowStockLevel;

              return pw.TableRow(
                decoration: isLowStock ? const pw.BoxDecoration(color: PdfColors.red50) : null,
                children: [
                  _buildTableCell(product.name),
                  _buildTableCell(product.sku),
                  _buildTableCell('${product.stock}', textColor: isLowStock ? PdfColors.red : null),
                  _buildTableCell(currencyFormat.format(product.price)),
                  _buildTableCell(currencyFormat.format(totalValue)),
                ],
              );
            }),
          ],
        ),
      ],
    );
  }

  /// 構建表格單元格
  pw.Widget _buildTableCell(String text, {bool isHeader = false, PdfColor? textColor}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: textColor,
        ),
      ),
    );
  }
}
