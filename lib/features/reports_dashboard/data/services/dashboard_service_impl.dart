import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/constants/firestore_constants.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/entities/dashboard_data.dart';
import '../../domain/services/dashboard_service.dart';

/// 儀表板數據服務實現
class DashboardServiceImpl implements DashboardService {
  final FirebaseFirestore _firestore;

  DashboardServiceImpl(this._firestore);

  @override
  Future<DashboardData> getAdminDashboardData(String storeId) async {
    try {
      Logger.debug('[DashboardService] Getting admin dashboard data for store: $storeId');

      // 並行獲取所有數據
      final futures = await Future.wait([
        _getTodaySalesData(storeId),
        _getYesterdaySalesData(storeId),
        getLowStockProductsCount(storeId),
        getSalesTrendData(storeId),
        getTopSellingProducts(storeId),
      ]);

      final todayData = futures[0] as Map<String, dynamic>;
      final yesterdayData = futures[1] as Map<String, dynamic>;
      final lowStockCount = futures[2] as int;
      final trendData = futures[3] as List<SalesTrendData>;
      final topProducts = futures[4] as List<TopSellingProductData>;

      final dashboardData = DashboardData(
        todayTotalSales: todayData['totalSales'] as double,
        yesterdayTotalSales: yesterdayData['totalSales'] as double,
        todayTransactionsCount: todayData['transactionsCount'] as int,
        yesterdayTransactionsCount: yesterdayData['transactionsCount'] as int,
        lowStockProductsCount: lowStockCount,
        salesTrendData: trendData,
        topSellingProducts: topProducts,
      );

      Logger.debug('[DashboardService] Admin dashboard data retrieved successfully');
      return dashboardData;
    } catch (e, stackTrace) {
      Logger.error('[DashboardService] Error getting admin dashboard data', e, stackTrace);
      return DashboardDataExtension.empty();
    }
  }

  @override
  Future<DashboardData> getTenantDashboardData(String storeId, String tenantId) async {
    try {
      Logger.debug(
        '[DashboardService] Getting tenant dashboard data for store: $storeId, tenant: $tenantId',
      );

      // 並行獲取所有數據
      final futures = await Future.wait([
        _getTodaySalesData(storeId, tenantId: tenantId),
        _getYesterdaySalesData(storeId, tenantId: tenantId),
        getLowStockProductsCount(storeId, tenantId: tenantId),
        getSalesTrendData(storeId, tenantId: tenantId),
        getTopSellingProducts(storeId, tenantId: tenantId),
      ]);

      final todayData = futures[0] as Map<String, dynamic>;
      final yesterdayData = futures[1] as Map<String, dynamic>;
      final lowStockCount = futures[2] as int;
      final trendData = futures[3] as List<SalesTrendData>;
      final topProducts = futures[4] as List<TopSellingProductData>;

      final dashboardData = DashboardData(
        todayTotalSales: todayData['totalSales'] as double,
        yesterdayTotalSales: yesterdayData['totalSales'] as double,
        todayTransactionsCount: todayData['transactionsCount'] as int,
        yesterdayTransactionsCount: yesterdayData['transactionsCount'] as int,
        lowStockProductsCount: lowStockCount,
        salesTrendData: trendData,
        topSellingProducts: topProducts,
      );

      Logger.debug('[DashboardService] Tenant dashboard data retrieved successfully');
      return dashboardData;
    } catch (e, stackTrace) {
      Logger.error('[DashboardService] Error getting tenant dashboard data', e, stackTrace);
      return DashboardDataExtension.empty();
    }
  }

  @override
  Stream<DashboardData> watchAdminDashboardData(String storeId) {
    // 為了簡化實現，這裡每30秒刷新一次數據
    // 在實際應用中，可以使用更複雜的實時監聽邏輯
    return Stream.periodic(
      const Duration(seconds: 30),
    ).asyncMap((_) => getAdminDashboardData(storeId)).handleError((error, stackTrace) {
      Logger.error('[DashboardService] Error in admin dashboard stream', error, stackTrace);
      return DashboardDataExtension.empty();
    });
  }

  @override
  Stream<DashboardData> watchTenantDashboardData(String storeId, String tenantId) {
    // 為了簡化實現，這裡每30秒刷新一次數據
    return Stream.periodic(
      const Duration(seconds: 30),
    ).asyncMap((_) => getTenantDashboardData(storeId, tenantId)).handleError((error, stackTrace) {
      Logger.error('[DashboardService] Error in tenant dashboard stream', error, stackTrace);
      return DashboardDataExtension.empty();
    });
  }

  @override
  Future<int> getLowStockProductsCount(String storeId, {String? tenantId}) async {
    try {
      Logger.debug(
        '[DashboardService] Getting low stock products count for store: $storeId, tenant: $tenantId',
      );

      int lowStockCount = 0;

      if (tenantId != null) {
        // 查詢特定租戶的低庫存商品
        final productsRef = _firestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .where(FirestoreConstants.active, isEqualTo: true);

        final snapshot = await productsRef.get();

        for (final doc in snapshot.docs) {
          final data = doc.data();
          final stock = data[FirestoreConstants.stock] as int? ?? 0;
          final lowStockLevel = data[FirestoreConstants.lowStockLevel] as int? ?? 5;

          if (stock <= lowStockLevel) {
            lowStockCount++;
          }
        }
      } else {
        // 查詢所有租戶的低庫存商品（管理員視圖）
        final tenantsRef = _firestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants);

        final tenantsSnapshot = await tenantsRef.get();

        for (final tenantDoc in tenantsSnapshot.docs) {
          final productsRef = tenantDoc.reference
              .collection(FirestoreConstants.products)
              .where(FirestoreConstants.active, isEqualTo: true);

          final productsSnapshot = await productsRef.get();

          for (final productDoc in productsSnapshot.docs) {
            final data = productDoc.data();
            final stock = data[FirestoreConstants.stock] as int? ?? 0;
            final lowStockLevel = data[FirestoreConstants.lowStockLevel] as int? ?? 5;

            if (stock <= lowStockLevel) {
              lowStockCount++;
            }
          }
        }
      }

      Logger.debug('[DashboardService] Low stock products count: $lowStockCount');
      return lowStockCount;
    } catch (e, stackTrace) {
      Logger.error('[DashboardService] Error getting low stock products count', e, stackTrace);
      return 0;
    }
  }

  @override
  Future<List<SalesTrendData>> getSalesTrendData(
    String storeId, {
    String? tenantId,
    int days = 7,
  }) async {
    try {
      Logger.debug('[DashboardService] Getting sales trend data for $days days');

      final trendData = <SalesTrendData>[];
      final now = DateTime.now();

      for (int i = days - 1; i >= 0; i--) {
        final date = DateTime(now.year, now.month, now.day).subtract(Duration(days: i));
        final salesData = await _getSalesDataForDate(storeId, date, tenantId: tenantId);

        trendData.add(
          SalesTrendData(
            date: date,
            sales: salesData['totalSales'] as double,
            transactionsCount: salesData['transactionsCount'] as int,
          ),
        );
      }

      Logger.debug(
        '[DashboardService] Sales trend data retrieved: ${trendData.length} data points',
      );
      return trendData;
    } catch (e, stackTrace) {
      Logger.error('[DashboardService] Error getting sales trend data', e, stackTrace);
      return [];
    }
  }

  @override
  Future<List<TopSellingProductData>> getTopSellingProducts(
    String storeId, {
    String? tenantId,
    int limit = 5,
  }) async {
    try {
      Logger.debug('[DashboardService] Getting top selling products (limit: $limit)');

      // 獲取最近30天的銷售數據來計算最暢銷商品
      final endDate = DateTime.now();
      final startDate = endDate.subtract(const Duration(days: 30));

      final salesRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.sales)
          .where(
            FirestoreConstants.createdAt,
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate),
          )
          .where(FirestoreConstants.createdAt, isLessThanOrEqualTo: Timestamp.fromDate(endDate));

      final snapshot = await salesRef.get();

      // 聚合商品銷售數據
      final productSales = <String, Map<String, dynamic>>{};

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final items = data[FirestoreConstants.items] as List<dynamic>? ?? [];

        for (final item in items) {
          final itemData = item as Map<String, dynamic>;
          final productId = itemData['productId'] as String;
          final quantity = itemData['qty'] as int? ?? 0;
          final price = (itemData['price'] as num?)?.toDouble() ?? 0.0;
          final productName = itemData['name'] as String? ?? '';
          final productSku = itemData['sku'] as String? ?? '';
          final itemTenantId = itemData[FirestoreConstants.tenantId] as String? ?? '';

          // 如果指定了租戶ID，只計算該租戶的商品
          if (tenantId != null && itemTenantId != tenantId) {
            continue;
          }

          if (productSales.containsKey(productId)) {
            productSales[productId]!['quantitySold'] += quantity;
            productSales[productId]!['totalSales'] += price * quantity;
          } else {
            productSales[productId] = {
              'productName': productName,
              'productSku': productSku,
              'quantitySold': quantity,
              'totalSales': price * quantity,
            };
          }
        }
      }

      // 按銷售數量排序並取前N個
      final sortedProducts =
          productSales.entries.toList()..sort(
            (a, b) => (b.value['quantitySold'] as int).compareTo(a.value['quantitySold'] as int),
          );

      final topProducts =
          sortedProducts
              .take(limit)
              .map(
                (entry) => TopSellingProductData(
                  productId: entry.key,
                  productName: entry.value['productName'] as String,
                  productSku: entry.value['productSku'] as String,
                  quantitySold: entry.value['quantitySold'] as int,
                  totalSales: (entry.value['totalSales'] as num).toDouble(),
                ),
              )
              .toList();

      Logger.debug(
        '[DashboardService] Top selling products retrieved: ${topProducts.length} products',
      );
      return topProducts;
    } catch (e, stackTrace) {
      Logger.error('[DashboardService] Error getting top selling products', e, stackTrace);
      return [];
    }
  }

  /// 獲取今日銷售數據
  Future<Map<String, dynamic>> _getTodaySalesData(String storeId, {String? tenantId}) async {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    return _getSalesDataForDate(storeId, todayStart, tenantId: tenantId);
  }

  /// 獲取昨日銷售數據
  Future<Map<String, dynamic>> _getYesterdaySalesData(String storeId, {String? tenantId}) async {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    final yesterdayStart = DateTime(yesterday.year, yesterday.month, yesterday.day);
    return _getSalesDataForDate(storeId, yesterdayStart, tenantId: tenantId);
  }

  /// 獲取指定日期的銷售數據
  Future<Map<String, dynamic>> _getSalesDataForDate(
    String storeId,
    DateTime date, {
    String? tenantId,
  }) async {
    try {
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final salesRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.sales)
          .where(
            FirestoreConstants.createdAt,
            isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay),
          )
          .where(FirestoreConstants.createdAt, isLessThan: Timestamp.fromDate(endOfDay));

      final snapshot = await salesRef.get();

      double totalSales = 0.0;
      int transactionsCount = snapshot.docs.length;

      for (final doc in snapshot.docs) {
        final data = doc.data();

        if (tenantId != null) {
          // 如果指定了租戶ID，只計算該租戶的商品銷售額
          final items = data[FirestoreConstants.items] as List<dynamic>? ?? [];
          for (final item in items) {
            final itemData = item as Map<String, dynamic>;
            final itemTenantId = itemData[FirestoreConstants.tenantId] as String? ?? '';

            if (itemTenantId == tenantId) {
              final quantity = itemData['qty'] as int? ?? 0;
              final price = (itemData['price'] as num?)?.toDouble() ?? 0.0;
              totalSales += price * quantity;
            }
          }
        } else {
          // 管理員看到所有銷售額
          totalSales += (data[FirestoreConstants.totalAmount] as num?)?.toDouble() ?? 0.0;
        }
      }

      // 如果是租戶視圖，需要重新計算交易筆數（只計算包含該租戶商品的交易）
      if (tenantId != null) {
        transactionsCount = 0;
        for (final doc in snapshot.docs) {
          final data = doc.data();
          final items = data[FirestoreConstants.items] as List<dynamic>? ?? [];

          bool hasTenantItems = false;
          for (final item in items) {
            final itemData = item as Map<String, dynamic>;
            final itemTenantId = itemData[FirestoreConstants.tenantId] as String? ?? '';

            if (itemTenantId == tenantId) {
              hasTenantItems = true;
              break;
            }
          }

          if (hasTenantItems) {
            transactionsCount++;
          }
        }
      }

      return {'totalSales': totalSales, 'transactionsCount': transactionsCount};
    } catch (e, stackTrace) {
      Logger.error('[DashboardService] Error getting sales data for date: $date', e, stackTrace);
      return {'totalSales': 0.0, 'transactionsCount': 0};
    }
  }
}
