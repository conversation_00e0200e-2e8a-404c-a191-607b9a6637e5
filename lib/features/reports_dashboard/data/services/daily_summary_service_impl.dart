import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/constants/firestore_constants.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/entities/daily_summary_entity.dart';
import '../../domain/services/daily_summary_service.dart';
import '../models/daily_summary_dto.dart';

/// 每日摘要生成服務實現
class DailySummaryServiceImpl implements DailySummaryService {
  final FirebaseFirestore _firestore;

  DailySummaryServiceImpl(this._firestore);

  @override
  Future<void> generateDailySummary(String storeId, DateTime date) async {
    try {
      Logger.info('[DailySummaryService] Generating daily summary for store: $storeId, date: $date');

      // 計算日期範圍
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      // 查詢該日期的所有銷售數據
      final salesRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.sales)
          .where(FirestoreConstants.createdAt, isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where(FirestoreConstants.createdAt, isLessThan: Timestamp.fromDate(endOfDay));

      final salesSnapshot = await salesRef.get();

      // 聚合銷售數據
      double totalSales = 0.0;
      int transactionsCount = 0;

      for (final doc in salesSnapshot.docs) {
        final data = doc.data();
        final status = data[FirestoreConstants.status] as String? ?? '';
        
        // 只計算已完成的交易
        if (status == 'completed') {
          totalSales += (data[FirestoreConstants.totalAmount] as num?)?.toDouble() ?? 0.0;
          transactionsCount++;
        }
      }

      // 計算低庫存商品數量
      final lowStockCount = await _getLowStockProductsCount(storeId);

      // 生成文檔 ID
      final documentId = DailySummaryEntity.generateDocumentId(date, storeId);

      // 創建摘要實體
      final summaryEntity = DailySummaryEntity(
        id: documentId,
        date: startOfDay,
        storeId: storeId,
        totalSales: totalSales,
        transactionsCount: transactionsCount,
        lowStockProductsCount: lowStockCount,
        updatedAt: DateTime.now(),
        createdAt: DateTime.now(),
      );

      // 轉換為 DTO 並保存到 Firestore
      final summaryDto = DailySummaryDto.fromEntity(summaryEntity);
      
      await _firestore
          .collection(FirestoreConstants.dailySummaries)
          .doc(documentId)
          .set(summaryDto.toJson());

      Logger.info('[DailySummaryService] Daily summary generated successfully: $documentId');
    } catch (e, stackTrace) {
      Logger.error('[DailySummaryService] Error generating daily summary', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> generateDailySummariesBatch(
    String storeId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      Logger.info('[DailySummaryService] Generating batch summaries from $startDate to $endDate');

      final currentDate = DateTime(startDate.year, startDate.month, startDate.day);
      final finalDate = DateTime(endDate.year, endDate.month, endDate.day);

      var date = currentDate;
      while (date.isBefore(finalDate) || date.isAtSameMomentAs(finalDate)) {
        await generateDailySummary(storeId, date);
        date = date.add(const Duration(days: 1));
      }

      Logger.info('[DailySummaryService] Batch summary generation completed');
    } catch (e, stackTrace) {
      Logger.error('[DailySummaryService] Error generating batch summaries', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<bool> hasDailySummary(String storeId, DateTime date) async {
    try {
      final documentId = DailySummaryEntity.generateDocumentId(date, storeId);
      
      final doc = await _firestore
          .collection(FirestoreConstants.dailySummaries)
          .doc(documentId)
          .get();

      return doc.exists;
    } catch (e, stackTrace) {
      Logger.error('[DailySummaryService] Error checking daily summary existence', e, stackTrace);
      return false;
    }
  }

  @override
  Future<void> regenerateDailySummary(String storeId, DateTime date) async {
    try {
      Logger.info('[DailySummaryService] Regenerating daily summary for store: $storeId, date: $date');

      // 直接生成新的摘要（會覆蓋現有數據）
      await generateDailySummary(storeId, date);

      Logger.info('[DailySummaryService] Daily summary regenerated successfully');
    } catch (e, stackTrace) {
      Logger.error('[DailySummaryService] Error regenerating daily summary', e, stackTrace);
      rethrow;
    }
  }

  /// 計算低庫存商品數量
  Future<int> _getLowStockProductsCount(String storeId) async {
    try {
      int lowStockCount = 0;

      // 查詢所有租戶的低庫存商品
      final tenantsRef = _firestore
          .collection(FirestoreConstants.stores)
          .doc(storeId)
          .collection(FirestoreConstants.tenants);

      final tenantsSnapshot = await tenantsRef.get();
      
      for (final tenantDoc in tenantsSnapshot.docs) {
        final productsRef = tenantDoc.reference
            .collection(FirestoreConstants.products)
            .where(FirestoreConstants.active, isEqualTo: true);

        final productsSnapshot = await productsRef.get();
        
        for (final productDoc in productsSnapshot.docs) {
          final data = productDoc.data();
          final stock = data[FirestoreConstants.stock] as int? ?? 0;
          final lowStockLevel = data[FirestoreConstants.lowStockLevel] as int? ?? 5;
          
          if (stock <= lowStockLevel) {
            lowStockCount++;
          }
        }
      }

      return lowStockCount;
    } catch (e, stackTrace) {
      Logger.error('[DailySummaryService] Error calculating low stock products count', e, stackTrace);
      return 0;
    }
  }
}
