import '../../domain/entities/daily_summary_entity.dart';
import '../../domain/repositories/daily_summary_repository.dart';
import '../datasources/daily_summary_remote_ds.dart';
import '../models/daily_summary_dto.dart';

/// 每日摘要倉庫實現
class DailySummaryRepositoryImpl implements DailySummaryRepository {
  final DailySummaryRemoteDataSource _remoteDataSource;

  DailySummaryRepositoryImpl(this._remoteDataSource);

  @override
  Future<DailySummaryEntity?> getDailySummary(String storeId, DateTime date) async {
    final dto = await _remoteDataSource.getDailySummary(storeId, date);
    if (dto == null) return null;
    
    final docId = DailySummaryEntity.generateDocumentId(date, storeId);
    return dto.toEntity(docId);
  }

  @override
  Stream<DailySummaryEntity?> watchDailySummary(String storeId, DateTime date) {
    return _remoteDataSource.watchDailySummary(storeId, date).map((dto) {
      if (dto == null) return null;
      
      final docId = DailySummaryEntity.generateDocumentId(date, storeId);
      return dto.toEntity(docId);
    });
  }

  @override
  Future<List<DailySummaryEntity>> getDailySummaries(
    String storeId, {
    DateTime? startDate,
    DateTime? endDate,
    int limit = 30,
  }) async {
    final dtos = await _remoteDataSource.getDailySummaries(
      storeId,
      startDate: startDate,
      endDate: endDate,
      limit: limit,
    );

    return dtos.map((dto) {
      final docId = DailySummaryEntity.generateDocumentId(
        DateTime.parse(dto.date),
        dto.storeId,
      );
      return dto.toEntity(docId);
    }).toList();
  }

  @override
  Stream<List<DailySummaryEntity>> watchDailySummaries(
    String storeId, {
    DateTime? startDate,
    DateTime? endDate,
    int limit = 30,
  }) {
    return _remoteDataSource.watchDailySummaries(
      storeId,
      startDate: startDate,
      endDate: endDate,
      limit: limit,
    ).map((dtos) {
      return dtos.map((dto) {
        final docId = DailySummaryEntity.generateDocumentId(
          DateTime.parse(dto.date),
          dto.storeId,
        );
        return dto.toEntity(docId);
      }).toList();
    });
  }

  @override
  Future<DailySummaryEntity> createOrUpdateDailySummary(DailySummaryEntity summary) async {
    final dto = DailySummaryDto.fromEntity(summary);
    final docId = await _remoteDataSource.createOrUpdateDailySummary(dto);
    
    // 返回更新後的實體（包含正確的 ID）
    return summary.copyWith(id: docId);
  }

  @override
  Future<void> deleteDailySummary(String storeId, DateTime date) async {
    await _remoteDataSource.deleteDailySummary(storeId, date);
  }

  @override
  Future<bool> dailySummaryExists(String storeId, DateTime date) async {
    return await _remoteDataSource.dailySummaryExists(storeId, date);
  }

  @override
  Future<List<DailySummaryEntity>> getLatestDailySummaries(
    String storeId, {
    int limit = 1,
  }) async {
    final dtos = await _remoteDataSource.getDailySummaries(
      storeId,
      limit: limit,
    );

    return dtos.map((dto) {
      final docId = DailySummaryEntity.generateDocumentId(
        DateTime.parse(dto.date),
        dto.storeId,
      );
      return dto.toEntity(docId);
    }).toList();
  }
}
