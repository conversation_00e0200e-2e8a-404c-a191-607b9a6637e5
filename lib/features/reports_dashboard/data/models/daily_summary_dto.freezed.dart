// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'daily_summary_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DailySummaryDto {

/// 日期字符串 (YYYY-MM-DD 格式)
 String get date;/// 店鋪 ID
 String get storeId;/// 當日總銷售額
 double get totalSales;/// 當日交易筆數
 int get transactionsCount;/// 低庫存商品數量
 int get lowStockProductsCount;/// 最後更新時間
@TimestampConverter() Timestamp get updatedAt;/// 創建時間
@TimestampConverter() Timestamp get createdAt;
/// Create a copy of DailySummaryDto
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DailySummaryDtoCopyWith<DailySummaryDto> get copyWith => _$DailySummaryDtoCopyWithImpl<DailySummaryDto>(this as DailySummaryDto, _$identity);

  /// Serializes this DailySummaryDto to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DailySummaryDto&&(identical(other.date, date) || other.date == date)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.transactionsCount, transactionsCount) || other.transactionsCount == transactionsCount)&&(identical(other.lowStockProductsCount, lowStockProductsCount) || other.lowStockProductsCount == lowStockProductsCount)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,date,storeId,totalSales,transactionsCount,lowStockProductsCount,updatedAt,createdAt);

@override
String toString() {
  return 'DailySummaryDto(date: $date, storeId: $storeId, totalSales: $totalSales, transactionsCount: $transactionsCount, lowStockProductsCount: $lowStockProductsCount, updatedAt: $updatedAt, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $DailySummaryDtoCopyWith<$Res>  {
  factory $DailySummaryDtoCopyWith(DailySummaryDto value, $Res Function(DailySummaryDto) _then) = _$DailySummaryDtoCopyWithImpl;
@useResult
$Res call({
 String date, String storeId, double totalSales, int transactionsCount, int lowStockProductsCount,@TimestampConverter() Timestamp updatedAt,@TimestampConverter() Timestamp createdAt
});




}
/// @nodoc
class _$DailySummaryDtoCopyWithImpl<$Res>
    implements $DailySummaryDtoCopyWith<$Res> {
  _$DailySummaryDtoCopyWithImpl(this._self, this._then);

  final DailySummaryDto _self;
  final $Res Function(DailySummaryDto) _then;

/// Create a copy of DailySummaryDto
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? date = null,Object? storeId = null,Object? totalSales = null,Object? transactionsCount = null,Object? lowStockProductsCount = null,Object? updatedAt = null,Object? createdAt = null,}) {
  return _then(_self.copyWith(
date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as String,storeId: null == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as String,totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,transactionsCount: null == transactionsCount ? _self.transactionsCount : transactionsCount // ignore: cast_nullable_to_non_nullable
as int,lowStockProductsCount: null == lowStockProductsCount ? _self.lowStockProductsCount : lowStockProductsCount // ignore: cast_nullable_to_non_nullable
as int,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as Timestamp,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as Timestamp,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _DailySummaryDto extends DailySummaryDto {
  const _DailySummaryDto({required this.date, required this.storeId, required this.totalSales, required this.transactionsCount, required this.lowStockProductsCount, @TimestampConverter() required this.updatedAt, @TimestampConverter() required this.createdAt}): super._();
  factory _DailySummaryDto.fromJson(Map<String, dynamic> json) => _$DailySummaryDtoFromJson(json);

/// 日期字符串 (YYYY-MM-DD 格式)
@override final  String date;
/// 店鋪 ID
@override final  String storeId;
/// 當日總銷售額
@override final  double totalSales;
/// 當日交易筆數
@override final  int transactionsCount;
/// 低庫存商品數量
@override final  int lowStockProductsCount;
/// 最後更新時間
@override@TimestampConverter() final  Timestamp updatedAt;
/// 創建時間
@override@TimestampConverter() final  Timestamp createdAt;

/// Create a copy of DailySummaryDto
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DailySummaryDtoCopyWith<_DailySummaryDto> get copyWith => __$DailySummaryDtoCopyWithImpl<_DailySummaryDto>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DailySummaryDtoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DailySummaryDto&&(identical(other.date, date) || other.date == date)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.transactionsCount, transactionsCount) || other.transactionsCount == transactionsCount)&&(identical(other.lowStockProductsCount, lowStockProductsCount) || other.lowStockProductsCount == lowStockProductsCount)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,date,storeId,totalSales,transactionsCount,lowStockProductsCount,updatedAt,createdAt);

@override
String toString() {
  return 'DailySummaryDto(date: $date, storeId: $storeId, totalSales: $totalSales, transactionsCount: $transactionsCount, lowStockProductsCount: $lowStockProductsCount, updatedAt: $updatedAt, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$DailySummaryDtoCopyWith<$Res> implements $DailySummaryDtoCopyWith<$Res> {
  factory _$DailySummaryDtoCopyWith(_DailySummaryDto value, $Res Function(_DailySummaryDto) _then) = __$DailySummaryDtoCopyWithImpl;
@override @useResult
$Res call({
 String date, String storeId, double totalSales, int transactionsCount, int lowStockProductsCount,@TimestampConverter() Timestamp updatedAt,@TimestampConverter() Timestamp createdAt
});




}
/// @nodoc
class __$DailySummaryDtoCopyWithImpl<$Res>
    implements _$DailySummaryDtoCopyWith<$Res> {
  __$DailySummaryDtoCopyWithImpl(this._self, this._then);

  final _DailySummaryDto _self;
  final $Res Function(_DailySummaryDto) _then;

/// Create a copy of DailySummaryDto
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? date = null,Object? storeId = null,Object? totalSales = null,Object? transactionsCount = null,Object? lowStockProductsCount = null,Object? updatedAt = null,Object? createdAt = null,}) {
  return _then(_DailySummaryDto(
date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as String,storeId: null == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as String,totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,transactionsCount: null == transactionsCount ? _self.transactionsCount : transactionsCount // ignore: cast_nullable_to_non_nullable
as int,lowStockProductsCount: null == lowStockProductsCount ? _self.lowStockProductsCount : lowStockProductsCount // ignore: cast_nullable_to_non_nullable
as int,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as Timestamp,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as Timestamp,
  ));
}


}

// dart format on
