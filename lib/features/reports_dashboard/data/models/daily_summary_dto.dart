import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/daily_summary_entity.dart';

part 'daily_summary_dto.freezed.dart';
part 'daily_summary_dto.g.dart';

/// Timestamp 轉換器
class TimestampConverter implements JsonConverter<Timestamp, Object> {
  const TimestampConverter();

  @override
  Timestamp fromJson(Object json) {
    if (json is Timestamp) return json;
    if (json is Map<String, dynamic>) {
      return Timestamp(json['_seconds'] as int, json['_nanoseconds'] as int);
    }
    throw ArgumentError('Cannot convert $json to Timestamp');
  }

  @override
  Object toJson(Timestamp timestamp) => timestamp;
}

/// 每日摘要數據傳輸對象
///
/// 用於 Firestore 數據的序列化和反序列化
@freezed
abstract class DailySummaryDto with _$DailySummaryDto {
  const DailySummaryDto._();

  const factory DailySummaryDto({
    /// 日期字符串 (YYYY-MM-DD 格式)
    required String date,

    /// 店鋪 ID
    required String storeId,

    /// 當日總銷售額
    required double totalSales,

    /// 當日交易筆數
    required int transactionsCount,

    /// 低庫存商品數量
    required int lowStockProductsCount,

    /// 最後更新時間
    @TimestampConverter() required Timestamp updatedAt,

    /// 創建時間
    @TimestampConverter() required Timestamp createdAt,
  }) = _DailySummaryDto;

  /// 從 JSON 創建 DTO
  factory DailySummaryDto.fromJson(Map<String, dynamic> json) => _$DailySummaryDtoFromJson(json);

  /// 從 Firestore 文檔創建 DTO
  factory DailySummaryDto.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data();
    if (data == null) {
      throw Exception('DailySummaryDto.fromFirestore: Document data is null');
    }

    return DailySummaryDto(
      date: data['date'] as String? ?? '',
      storeId: data['storeId'] as String? ?? '',
      totalSales: (data['totalSales'] as num?)?.toDouble() ?? 0.0,
      transactionsCount: data['transactionsCount'] as int? ?? 0,
      lowStockProductsCount: data['lowStockProductsCount'] as int? ?? 0,
      updatedAt: data['updatedAt'] as Timestamp? ?? Timestamp.now(),
      createdAt: data['createdAt'] as Timestamp? ?? Timestamp.now(),
    );
  }

  /// 轉換為 Firestore 數據
  Map<String, dynamic> toFirestore() {
    return {
      'date': date,
      'storeId': storeId,
      'totalSales': totalSales,
      'transactionsCount': transactionsCount,
      'lowStockProductsCount': lowStockProductsCount,
      'updatedAt': updatedAt,
      'createdAt': createdAt,
    };
  }

  /// 轉換為領域實體
  DailySummaryEntity toEntity(String documentId) {
    // 解析日期
    DateTime parsedDate;
    try {
      final dateParts = date.split('-');
      if (dateParts.length == 3) {
        parsedDate = DateTime(
          int.parse(dateParts[0]),
          int.parse(dateParts[1]),
          int.parse(dateParts[2]),
        );
      } else {
        // 如果日期格式不正確，嘗試從文檔 ID 解析
        parsedDate = DailySummaryEntity.parseDateFromDocumentId(documentId) ?? DateTime.now();
      }
    } catch (e) {
      // 如果解析失敗，嘗試從文檔 ID 解析
      parsedDate = DailySummaryEntity.parseDateFromDocumentId(documentId) ?? DateTime.now();
    }

    return DailySummaryEntity(
      id: documentId,
      date: parsedDate,
      storeId: storeId,
      totalSales: totalSales,
      transactionsCount: transactionsCount,
      lowStockProductsCount: lowStockProductsCount,
      updatedAt: updatedAt.toDate(),
      createdAt: createdAt.toDate(),
    );
  }

  /// 從領域實體創建 DTO
  static DailySummaryDto fromEntity(DailySummaryEntity entity) {
    return DailySummaryDto(
      date: entity.formattedDate,
      storeId: entity.storeId,
      totalSales: entity.totalSales,
      transactionsCount: entity.transactionsCount,
      lowStockProductsCount: entity.lowStockProductsCount,
      updatedAt: Timestamp.fromDate(entity.updatedAt),
      createdAt: Timestamp.fromDate(entity.createdAt),
    );
  }
}
