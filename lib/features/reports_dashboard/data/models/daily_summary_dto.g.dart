// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'daily_summary_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_DailySummaryDto _$DailySummaryDtoFromJson(
  Map<String, dynamic> json,
) => _DailySummaryDto(
  date: json['date'] as String,
  storeId: json['storeId'] as String,
  totalSales: (json['totalSales'] as num).toDouble(),
  transactionsCount: (json['transactionsCount'] as num).toInt(),
  lowStockProductsCount: (json['lowStockProductsCount'] as num).toInt(),
  updatedAt: const TimestampConverter().fromJson(json['updatedAt'] as Object),
  createdAt: const TimestampConverter().fromJson(json['createdAt'] as Object),
);

Map<String, dynamic> _$DailySummaryDtoToJson(_DailySummaryDto instance) =>
    <String, dynamic>{
      'date': instance.date,
      'storeId': instance.storeId,
      'totalSales': instance.totalSales,
      'transactionsCount': instance.transactionsCount,
      'lowStockProductsCount': instance.lowStockProductsCount,
      'updatedAt': const TimestampConverter().toJson(instance.updatedAt),
      'createdAt': const TimestampConverter().toJson(instance.createdAt),
    };
