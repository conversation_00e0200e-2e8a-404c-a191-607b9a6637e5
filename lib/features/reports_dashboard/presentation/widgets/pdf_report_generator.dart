import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:printing/printing.dart';
import 'dart:io';
import '../providers/daily_summary_providers.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/enums/report_format.dart';

/// PDF 報告生成器組件
///
/// 提供選擇報告類型、日期範圍並生成 PDF 的 UI
class PdfReportGenerator extends ConsumerStatefulWidget {
  final String storeId;
  final String? tenantId; // null 表示管理員視圖

  const PdfReportGenerator({super.key, required this.storeId, this.tenantId});

  @override
  ConsumerState<PdfReportGenerator> createState() => _PdfReportGeneratorState();
}

class _PdfReportGeneratorState extends ConsumerState<PdfReportGenerator> {
  ReportType _selectedReportType = ReportType.dailySales;
  ReportFormat _selectedFormat = ReportFormat.pdf;
  DateTime _selectedDate = DateTime.now().subtract(const Duration(days: 1));
  bool _isGenerating = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [colorScheme.surface, colorScheme.surface.withOpacity(0.8)],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.1),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
        border: Border.all(color: colorScheme.outline.withOpacity(0.1), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [colorScheme.primary, colorScheme.primary.withOpacity(0.8)],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(Icons.analytics, color: colorScheme.onPrimary, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '報告生成器',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '生成專業的銷售和庫存報告',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 報告類型選擇
            _buildSectionHeader('報告類型', Icons.description, theme, colorScheme),
            const SizedBox(height: 12),
            Container(
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
              ),
              child: DropdownButtonFormField<ReportType>(
                value: _selectedReportType,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                items:
                    ReportType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Row(
                          children: [
                            Icon(
                              type == ReportType.dailySales ? Icons.trending_up : Icons.inventory,
                              size: 20,
                              color: colorScheme.primary,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              _getReportTypeName(type),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                onChanged:
                    _isGenerating
                        ? null
                        : (value) {
                          if (value != null) {
                            setState(() {
                              _selectedReportType = value;
                            });
                          }
                        },
              ),
            ),

            const SizedBox(height: 20),

            // 格式選擇
            _buildSectionHeader('報告格式', Icons.file_copy, theme, colorScheme),
            const SizedBox(height: 12),
            Container(
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
              ),
              child: DropdownButtonFormField<ReportFormat>(
                value: _selectedFormat,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                items:
                    ReportFormat.values.map((format) {
                      return DropdownMenuItem(
                        value: format,
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: colorScheme.primary.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                format == ReportFormat.pdf
                                    ? Icons.picture_as_pdf
                                    : Icons.table_chart,
                                size: 18,
                                color: colorScheme.primary,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              format.displayName,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                onChanged:
                    _isGenerating
                        ? null
                        : (value) {
                          if (value != null) {
                            setState(() {
                              _selectedFormat = value;
                            });
                          }
                        },
              ),
            ),

            const SizedBox(height: 16),

            // 日期選擇（僅對每日銷售報告顯示）
            if (_selectedReportType == ReportType.dailySales) ...[
              Text(
                '報告日期:',
                style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              InkWell(
                onTap: _isGenerating ? null : _selectDate,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  decoration: BoxDecoration(
                    border: Border.all(color: colorScheme.outline),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 20,
                        color: colorScheme.onSurface.withOpacity(0.6),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        DateFormat('yyyy-MM-dd').format(_selectedDate),
                        style: theme.textTheme.bodyLarge,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // 生成按鈕
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    height: 56,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors:
                            _isGenerating
                                ? [colorScheme.outline, colorScheme.outline.withOpacity(0.8)]
                                : [colorScheme.primary, colorScheme.primary.withOpacity(0.8)],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow:
                          _isGenerating
                              ? []
                              : [
                                BoxShadow(
                                  color: colorScheme.primary.withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                    ),
                    child: ElevatedButton.icon(
                      onPressed: _isGenerating ? null : _generateReport,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        shadowColor: Colors.transparent,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                      ),
                      icon:
                          _isGenerating
                              ? SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(colorScheme.onPrimary),
                                ),
                              )
                              : Icon(
                                _selectedFormat == ReportFormat.pdf
                                    ? Icons.picture_as_pdf
                                    : Icons.table_chart,
                                color: colorScheme.onPrimary,
                              ),
                      label: Text(
                        _isGenerating ? '生成中...' : '生成 ${_selectedFormat.displayName}',
                        style: TextStyle(
                          color: colorScheme.onPrimary,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  height: 56,
                  decoration: BoxDecoration(
                    color: colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: colorScheme.outline.withOpacity(0.3)),
                  ),
                  child: ElevatedButton.icon(
                    onPressed: _isGenerating ? null : _previewReport,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                    ),
                    icon: Icon(Icons.preview, color: colorScheme.onSurfaceVariant),
                    label: Text(
                      '預覽',
                      style: TextStyle(
                        color: colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 說明信息
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    colorScheme.primaryContainer.withOpacity(0.3),
                    colorScheme.primaryContainer.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: colorScheme.primary.withOpacity(0.2), width: 1),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: colorScheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(Icons.lightbulb_outline, size: 16, color: colorScheme.primary),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '報告內容說明',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    _getReportDescription(_selectedReportType),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withOpacity(0.8),
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      helpText: '選擇報告日期',
      cancelText: '取消',
      confirmText: '確定',
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _generateReport() async {
    if (_isGenerating) return;

    setState(() {
      _isGenerating = true;
    });

    try {
      final reportService = ref.read(reportGeneratorServiceProvider);
      Uint8List reportBytes;

      if (_selectedFormat == ReportFormat.pdf) {
        switch (_selectedReportType) {
          case ReportType.dailySales:
            reportBytes = await reportService.generateDailySalesReportPdf(
              widget.storeId,
              _selectedDate,
            );
            break;
          case ReportType.inventory:
            reportBytes = await reportService.generateInventoryReportPdf(
              widget.storeId,
              tenantId: widget.tenantId,
            );
            break;
        }
      } else {
        // Excel format
        switch (_selectedReportType) {
          case ReportType.dailySales:
            reportBytes = await reportService.generateDailySalesReportExcel(
              widget.storeId,
              _selectedDate,
            );
            break;
          case ReportType.inventory:
            reportBytes = await reportService.generateInventoryReportExcel(
              widget.storeId,
              tenantId: widget.tenantId,
            );
            break;
        }
      }

      // 保存並分享報告
      await _saveReportAndShare(reportBytes, _getReportFileName());

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${_selectedFormat.displayName} 報告生成成功'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      // Debug logging for error tracking
      Logger.error('Failed to generate PDF report: ${_getReportTypeName(_selectedReportType)}', e);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('生成報告失敗：$e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
        });
      }
    }
  }

  Future<void> _previewReport() async {
    if (_isGenerating) return;

    setState(() {
      _isGenerating = true;
    });

    try {
      final reportService = ref.read(reportGeneratorServiceProvider);
      Uint8List pdfBytes;

      switch (_selectedReportType) {
        case ReportType.dailySales:
          pdfBytes = await reportService.generateDailySalesReportPdf(widget.storeId, _selectedDate);
          break;
        case ReportType.inventory:
          pdfBytes = await reportService.generateInventoryReportPdf(
            widget.storeId,
            tenantId: widget.tenantId,
          );
          break;
      }

      // 使用 printing 套件預覽 PDF
      if (mounted) {
        await Printing.layoutPdf(onLayout: (format) async => pdfBytes, name: _getReportFileName());
      }
    } catch (e) {
      // Debug logging for error tracking
      Logger.error('Failed to preview PDF report: ${_getReportTypeName(_selectedReportType)}', e);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('預覽報告失敗：$e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
        });
      }
    }
  }

  Future<void> _saveReportAndShare(Uint8List reportBytes, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    await file.writeAsBytes(reportBytes);

    await Share.shareXFiles([XFile(file.path)], text: '報告已生成');
  }

  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.dailySales:
        return '每日銷售報告';
      case ReportType.inventory:
        return widget.tenantId != null ? '我的庫存報告' : '店鋪庫存報告';
    }
  }

  String _getReportDescription(ReportType type) {
    switch (type) {
      case ReportType.dailySales:
        return '• 包含指定日期的銷售摘要和交易明細\n'
            '• 顯示總銷售額、交易筆數和平均交易金額\n'
            '• 列出所有完成的交易記錄';
      case ReportType.inventory:
        return widget.tenantId != null
            ? '• 包含您的所有商品庫存信息\n'
                '• 顯示商品名稱、SKU、庫存數量和價值\n'
                '• 標示低庫存商品'
            : '• 包含店鋪所有商品庫存信息\n'
                '• 顯示商品名稱、SKU、庫存數量和價值\n'
                '• 標示低庫存商品和租戶信息';
    }
  }

  String _getReportFileName() {
    final dateStr = DateFormat(
      'yyyyMMdd',
    ).format(_selectedReportType == ReportType.dailySales ? _selectedDate : DateTime.now());
    final extension = _selectedFormat.fileExtension;

    switch (_selectedReportType) {
      case ReportType.dailySales:
        return '每日銷售報告_$dateStr.$extension';
      case ReportType.inventory:
        return widget.tenantId != null
            ? '庫存報告_${widget.tenantId}_$dateStr.$extension'
            : '店鋪庫存報告_$dateStr.$extension';
    }
  }

  Widget _buildSectionHeader(
    String title,
    IconData icon,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 16, color: colorScheme.primary),
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
      ],
    );
  }
}

enum ReportType { dailySales, inventory }
