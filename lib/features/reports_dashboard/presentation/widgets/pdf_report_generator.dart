import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:printing/printing.dart';
import 'dart:io';
import '../providers/daily_summary_providers.dart';
import '../../../../core/utils/logger.dart';

/// PDF 報告生成器組件
///
/// 提供選擇報告類型、日期範圍並生成 PDF 的 UI
class PdfReportGenerator extends ConsumerStatefulWidget {
  final String storeId;
  final String? tenantId; // null 表示管理員視圖

  const PdfReportGenerator({super.key, required this.storeId, this.tenantId});

  @override
  ConsumerState<PdfReportGenerator> createState() => _PdfReportGeneratorState();
}

class _PdfReportGeneratorState extends ConsumerState<PdfReportGenerator> {
  ReportType _selectedReportType = ReportType.dailySales;
  DateTime _selectedDate = DateTime.now().subtract(const Duration(days: 1));
  bool _isGenerating = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.picture_as_pdf, color: colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'PDF 報告生成器',
                  style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 報告類型選擇
            Text('報告類型:', style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600)),
            const SizedBox(height: 8),
            DropdownButtonFormField<ReportType>(
              value: _selectedReportType,
              decoration: InputDecoration(
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items:
                  ReportType.values.map((type) {
                    return DropdownMenuItem(value: type, child: Text(_getReportTypeName(type)));
                  }).toList(),
              onChanged:
                  _isGenerating
                      ? null
                      : (value) {
                        if (value != null) {
                          setState(() {
                            _selectedReportType = value;
                          });
                        }
                      },
            ),

            const SizedBox(height: 16),

            // 日期選擇（僅對每日銷售報告顯示）
            if (_selectedReportType == ReportType.dailySales) ...[
              Text(
                '報告日期:',
                style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              InkWell(
                onTap: _isGenerating ? null : _selectDate,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  decoration: BoxDecoration(
                    border: Border.all(color: colorScheme.outline),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 20,
                        color: colorScheme.onSurface.withOpacity(0.6),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        DateFormat('yyyy-MM-dd').format(_selectedDate),
                        style: theme.textTheme.bodyLarge,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // 生成按鈕
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isGenerating ? null : _generateReport,
                    icon:
                        _isGenerating
                            ? SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(colorScheme.onPrimary),
                              ),
                            )
                            : const Icon(Icons.picture_as_pdf),
                    label: Text(_isGenerating ? '生成中...' : '生成 PDF'),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: _isGenerating ? null : _previewReport,
                  icon: const Icon(Icons.preview),
                  label: const Text('預覽'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.secondary,
                    foregroundColor: colorScheme.onSecondary,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 說明信息
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.surfaceVariant.withOpacity(0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, size: 16, color: colorScheme.primary),
                      const SizedBox(width: 8),
                      Text(
                        '說明',
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getReportDescription(_selectedReportType),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      helpText: '選擇報告日期',
      cancelText: '取消',
      confirmText: '確定',
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _generateReport() async {
    if (_isGenerating) return;

    setState(() {
      _isGenerating = true;
    });

    try {
      final reportService = ref.read(reportGeneratorServiceProvider);
      Uint8List pdfBytes;

      switch (_selectedReportType) {
        case ReportType.dailySales:
          pdfBytes = await reportService.generateDailySalesReportPdf(widget.storeId, _selectedDate);
          break;
        case ReportType.inventory:
          pdfBytes = await reportService.generateInventoryReportPdf(
            widget.storeId,
            tenantId: widget.tenantId,
          );
          break;
      }

      // 保存並分享 PDF
      await _savePdfAndShare(pdfBytes, _getReportFileName());

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('PDF 報告生成成功'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      // Debug logging for error tracking
      Logger.error('Failed to generate PDF report: ${_getReportTypeName(_selectedReportType)}', e);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('生成報告失敗：$e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
        });
      }
    }
  }

  Future<void> _previewReport() async {
    if (_isGenerating) return;

    setState(() {
      _isGenerating = true;
    });

    try {
      final reportService = ref.read(reportGeneratorServiceProvider);
      Uint8List pdfBytes;

      switch (_selectedReportType) {
        case ReportType.dailySales:
          pdfBytes = await reportService.generateDailySalesReportPdf(widget.storeId, _selectedDate);
          break;
        case ReportType.inventory:
          pdfBytes = await reportService.generateInventoryReportPdf(
            widget.storeId,
            tenantId: widget.tenantId,
          );
          break;
      }

      // 使用 printing 套件預覽 PDF
      if (mounted) {
        await Printing.layoutPdf(onLayout: (format) async => pdfBytes, name: _getReportFileName());
      }
    } catch (e) {
      // Debug logging for error tracking
      Logger.error('Failed to preview PDF report: ${_getReportTypeName(_selectedReportType)}', e);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('預覽報告失敗：$e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
        });
      }
    }
  }

  Future<void> _savePdfAndShare(Uint8List pdfBytes, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    await file.writeAsBytes(pdfBytes);

    await Share.shareXFiles([XFile(file.path)], text: '報告已生成');
  }

  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.dailySales:
        return '每日銷售報告';
      case ReportType.inventory:
        return widget.tenantId != null ? '我的庫存報告' : '店鋪庫存報告';
    }
  }

  String _getReportDescription(ReportType type) {
    switch (type) {
      case ReportType.dailySales:
        return '• 包含指定日期的銷售摘要和交易明細\n'
            '• 顯示總銷售額、交易筆數和平均交易金額\n'
            '• 列出所有完成的交易記錄';
      case ReportType.inventory:
        return widget.tenantId != null
            ? '• 包含您的所有商品庫存信息\n'
                '• 顯示商品名稱、SKU、庫存數量和價值\n'
                '• 標示低庫存商品'
            : '• 包含店鋪所有商品庫存信息\n'
                '• 顯示商品名稱、SKU、庫存數量和價值\n'
                '• 標示低庫存商品和租戶信息';
    }
  }

  String _getReportFileName() {
    final dateStr = DateFormat(
      'yyyyMMdd',
    ).format(_selectedReportType == ReportType.dailySales ? _selectedDate : DateTime.now());

    switch (_selectedReportType) {
      case ReportType.dailySales:
        return '每日銷售報告_$dateStr.pdf';
      case ReportType.inventory:
        return widget.tenantId != null
            ? '庫存報告_${widget.tenantId}_$dateStr.pdf'
            : '店鋪庫存報告_$dateStr.pdf';
    }
  }
}

enum ReportType { dailySales, inventory }
