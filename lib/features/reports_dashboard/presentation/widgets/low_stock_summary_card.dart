import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../product_mgmt/presentation/providers/product_providers.dart';
import '../pages/low_stock_products_page.dart';

/// 低庫存商品摘要卡片
///
/// 顯示低庫存商品數量並提供快速訪問
class LowStockSummaryCard extends ConsumerWidget {
  final String storeId;
  final String? tenantId; // null 表示管理員視圖

  const LowStockSummaryCard({super.key, required this.storeId, this.tenantId});

  bool get isAdminView => tenantId == null;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 根據視圖類型選擇對應的 Provider
    final lowStockProductsAsync =
        isAdminView
            ? ref.watch(lowStockProductsByStoreProvider(storeId))
            : ref.watch(lowStockProductsByTenantProvider((storeId: storeId, tenantId: tenantId!)));

    return lowStockProductsAsync.when(
      loading: () => _buildLoadingCard(context),
      error: (error, stackTrace) => _buildErrorCard(context, error),
      data: (products) => _buildDataCard(context, products),
    );
  }

  Widget _buildLoadingCard(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.inventory_2, color: colorScheme.primary, size: 24),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '低庫存商品',
                      style: theme.textTheme.titleSmall?.copyWith(
                        color: colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              const Center(
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '載入中...',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withOpacity(0.6),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorCard(BuildContext context, Object error) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.error_outline, color: colorScheme.error, size: 24),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '低庫存商品',
                      style: theme.textTheme.titleSmall?.copyWith(
                        color: colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                '載入失敗',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.error,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '點擊重試',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDataCard(BuildContext context, List products) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final count = products.length;

    // 根據數量決定顏色和狀態
    Color iconColor;
    Color countColor;
    Color backgroundColor;
    String statusText;
    IconData statusIcon;

    if (count == 0) {
      iconColor = Colors.green;
      countColor = Colors.green;
      backgroundColor = Colors.green.withOpacity(0.05);
      statusText = '庫存充足';
      statusIcon = Icons.check_circle_outline;
    } else if (count <= 5) {
      iconColor = Colors.orange;
      countColor = Colors.orange;
      backgroundColor = Colors.orange.withOpacity(0.05);
      statusText = isAdminView ? '需要關注' : '部分商品';
      statusIcon = Icons.warning_amber_outlined;
    } else {
      iconColor = colorScheme.error;
      countColor = colorScheme.error;
      backgroundColor = colorScheme.error.withOpacity(0.05);
      statusText = isAdminView ? '緊急補貨' : '多項商品';
      statusIcon = Icons.error_outline;
    }

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [backgroundColor, backgroundColor.withOpacity(0.3)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(color: iconColor.withOpacity(0.1), blurRadius: 8, offset: const Offset(0, 4)),
        ],
        border: Border.all(color: iconColor.withOpacity(0.2), width: 1),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => LowStockProductsPage(storeId: storeId, tenantId: tenantId),
              ),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: iconColor.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(Icons.inventory_2, color: iconColor, size: 24),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        '低庫存商品',
                        style: theme.textTheme.titleSmall?.copyWith(
                          color: colorScheme.onSurface.withOpacity(0.7),
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  count.toString(),
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: countColor,
                    fontSize: 28,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(statusIcon, size: 16, color: iconColor),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        statusText,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: iconColor,
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (count > 0)
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: iconColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(Icons.arrow_forward_ios, size: 12, color: iconColor),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
