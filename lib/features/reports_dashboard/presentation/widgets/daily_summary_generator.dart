import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../providers/daily_summary_providers.dart';
import '../pages/daily_summaries_page.dart';

/// 每日摘要生成器組件
///
/// 提供管理員生成每日摘要的 UI 界面
class DailySummaryGenerator extends ConsumerStatefulWidget {
  final String storeId;

  const DailySummaryGenerator({super.key, required this.storeId});

  @override
  ConsumerState<DailySummaryGenerator> createState() => _DailySummaryGeneratorState();
}

class _DailySummaryGeneratorState extends ConsumerState<DailySummaryGenerator> {
  DateTime _selectedDate = DateTime.now().subtract(const Duration(days: 1)); // 默認選擇昨天
  bool _isGenerating = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.summarize, color: colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  '每日摘要生成器',
                  style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text('選擇要生成摘要的日期：', style: theme.textTheme.bodyMedium),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: _isGenerating ? null : _selectDate,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                      decoration: BoxDecoration(
                        border: Border.all(color: colorScheme.outline),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.calendar_today,
                            size: 20,
                            color: colorScheme.onSurface.withOpacity(0.6),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            DateFormat('yyyy-MM-dd').format(_selectedDate),
                            style: theme.textTheme.bodyLarge,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _isGenerating ? null : _generateSummary,
                  icon:
                      _isGenerating
                          ? SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(colorScheme.onPrimary),
                            ),
                          )
                          : const Icon(Icons.play_arrow),
                  label: Text(_isGenerating ? '生成中...' : '生成摘要'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.surfaceVariant.withOpacity(0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, size: 16, color: colorScheme.primary),
                      const SizedBox(width: 8),
                      Text(
                        '說明',
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• 每日摘要會聚合指定日期的所有銷售數據\n'
                    '• 包含總銷售額、交易筆數和低庫存商品數量\n'
                    '• 如果該日期已有摘要，將會被覆蓋更新\n'
                    '• 建議在每日營業結束後生成前一天的摘要',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // 查看摘要連結
            Center(
              child: TextButton.icon(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => DailySummariesPage(storeId: widget.storeId),
                    ),
                  );
                },
                icon: const Icon(Icons.visibility, size: 16),
                label: const Text('查看已生成的摘要'),
                style: TextButton.styleFrom(foregroundColor: colorScheme.primary),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      helpText: '選擇日期',
      cancelText: '取消',
      confirmText: '確定',
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _generateSummary() async {
    if (_isGenerating) return;

    setState(() {
      _isGenerating = true;
    });

    try {
      await ref
          .read(dailySummaryServiceProvider)
          .generateDailySummary(widget.storeId, _selectedDate);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('成功生成 ${DateFormat('yyyy-MM-dd').format(_selectedDate)} 的每日摘要'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );

        // 刷新相關的 Provider 數據
        ref.invalidate(dailySummaryRepositoryProvider);
        ref.invalidate(adminDashboardDataProvider(widget.storeId));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('生成摘要失敗：$e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
        });
      }
    }
  }
}
