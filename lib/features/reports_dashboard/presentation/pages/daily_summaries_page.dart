import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../providers/daily_summary_providers.dart';
import '../widgets/daily_summary_generator.dart';
import '../../domain/entities/daily_summary_entity.dart';
import '../../../../core/utils/logger.dart';

/// 每日摘要查看頁面
///
/// 顯示已生成的每日摘要列表和生成新摘要的功能
class DailySummariesPage extends ConsumerStatefulWidget {
  final String storeId;

  const DailySummariesPage({super.key, required this.storeId});

  @override
  ConsumerState<DailySummariesPage> createState() => _DailySummariesPageState();
}

class _DailySummariesPageState extends ConsumerState<DailySummariesPage> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('每日摘要管理'),
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 摘要生成器
            DailySummaryGenerator(storeId: widget.storeId),
            const SizedBox(height: 24),

            // 最近的摘要列表
            Text(
              '最近的每日摘要',
              style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildRecentSummariesList(),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentSummariesList() {
    final summariesAsync = ref.watch(recentDailySummariesProvider(widget.storeId));

    return summariesAsync.when(
      loading:
          () => const Center(
            child: Padding(padding: EdgeInsets.all(32), child: CircularProgressIndicator()),
          ),
      error: (error, stack) {
        // Debug logging for error tracking
        Logger.error('Error loading daily summaries', error);
        Logger.debug('Stack trace: $stack');

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Icon(Icons.error_outline, size: 48, color: Theme.of(context).colorScheme.error),
                const SizedBox(height: 16),
                Text('載入摘要失敗', style: Theme.of(context).textTheme.titleMedium),
                const SizedBox(height: 8),
                Text(
                  error.toString(),
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
      data: (summaries) {
        if (summaries.isEmpty) {
          return _buildEmptyState();
        }

        return Column(children: summaries.map((summary) => _buildSummaryCard(summary)).toList());
      },
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(Icons.summarize_outlined, size: 64, color: colorScheme.outline),
            const SizedBox(height: 16),
            Text(
              '尚無每日摘要',
              style: theme.textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '使用上方的生成器來創建第一個每日摘要',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(DailySummaryEntity summary) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final currencyFormat = NumberFormat.currency(symbol: 'NT\$', decimalDigits: 0); // 移除 locale 參數
    final dateFormat = DateFormat('yyyy年MM月dd日 (E)'); // 移除 locale 參數避免初始化問題

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 日期標題
            Row(
              children: [
                Icon(Icons.calendar_today, size: 20, color: colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  dateFormat.format(summary.date),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '已生成',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 摘要數據
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    icon: Icons.attach_money,
                    label: '總銷售額',
                    value: currencyFormat.format(summary.totalSales),
                    color: Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSummaryItem(
                    icon: Icons.receipt_long,
                    label: '交易筆數',
                    value: '${summary.transactionsCount} 筆',
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    icon: Icons.warning_amber,
                    label: '低庫存商品',
                    value: '${summary.lowStockProductsCount} 種',
                    color: summary.lowStockProductsCount > 0 ? Colors.orange : Colors.grey,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSummaryItem(
                    icon: Icons.schedule,
                    label: '生成時間',
                    value: DateFormat('HH:mm').format(summary.createdAt),
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: color.withOpacity(0.8),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: theme.textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold, color: color),
          ),
        ],
      ),
    );
  }
}
