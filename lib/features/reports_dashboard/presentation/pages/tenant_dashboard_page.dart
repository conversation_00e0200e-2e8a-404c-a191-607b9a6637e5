import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/daily_summary_providers.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/sales_trend_chart.dart';
import '../widgets/top_products_chart.dart';
import '../widgets/low_stock_summary_card.dart';
import '../../domain/entities/dashboard_data.dart';

/// 租戶儀表板頁面
class TenantDashboardPage extends ConsumerWidget {
  final String storeId;
  final String tenantId;

  const TenantDashboardPage({super.key, required this.storeId, required this.tenantId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardDataAsync = ref.watch(
      tenantDashboardDataProvider((storeId: storeId, tenantId: tenantId)),
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('我的儀表板'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(tenantDashboardDataProvider((storeId: storeId, tenantId: tenantId)));
            },
          ),
        ],
      ),
      body: dashboardDataAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error:
            (error, stackTrace) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Theme.of(context).colorScheme.error),
                  const SizedBox(height: 16),
                  Text('載入失敗', style: Theme.of(context).textTheme.headlineSmall),
                  const SizedBox(height: 8),
                  Text(
                    error.toString(),
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      ref.invalidate(
                        tenantDashboardDataProvider((storeId: storeId, tenantId: tenantId)),
                      );
                    },
                    child: const Text('重試'),
                  ),
                ],
              ),
            ),
        data:
            (dashboardData) => RefreshIndicator(
              onRefresh: () async {
                ref.invalidate(tenantDashboardDataProvider((storeId: storeId, tenantId: tenantId)));
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 概覽卡片
                    _buildOverviewCards(context, dashboardData),
                    const SizedBox(height: 24),

                    // 銷售趨勢圖表
                    SalesTrendChart(
                      data: dashboardData.salesTrendData,
                      title: '我的商品銷售趨勢 (最近7天)',
                      lineColor: Colors.orange,
                    ),
                    const SizedBox(height: 24),

                    // 最暢銷商品圖表
                    TopProductsChart(
                      data: dashboardData.topSellingProducts,
                      title: '我的最暢銷商品 (最近30天)',
                    ),
                  ],
                ),
              ),
            ),
      ),
    );
  }

  Widget _buildOverviewCards(BuildContext context, DashboardData data) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.3,
      children: [
        DashboardCard(
          title: '今日銷售額',
          value: formatCurrency(data.todayTotalSales),
          subtitle: '昨日: ${formatCurrency(data.yesterdayTotalSales)}',
          icon: Icons.attach_money,
          iconColor: Colors.green,
          changePercentage: data.salesChangePercentage,
        ),
        DashboardCard(
          title: '今日交易筆數',
          value: formatNumber(data.todayTransactionsCount),
          subtitle: '昨日: ${formatNumber(data.yesterdayTransactionsCount)}',
          icon: Icons.receipt_long,
          iconColor: Colors.blue,
          changePercentage: data.transactionsChangePercentage,
        ),
        LowStockSummaryCard(storeId: storeId, tenantId: tenantId),
        DashboardCard(
          title: '平均交易金額',
          value: formatCurrency(data.todayAverageTransactionAmount),
          subtitle: '昨日: ${formatCurrency(data.yesterdayAverageTransactionAmount)}',
          icon: Icons.trending_up,
          iconColor: Colors.purple,
        ),
      ],
    );
  }
}
