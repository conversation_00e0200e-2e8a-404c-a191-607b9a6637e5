import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../product_mgmt/domain/entities/product_entity.dart';
import '../../../product_mgmt/presentation/providers/product_providers.dart';
import '../../../tenant_mgmt/presentation/providers/grid_providers.dart';
import '../../../tenant_mgmt/domain/entities/grid_entity.dart';

/// 低庫存商品列表頁面
///
/// 支援管理員和租戶兩種視圖
class LowStockProductsPage extends ConsumerWidget {
  final String storeId;
  final String? tenantId; // null 表示管理員視圖

  const LowStockProductsPage({super.key, required this.storeId, this.tenantId});

  bool get isAdminView => tenantId == null;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 根據視圖類型選擇對應的 Provider
    final lowStockProductsAsync =
        isAdminView
            ? ref.watch(lowStockProductsByStoreProvider(storeId))
            : ref.watch(lowStockProductsByTenantProvider((storeId: storeId, tenantId: tenantId!)));

    // 獲取格位信息用於顯示格位代碼
    final gridsAsync = ref.watch(gridsByStoreIdProvider(storeId));

    return Scaffold(
      appBar: AppBar(
        title: Text(isAdminView ? '低庫存商品 (全店)' : '我的低庫存商品'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              if (isAdminView) {
                ref.invalidate(lowStockProductsByStoreProvider(storeId));
              } else {
                ref.invalidate(
                  lowStockProductsByTenantProvider((storeId: storeId, tenantId: tenantId!)),
                );
              }
            },
          ),
        ],
      ),
      body: lowStockProductsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => _buildErrorView(context, error),
        data:
            (products) => gridsAsync.when(
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stackTrace) => _buildProductsList(context, products, {}),
              data: (grids) {
                // 創建格位 ID 到格位實體的映射
                final gridMap = {for (var grid in grids) grid.id: grid};
                return _buildProductsList(context, products, gridMap);
              },
            ),
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, Object error) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
          const SizedBox(height: 16),
          Text('載入失敗', style: theme.textTheme.headlineSmall),
          const SizedBox(height: 8),
          Text(error.toString(), style: theme.textTheme.bodyMedium, textAlign: TextAlign.center),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('返回'),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsList(
    BuildContext context,
    List<ProductEntity> products,
    Map<String, GridEntity> gridMap,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (products.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2, size: 64, color: colorScheme.primary),
            const SizedBox(height: 16),
            Text('沒有低庫存商品', style: theme.textTheme.headlineSmall),
            const SizedBox(height: 8),
            Text(
              isAdminView ? '店鋪中所有商品庫存充足' : '您的商品庫存充足',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // 統計信息卡片
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: colorScheme.errorContainer,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(Icons.warning, color: colorScheme.onErrorContainer, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '低庫存警告',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: colorScheme.onErrorContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '發現 ${products.length} 個商品庫存不足',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onErrorContainer,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // 商品列表
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: products.length,
            itemBuilder: (context, index) {
              final product = products[index];
              final grid = gridMap[product.gridId];

              return _buildProductCard(context, product, grid);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProductCard(BuildContext context, ProductEntity product, GridEntity? grid) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final currencyFormat = NumberFormat.currency(locale: 'zh_TW', symbol: 'NT\$', decimalDigits: 0);

    // 計算庫存狀態
    final stockPercentage =
        product.lowStockLevel > 0 ? (product.stock / product.lowStockLevel).clamp(0.0, 1.0) : 0.0;

    final stockColor =
        stockPercentage <= 0.5
            ? colorScheme.error
            : stockPercentage <= 0.8
            ? Colors.orange
            : colorScheme.primary;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 商品基本信息
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.name,
                        style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'SKU: ${product.sku}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                      if (product.barcode.isNotEmpty) ...[
                        const SizedBox(height: 2),
                        Text(
                          '條碼: ${product.barcode}',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      currencyFormat.format(product.price),
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.primary,
                      ),
                    ),
                    if (grid != null) ...[
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: colorScheme.secondaryContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '格位: ${grid.code}',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSecondaryContainer,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 庫存信息
            Row(
              children: [
                Icon(Icons.inventory, size: 20, color: stockColor),
                const SizedBox(width: 8),
                Text(
                  '當前庫存: ${product.stock}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: stockColor,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  '低庫存水平: ${product.lowStockLevel}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // 庫存進度條
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '庫存狀態',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                    Text(
                      '${(stockPercentage * 100).toInt()}%',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: stockColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: stockPercentage,
                  backgroundColor: colorScheme.surfaceVariant,
                  valueColor: AlwaysStoppedAnimation<Color>(stockColor),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
