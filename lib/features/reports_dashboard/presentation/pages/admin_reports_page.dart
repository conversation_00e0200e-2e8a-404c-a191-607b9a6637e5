import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/daily_summary_generator.dart';
import 'admin_dashboard_page.dart';

/// 管理員報告頁面
/// 
/// 包含儀表板和報告生成工具
class AdminReportsPage extends ConsumerWidget {
  final String storeId;

  const AdminReportsPage({
    super.key,
    required this.storeId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('報告與儀表板'),
          bottom: const TabBar(
            tabs: [
              Tab(
                icon: Icon(Icons.dashboard),
                text: '儀表板',
              ),
              Tab(
                icon: Icon(Icons.settings),
                text: '報告管理',
              ),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            // 儀表板標籤
            AdminDashboardPage(storeId: storeId),
            
            // 報告管理標籤
            _buildReportsManagementTab(context),
          ],
        ),
      ),
    );
  }

  Widget _buildReportsManagementTab(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '報告管理工具',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '管理和生成各種報告數據',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          
          // 每日摘要生成器
          DailySummaryGenerator(storeId: storeId),
          
          const SizedBox(height: 24),
          
          // 其他報告工具（預留）
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.upcoming_outlined,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '即將推出',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '更多報告功能正在開發中：',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• PDF 報告生成\n'
                    '• 庫存報告\n'
                    '• 銷售分析報告\n'
                    '• 租戶業績報告',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
