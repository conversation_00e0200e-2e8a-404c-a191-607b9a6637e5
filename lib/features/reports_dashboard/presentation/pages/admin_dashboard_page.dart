import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/daily_summary_providers.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/sales_trend_chart.dart';
import '../widgets/top_products_chart.dart';
import '../../domain/entities/dashboard_data.dart';

/// 管理員儀表板頁面
class AdminDashboardPage extends ConsumerWidget {
  final String storeId;

  const AdminDashboardPage({
    super.key,
    required this.storeId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardDataAsync = ref.watch(adminDashboardDataProvider(storeId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('管理員儀表板'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(adminDashboardDataProvider(storeId));
            },
          ),
        ],
      ),
      body: dashboardDataAsync.when(
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                '載入失敗',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(adminDashboardDataProvider(storeId));
                },
                child: const Text('重試'),
              ),
            ],
          ),
        ),
        data: (dashboardData) => RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(adminDashboardDataProvider(storeId));
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 概覽卡片
                _buildOverviewCards(context, dashboardData),
                const SizedBox(height: 24),
                
                // 銷售趨勢圖表
                SalesTrendChart(
                  data: dashboardData.salesTrendData,
                  title: '銷售趨勢 (最近7天)',
                ),
                const SizedBox(height: 24),
                
                // 最暢銷商品圖表
                TopProductsChart(
                  data: dashboardData.topSellingProducts,
                  title: '最暢銷商品 (最近30天)',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewCards(BuildContext context, DashboardData data) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: [
        DashboardCard(
          title: '今日銷售額',
          value: formatCurrency(data.todayTotalSales),
          subtitle: '昨日: ${formatCurrency(data.yesterdayTotalSales)}',
          icon: Icons.attach_money,
          iconColor: Colors.green,
          changePercentage: data.salesChangePercentage,
        ),
        DashboardCard(
          title: '今日交易筆數',
          value: formatNumber(data.todayTransactionsCount),
          subtitle: '昨日: ${formatNumber(data.yesterdayTransactionsCount)}',
          icon: Icons.receipt_long,
          iconColor: Colors.blue,
          changePercentage: data.transactionsChangePercentage,
        ),
        DashboardCard(
          title: '低庫存商品',
          value: formatNumber(data.lowStockProductsCount),
          subtitle: '需要補貨',
          icon: Icons.inventory_2,
          iconColor: data.lowStockProductsCount > 0 ? Colors.orange : Colors.green,
        ),
        DashboardCard(
          title: '平均交易金額',
          value: formatCurrency(data.todayAverageTransactionAmount),
          subtitle: '昨日: ${formatCurrency(data.yesterdayAverageTransactionAmount)}',
          icon: Icons.trending_up,
          iconColor: Colors.purple,
        ),
      ],
    );
  }
}
