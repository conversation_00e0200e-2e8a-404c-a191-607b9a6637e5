import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/daily_summary_providers.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/sales_trend_chart.dart';
import '../widgets/top_products_chart.dart';
import '../widgets/low_stock_summary_card.dart';
import '../../domain/entities/dashboard_data.dart';

/// 管理員儀表板頁面
class AdminDashboardPage extends ConsumerWidget {
  final String storeId;

  const AdminDashboardPage({super.key, required this.storeId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardDataAsync = ref.watch(adminDashboardDataProvider(storeId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('管理員儀表板'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(adminDashboardDataProvider(storeId));
            },
          ),
        ],
      ),
      body: dashboardDataAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error:
            (error, stackTrace) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Theme.of(context).colorScheme.error),
                  const SizedBox(height: 16),
                  Text('載入失敗', style: Theme.of(context).textTheme.headlineSmall),
                  const SizedBox(height: 8),
                  Text(
                    error.toString(),
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      ref.invalidate(adminDashboardDataProvider(storeId));
                    },
                    child: const Text('重試'),
                  ),
                ],
              ),
            ),
        data:
            (dashboardData) => RefreshIndicator(
              onRefresh: () async {
                ref.invalidate(adminDashboardDataProvider(storeId));
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 歡迎標題
                    _buildWelcomeHeader(context),
                    const SizedBox(height: 24),

                    // 概覽卡片
                    _buildOverviewCards(context, dashboardData),
                    const SizedBox(height: 32),

                    // 銷售趨勢圖表
                    SalesTrendChart(data: dashboardData.salesTrendData, title: '銷售趨勢 (最近7天)'),
                    const SizedBox(height: 32),

                    // 最暢銷商品圖表
                    TopProductsChart(
                      data: dashboardData.topSellingProducts,
                      title: '最暢銷商品 (最近30天)',
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
      ),
    );
  }

  Widget _buildWelcomeHeader(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final now = DateTime.now();
    final hour = now.hour;

    String greeting;
    IconData greetingIcon;
    Color greetingColor;

    if (hour < 12) {
      greeting = '早上好！';
      greetingIcon = Icons.wb_sunny;
      greetingColor = Colors.orange;
    } else if (hour < 18) {
      greeting = '下午好！';
      greetingIcon = Icons.wb_sunny_outlined;
      greetingColor = Colors.blue;
    } else {
      greeting = '晚上好！';
      greetingIcon = Icons.nights_stay;
      greetingColor = Colors.indigo;
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [greetingColor.withOpacity(0.1), greetingColor.withOpacity(0.05)],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: greetingColor.withOpacity(0.2), width: 1),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: greetingColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(greetingIcon, color: greetingColor, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  greeting,
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '歡迎回到管理員儀表板',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '店鋪總覽',
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.onPrimaryContainer,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewCards(BuildContext context, DashboardData data) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 20,
      mainAxisSpacing: 20,
      childAspectRatio: 1.1,
      children: [
        DashboardCard(
          title: '今日銷售額',
          value: formatCurrency(data.todayTotalSales),
          subtitle: '昨日: ${formatCurrency(data.yesterdayTotalSales)}',
          icon: Icons.attach_money,
          iconColor: Colors.green,
          backgroundColor: Colors.green.withOpacity(0.05),
          changePercentage: data.salesChangePercentage,
        ),
        DashboardCard(
          title: '今日交易筆數',
          value: formatNumber(data.todayTransactionsCount),
          subtitle: '昨日: ${formatNumber(data.yesterdayTransactionsCount)}',
          icon: Icons.receipt_long,
          iconColor: Colors.blue,
          backgroundColor: Colors.blue.withOpacity(0.05),
          changePercentage: data.transactionsChangePercentage,
        ),
        LowStockSummaryCard(storeId: storeId),
        DashboardCard(
          title: '平均交易金額',
          value: formatCurrency(data.todayAverageTransactionAmount),
          subtitle: '昨日: ${formatCurrency(data.yesterdayAverageTransactionAmount)}',
          icon: Icons.trending_up,
          iconColor: Colors.purple,
          backgroundColor: Colors.purple.withOpacity(0.05),
        ),
      ],
    );
  }
}
