import 'package:flutter/material.dart';
import 'admin_dashboard_page.dart';
import 'tenant_dashboard_page.dart';

/// 儀表板演示頁面
/// 
/// 用於展示管理員和租戶儀表板的功能
class DashboardDemoPage extends StatelessWidget {
  const DashboardDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('儀表板演示'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '儀表板功能',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '• 管理員儀表板：查看整個店鋪的銷售數據和統計\n'
                      '• 租戶儀表板：查看自己商品的銷售數據\n'
                      '• 實時數據更新和圖表可視化\n'
                      '• 銷售趨勢分析和最暢銷商品統計',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const AdminDashboardPage(
                      storeId: 'demo_store_001',
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.admin_panel_settings),
              label: const Text('管理員儀表板'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.all(16),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const TenantDashboardPage(
                      storeId: 'demo_store_001',
                      tenantId: 'demo_tenant_001',
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.person),
              label: const Text('租戶儀表板'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.all(16),
              ),
            ),
            const SizedBox(height: 32),
            Card(
              color: Theme.of(context).colorScheme.surfaceVariant,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '注意事項',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      '• 演示使用模擬數據，實際使用時會從 Firestore 獲取真實數據\n'
                      '• 圖表數據會根據實際銷售記錄動態更新\n'
                      '• 低庫存警告會根據商品的庫存水平自動計算',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
