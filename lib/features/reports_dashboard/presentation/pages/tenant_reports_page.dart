import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/pdf_report_generator.dart';
import 'tenant_dashboard_page.dart';

/// 租戶報告頁面
/// 
/// 包含儀表板和報告生成工具
class TenantReportsPage extends ConsumerWidget {
  final String storeId;
  final String tenantId;

  const TenantReportsPage({
    super.key,
    required this.storeId,
    required this.tenantId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('我的報告'),
          bottom: const TabBar(
            tabs: [
              Tab(
                icon: Icon(Icons.dashboard),
                text: '儀表板',
              ),
              Tab(
                icon: Icon(Icons.description),
                text: '報告生成',
              ),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            // 儀表板標籤
            TenantDashboardPage(
              storeId: storeId,
              tenantId: tenantId,
            ),
            
            // 報告生成標籤
            _buildReportsTab(context),
          ],
        ),
      ),
    );
  }

  Widget _buildReportsTab(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '我的報告',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '生成和管理您的業務報告',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          
          // PDF 報告生成器
          PdfReportGenerator(
            storeId: storeId,
            tenantId: tenantId,
          ),
          
          const SizedBox(height: 24),
          
          // 報告說明
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.help_outline,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '報告說明',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '可用的報告類型：',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildReportTypeInfo(
                    context,
                    '庫存報告',
                    '查看您的商品庫存狀況，包括庫存數量、價值和低庫存警告',
                    Icons.inventory_2,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '使用說明：',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '1. 選擇要生成的報告類型\n'
                    '2. 點擊「預覽」查看報告內容\n'
                    '3. 點擊「生成 PDF」下載或分享報告\n'
                    '4. 報告將保存到您的設備並可以分享',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // 快速操作
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.flash_on,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '快速操作',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            // 導航到低庫存商品頁面
                            Navigator.of(context).pushNamed(
                              '/low-stock-products',
                              arguments: {
                                'storeId': storeId,
                                'tenantId': tenantId,
                              },
                            );
                          },
                          icon: const Icon(Icons.warning),
                          label: const Text('查看低庫存'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            // 導航到產品管理頁面
                            Navigator.of(context).pushNamed(
                              '/products',
                              arguments: {
                                'storeId': storeId,
                                'tenantId': tenantId,
                              },
                            );
                          },
                          icon: const Icon(Icons.inventory),
                          label: const Text('管理商品'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportTypeInfo(
    BuildContext context,
    String title,
    String description,
    IconData icon,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
