import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../domain/entities/daily_summary_entity.dart';
import '../../domain/entities/dashboard_data.dart';
import '../../domain/repositories/daily_summary_repository.dart';
import '../../domain/services/dashboard_service.dart';
import '../../domain/services/daily_summary_service.dart';
import '../../data/datasources/daily_summary_remote_ds.dart';
import '../../data/repositories/daily_summary_repository_impl.dart';
import '../../data/services/dashboard_service_impl.dart';
import '../../data/services/daily_summary_service_impl.dart';

/// 每日摘要遠程數據源 Provider
final dailySummaryRemoteDataSourceProvider = Provider<DailySummaryRemoteDataSource>((ref) {
  return DailySummaryRemoteDataSourceImpl(FirebaseFirestore.instance);
});

/// 每日摘要倉庫 Provider
final dailySummaryRepositoryProvider = Provider<DailySummaryRepository>((ref) {
  return DailySummaryRepositoryImpl(ref.read(dailySummaryRemoteDataSourceProvider));
});

/// 獲取指定店鋪和日期的每日摘要 Provider
final dailySummaryProvider =
    StreamProvider.family<DailySummaryEntity?, ({String storeId, DateTime date})>((ref, params) {
      return ref
          .watch(dailySummaryRepositoryProvider)
          .watchDailySummary(params.storeId, params.date);
    });

/// 獲取今日摘要 Provider
final todayDailySummaryProvider = StreamProvider.family<DailySummaryEntity?, String>((
  ref,
  storeId,
) {
  final today = DateTime.now();
  return ref.watch(dailySummaryRepositoryProvider).watchDailySummary(storeId, today);
});

/// 獲取昨日摘要 Provider
final yesterdayDailySummaryProvider = StreamProvider.family<DailySummaryEntity?, String>((
  ref,
  storeId,
) {
  final yesterday = DateTime.now().subtract(const Duration(days: 1));
  return ref.watch(dailySummaryRepositoryProvider).watchDailySummary(storeId, yesterday);
});

/// 獲取指定店鋪的每日摘要列表 Provider
final dailySummariesProvider = StreamProvider.family<
  List<DailySummaryEntity>,
  ({String storeId, DateTime? startDate, DateTime? endDate, int limit})
>((ref, params) {
  return ref
      .watch(dailySummaryRepositoryProvider)
      .watchDailySummaries(
        params.storeId,
        startDate: params.startDate,
        endDate: params.endDate,
        limit: params.limit,
      );
});

/// 獲取最近 7 天的每日摘要 Provider
final recentDailySummariesProvider = StreamProvider.family<List<DailySummaryEntity>, String>((
  ref,
  storeId,
) {
  final endDate = DateTime.now();
  final startDate = endDate.subtract(const Duration(days: 6)); // 最近 7 天

  return ref
      .watch(dailySummaryRepositoryProvider)
      .watchDailySummaries(storeId, startDate: startDate, endDate: endDate, limit: 7);
});

/// 獲取最近 30 天的每日摘要 Provider
final monthlyDailySummariesProvider = StreamProvider.family<List<DailySummaryEntity>, String>((
  ref,
  storeId,
) {
  final endDate = DateTime.now();
  final startDate = endDate.subtract(const Duration(days: 29)); // 最近 30 天

  return ref
      .watch(dailySummaryRepositoryProvider)
      .watchDailySummaries(storeId, startDate: startDate, endDate: endDate, limit: 30);
});

/// 每日摘要管理 StateNotifier
class DailySummaryNotifier extends StateNotifier<AsyncValue<DailySummaryEntity?>> {
  final DailySummaryRepository _repository;

  DailySummaryNotifier(this._repository) : super(const AsyncValue.loading());

  /// 創建或更新每日摘要
  Future<void> createOrUpdateDailySummary(DailySummaryEntity summary) async {
    state = const AsyncValue.loading();

    try {
      final updatedSummary = await _repository.createOrUpdateDailySummary(summary);
      state = AsyncValue.data(updatedSummary);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 刪除每日摘要
  Future<void> deleteDailySummary(String storeId, DateTime date) async {
    state = const AsyncValue.loading();

    try {
      await _repository.deleteDailySummary(storeId, date);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 檢查每日摘要是否存在
  Future<bool> dailySummaryExists(String storeId, DateTime date) async {
    try {
      return await _repository.dailySummaryExists(storeId, date);
    } catch (error) {
      return false;
    }
  }

  /// 重置狀態
  void reset() {
    state = const AsyncValue.loading();
  }
}

/// 每日摘要管理 Provider
final dailySummaryNotifierProvider =
    StateNotifierProvider<DailySummaryNotifier, AsyncValue<DailySummaryEntity?>>((ref) {
      return DailySummaryNotifier(ref.read(dailySummaryRepositoryProvider));
    });

/// 每日摘要存在性檢查 Provider
final dailySummaryExistsProvider = FutureProvider.family<bool, ({String storeId, DateTime date})>((
  ref,
  params,
) {
  return ref.read(dailySummaryRepositoryProvider).dailySummaryExists(params.storeId, params.date);
});

/// 最新每日摘要 Provider
final latestDailySummariesProvider =
    FutureProvider.family<List<DailySummaryEntity>, ({String storeId, int limit})>((ref, params) {
      return ref
          .read(dailySummaryRepositoryProvider)
          .getLatestDailySummaries(params.storeId, limit: params.limit);
    });

/// 儀表板服務 Provider
final dashboardServiceProvider = Provider<DashboardService>((ref) {
  return DashboardServiceImpl(FirebaseFirestore.instance);
});

/// 管理員儀表板數據 Provider
final adminDashboardDataProvider = FutureProvider.family<DashboardData, String>((ref, storeId) {
  return ref.read(dashboardServiceProvider).getAdminDashboardData(storeId);
});

/// 租戶儀表板數據 Provider
final tenantDashboardDataProvider =
    FutureProvider.family<DashboardData, ({String storeId, String tenantId})>((ref, params) {
      return ref
          .read(dashboardServiceProvider)
          .getTenantDashboardData(params.storeId, params.tenantId);
    });

/// 管理員儀表板數據監聽 Provider
final adminDashboardStreamProvider = StreamProvider.family<DashboardData, String>((ref, storeId) {
  return ref.read(dashboardServiceProvider).watchAdminDashboardData(storeId);
});

/// 租戶儀表板數據監聽 Provider
final tenantDashboardStreamProvider =
    StreamProvider.family<DashboardData, ({String storeId, String tenantId})>((ref, params) {
      return ref
          .read(dashboardServiceProvider)
          .watchTenantDashboardData(params.storeId, params.tenantId);
    });

/// 低庫存商品數量 Provider
final lowStockProductsCountProvider =
    FutureProvider.family<int, ({String storeId, String? tenantId})>((ref, params) {
      return ref
          .read(dashboardServiceProvider)
          .getLowStockProductsCount(params.storeId, tenantId: params.tenantId);
    });

/// 銷售趨勢數據 Provider
final salesTrendDataProvider =
    FutureProvider.family<List<SalesTrendData>, ({String storeId, String? tenantId, int days})>((
      ref,
      params,
    ) {
      return ref
          .read(dashboardServiceProvider)
          .getSalesTrendData(params.storeId, tenantId: params.tenantId, days: params.days);
    });

/// 最暢銷商品數據 Provider
final topSellingProductsProvider = FutureProvider.family<
  List<TopSellingProductData>,
  ({String storeId, String? tenantId, int limit})
>((ref, params) {
  return ref
      .read(dashboardServiceProvider)
      .getTopSellingProducts(params.storeId, tenantId: params.tenantId, limit: params.limit);
});

/// 每日摘要服務 Provider
final dailySummaryServiceProvider = Provider<DailySummaryService>((ref) {
  return DailySummaryServiceImpl(FirebaseFirestore.instance);
});
