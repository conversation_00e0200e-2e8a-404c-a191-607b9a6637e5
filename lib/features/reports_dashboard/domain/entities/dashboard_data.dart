import 'package:freezed_annotation/freezed_annotation.dart';

part 'dashboard_data.freezed.dart';

/// 儀表板數據實體
@freezed
abstract class DashboardData with _$DashboardData {
  const DashboardData._();

  const factory DashboardData({
    /// 今日總銷售額
    required double todayTotalSales,
    
    /// 昨日總銷售額
    required double yesterdayTotalSales,
    
    /// 今日交易筆數
    required int todayTransactionsCount,
    
    /// 昨日交易筆數
    required int yesterdayTransactionsCount,
    
    /// 低庫存商品數量
    required int lowStockProductsCount,
    
    /// 銷售趨勢數據（最近7天）
    required List<SalesTrendData> salesTrendData,
    
    /// 最暢銷商品數據
    required List<TopSellingProductData> topSellingProducts,
  }) = _DashboardData;

  /// 今日與昨日銷售額比較百分比
  double get salesChangePercentage {
    if (yesterdayTotalSales == 0) {
      return todayTotalSales > 0 ? 100.0 : 0.0;
    }
    return ((todayTotalSales - yesterdayTotalSales) / yesterdayTotalSales) * 100;
  }

  /// 今日與昨日交易筆數比較百分比
  double get transactionsChangePercentage {
    if (yesterdayTransactionsCount == 0) {
      return todayTransactionsCount > 0 ? 100.0 : 0.0;
    }
    return ((todayTransactionsCount - yesterdayTransactionsCount) / yesterdayTransactionsCount) * 100;
  }

  /// 今日平均交易金額
  double get todayAverageTransactionAmount {
    if (todayTransactionsCount == 0) return 0.0;
    return todayTotalSales / todayTransactionsCount;
  }

  /// 昨日平均交易金額
  double get yesterdayAverageTransactionAmount {
    if (yesterdayTransactionsCount == 0) return 0.0;
    return yesterdayTotalSales / yesterdayTransactionsCount;
  }
}

/// 銷售趨勢數據點
@freezed
abstract class SalesTrendData with _$SalesTrendData {
  const factory SalesTrendData({
    /// 日期
    required DateTime date,
    
    /// 銷售額
    required double sales,
    
    /// 交易筆數
    required int transactionsCount,
  }) = _SalesTrendData;
}

/// 最暢銷商品數據
@freezed
abstract class TopSellingProductData with _$TopSellingProductData {
  const factory TopSellingProductData({
    /// 商品 ID
    required String productId,
    
    /// 商品名稱
    required String productName,
    
    /// 商品 SKU
    required String productSku,
    
    /// 銷售數量
    required int quantitySold,
    
    /// 銷售總額
    required double totalSales,
  }) = _TopSellingProductData;
}

/// 空的儀表板數據
extension DashboardDataExtension on DashboardData {
  static DashboardData empty() {
    return const DashboardData(
      todayTotalSales: 0.0,
      yesterdayTotalSales: 0.0,
      todayTransactionsCount: 0,
      yesterdayTransactionsCount: 0,
      lowStockProductsCount: 0,
      salesTrendData: [],
      topSellingProducts: [],
    );
  }
}
