import 'package:freezed_annotation/freezed_annotation.dart';

part 'daily_summary_entity.freezed.dart';

/// 每日摘要實體
///
/// 對應 Firestore 中的 `daily_summaries/{yyyyMMdd_storeId}` 文檔
/// 用於存儲每日的銷售和庫存摘要數據
@freezed
abstract class DailySummaryEntity with _$DailySummaryEntity {
  const DailySummaryEntity._();

  const factory DailySummaryEntity({
    required String id,
    required DateTime date,
    required String storeId,
    required double totalSales,
    required int transactionsCount,
    required int lowStockProductsCount,
    required DateTime updatedAt,
    required DateTime createdAt,
  }) = _DailySummaryEntity;

  /// 生成文檔 ID
  /// 格式: yyyyMMdd_storeId
  static String generateDocumentId(DateTime date, String storeId) {
    final dateStr =
        '${date.year}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}';
    return '${dateStr}_$storeId';
  }

  /// 從文檔 ID 解析日期
  static DateTime? parseDateFromDocumentId(String documentId) {
    try {
      final parts = documentId.split('_');
      if (parts.length < 2) return null;

      final dateStr = parts[0];
      if (dateStr.length != 8) return null;

      final year = int.parse(dateStr.substring(0, 4));
      final month = int.parse(dateStr.substring(4, 6));
      final day = int.parse(dateStr.substring(6, 8));

      return DateTime(year, month, day);
    } catch (e) {
      return null;
    }
  }

  /// 從文檔 ID 解析店鋪 ID
  static String? parseStoreIdFromDocumentId(String documentId) {
    try {
      final parts = documentId.split('_');
      if (parts.length < 2) return null;

      final dateStr = parts[0];
      if (dateStr.length != 8) return null;

      // 驗證日期部分是否為有效數字
      if (int.tryParse(dateStr) == null) return null;

      return parts.sublist(1).join('_'); // 處理店鋪 ID 中可能包含下劃線的情況
    } catch (e) {
      return null;
    }
  }

  /// 檢查是否為今日摘要
  bool get isToday {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  /// 檢查是否為昨日摘要
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year &&
        date.month == yesterday.month &&
        date.day == yesterday.day;
  }

  /// 格式化日期為顯示字符串
  String get formattedDate {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 平均每筆交易金額
  double get averageTransactionAmount {
    if (transactionsCount == 0) return 0.0;
    return totalSales / transactionsCount;
  }
}
