// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'daily_summary_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$DailySummaryEntity {

 String get id; DateTime get date; String get storeId; double get totalSales; int get transactionsCount; int get lowStockProductsCount; DateTime get updatedAt; DateTime get createdAt;
/// Create a copy of DailySummaryEntity
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DailySummaryEntityCopyWith<DailySummaryEntity> get copyWith => _$DailySummaryEntityCopyWithImpl<DailySummaryEntity>(this as DailySummaryEntity, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DailySummaryEntity&&(identical(other.id, id) || other.id == id)&&(identical(other.date, date) || other.date == date)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.transactionsCount, transactionsCount) || other.transactionsCount == transactionsCount)&&(identical(other.lowStockProductsCount, lowStockProductsCount) || other.lowStockProductsCount == lowStockProductsCount)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}


@override
int get hashCode => Object.hash(runtimeType,id,date,storeId,totalSales,transactionsCount,lowStockProductsCount,updatedAt,createdAt);

@override
String toString() {
  return 'DailySummaryEntity(id: $id, date: $date, storeId: $storeId, totalSales: $totalSales, transactionsCount: $transactionsCount, lowStockProductsCount: $lowStockProductsCount, updatedAt: $updatedAt, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $DailySummaryEntityCopyWith<$Res>  {
  factory $DailySummaryEntityCopyWith(DailySummaryEntity value, $Res Function(DailySummaryEntity) _then) = _$DailySummaryEntityCopyWithImpl;
@useResult
$Res call({
 String id, DateTime date, String storeId, double totalSales, int transactionsCount, int lowStockProductsCount, DateTime updatedAt, DateTime createdAt
});




}
/// @nodoc
class _$DailySummaryEntityCopyWithImpl<$Res>
    implements $DailySummaryEntityCopyWith<$Res> {
  _$DailySummaryEntityCopyWithImpl(this._self, this._then);

  final DailySummaryEntity _self;
  final $Res Function(DailySummaryEntity) _then;

/// Create a copy of DailySummaryEntity
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? date = null,Object? storeId = null,Object? totalSales = null,Object? transactionsCount = null,Object? lowStockProductsCount = null,Object? updatedAt = null,Object? createdAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,storeId: null == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as String,totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,transactionsCount: null == transactionsCount ? _self.transactionsCount : transactionsCount // ignore: cast_nullable_to_non_nullable
as int,lowStockProductsCount: null == lowStockProductsCount ? _self.lowStockProductsCount : lowStockProductsCount // ignore: cast_nullable_to_non_nullable
as int,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// @nodoc


class _DailySummaryEntity extends DailySummaryEntity {
  const _DailySummaryEntity({required this.id, required this.date, required this.storeId, required this.totalSales, required this.transactionsCount, required this.lowStockProductsCount, required this.updatedAt, required this.createdAt}): super._();
  

@override final  String id;
@override final  DateTime date;
@override final  String storeId;
@override final  double totalSales;
@override final  int transactionsCount;
@override final  int lowStockProductsCount;
@override final  DateTime updatedAt;
@override final  DateTime createdAt;

/// Create a copy of DailySummaryEntity
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DailySummaryEntityCopyWith<_DailySummaryEntity> get copyWith => __$DailySummaryEntityCopyWithImpl<_DailySummaryEntity>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DailySummaryEntity&&(identical(other.id, id) || other.id == id)&&(identical(other.date, date) || other.date == date)&&(identical(other.storeId, storeId) || other.storeId == storeId)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.transactionsCount, transactionsCount) || other.transactionsCount == transactionsCount)&&(identical(other.lowStockProductsCount, lowStockProductsCount) || other.lowStockProductsCount == lowStockProductsCount)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}


@override
int get hashCode => Object.hash(runtimeType,id,date,storeId,totalSales,transactionsCount,lowStockProductsCount,updatedAt,createdAt);

@override
String toString() {
  return 'DailySummaryEntity(id: $id, date: $date, storeId: $storeId, totalSales: $totalSales, transactionsCount: $transactionsCount, lowStockProductsCount: $lowStockProductsCount, updatedAt: $updatedAt, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$DailySummaryEntityCopyWith<$Res> implements $DailySummaryEntityCopyWith<$Res> {
  factory _$DailySummaryEntityCopyWith(_DailySummaryEntity value, $Res Function(_DailySummaryEntity) _then) = __$DailySummaryEntityCopyWithImpl;
@override @useResult
$Res call({
 String id, DateTime date, String storeId, double totalSales, int transactionsCount, int lowStockProductsCount, DateTime updatedAt, DateTime createdAt
});




}
/// @nodoc
class __$DailySummaryEntityCopyWithImpl<$Res>
    implements _$DailySummaryEntityCopyWith<$Res> {
  __$DailySummaryEntityCopyWithImpl(this._self, this._then);

  final _DailySummaryEntity _self;
  final $Res Function(_DailySummaryEntity) _then;

/// Create a copy of DailySummaryEntity
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? date = null,Object? storeId = null,Object? totalSales = null,Object? transactionsCount = null,Object? lowStockProductsCount = null,Object? updatedAt = null,Object? createdAt = null,}) {
  return _then(_DailySummaryEntity(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,storeId: null == storeId ? _self.storeId : storeId // ignore: cast_nullable_to_non_nullable
as String,totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,transactionsCount: null == transactionsCount ? _self.transactionsCount : transactionsCount // ignore: cast_nullable_to_non_nullable
as int,lowStockProductsCount: null == lowStockProductsCount ? _self.lowStockProductsCount : lowStockProductsCount // ignore: cast_nullable_to_non_nullable
as int,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
