// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dashboard_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$DashboardData {

/// 今日總銷售額
 double get todayTotalSales;/// 昨日總銷售額
 double get yesterdayTotalSales;/// 今日交易筆數
 int get todayTransactionsCount;/// 昨日交易筆數
 int get yesterdayTransactionsCount;/// 低庫存商品數量
 int get lowStockProductsCount;/// 銷售趨勢數據（最近7天）
 List<SalesTrendData> get salesTrendData;/// 最暢銷商品數據
 List<TopSellingProductData> get topSellingProducts;
/// Create a copy of DashboardData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DashboardDataCopyWith<DashboardData> get copyWith => _$DashboardDataCopyWithImpl<DashboardData>(this as DashboardData, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DashboardData&&(identical(other.todayTotalSales, todayTotalSales) || other.todayTotalSales == todayTotalSales)&&(identical(other.yesterdayTotalSales, yesterdayTotalSales) || other.yesterdayTotalSales == yesterdayTotalSales)&&(identical(other.todayTransactionsCount, todayTransactionsCount) || other.todayTransactionsCount == todayTransactionsCount)&&(identical(other.yesterdayTransactionsCount, yesterdayTransactionsCount) || other.yesterdayTransactionsCount == yesterdayTransactionsCount)&&(identical(other.lowStockProductsCount, lowStockProductsCount) || other.lowStockProductsCount == lowStockProductsCount)&&const DeepCollectionEquality().equals(other.salesTrendData, salesTrendData)&&const DeepCollectionEquality().equals(other.topSellingProducts, topSellingProducts));
}


@override
int get hashCode => Object.hash(runtimeType,todayTotalSales,yesterdayTotalSales,todayTransactionsCount,yesterdayTransactionsCount,lowStockProductsCount,const DeepCollectionEquality().hash(salesTrendData),const DeepCollectionEquality().hash(topSellingProducts));

@override
String toString() {
  return 'DashboardData(todayTotalSales: $todayTotalSales, yesterdayTotalSales: $yesterdayTotalSales, todayTransactionsCount: $todayTransactionsCount, yesterdayTransactionsCount: $yesterdayTransactionsCount, lowStockProductsCount: $lowStockProductsCount, salesTrendData: $salesTrendData, topSellingProducts: $topSellingProducts)';
}


}

/// @nodoc
abstract mixin class $DashboardDataCopyWith<$Res>  {
  factory $DashboardDataCopyWith(DashboardData value, $Res Function(DashboardData) _then) = _$DashboardDataCopyWithImpl;
@useResult
$Res call({
 double todayTotalSales, double yesterdayTotalSales, int todayTransactionsCount, int yesterdayTransactionsCount, int lowStockProductsCount, List<SalesTrendData> salesTrendData, List<TopSellingProductData> topSellingProducts
});




}
/// @nodoc
class _$DashboardDataCopyWithImpl<$Res>
    implements $DashboardDataCopyWith<$Res> {
  _$DashboardDataCopyWithImpl(this._self, this._then);

  final DashboardData _self;
  final $Res Function(DashboardData) _then;

/// Create a copy of DashboardData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? todayTotalSales = null,Object? yesterdayTotalSales = null,Object? todayTransactionsCount = null,Object? yesterdayTransactionsCount = null,Object? lowStockProductsCount = null,Object? salesTrendData = null,Object? topSellingProducts = null,}) {
  return _then(_self.copyWith(
todayTotalSales: null == todayTotalSales ? _self.todayTotalSales : todayTotalSales // ignore: cast_nullable_to_non_nullable
as double,yesterdayTotalSales: null == yesterdayTotalSales ? _self.yesterdayTotalSales : yesterdayTotalSales // ignore: cast_nullable_to_non_nullable
as double,todayTransactionsCount: null == todayTransactionsCount ? _self.todayTransactionsCount : todayTransactionsCount // ignore: cast_nullable_to_non_nullable
as int,yesterdayTransactionsCount: null == yesterdayTransactionsCount ? _self.yesterdayTransactionsCount : yesterdayTransactionsCount // ignore: cast_nullable_to_non_nullable
as int,lowStockProductsCount: null == lowStockProductsCount ? _self.lowStockProductsCount : lowStockProductsCount // ignore: cast_nullable_to_non_nullable
as int,salesTrendData: null == salesTrendData ? _self.salesTrendData : salesTrendData // ignore: cast_nullable_to_non_nullable
as List<SalesTrendData>,topSellingProducts: null == topSellingProducts ? _self.topSellingProducts : topSellingProducts // ignore: cast_nullable_to_non_nullable
as List<TopSellingProductData>,
  ));
}

}


/// @nodoc


class _DashboardData extends DashboardData {
  const _DashboardData({required this.todayTotalSales, required this.yesterdayTotalSales, required this.todayTransactionsCount, required this.yesterdayTransactionsCount, required this.lowStockProductsCount, required final  List<SalesTrendData> salesTrendData, required final  List<TopSellingProductData> topSellingProducts}): _salesTrendData = salesTrendData,_topSellingProducts = topSellingProducts,super._();
  

/// 今日總銷售額
@override final  double todayTotalSales;
/// 昨日總銷售額
@override final  double yesterdayTotalSales;
/// 今日交易筆數
@override final  int todayTransactionsCount;
/// 昨日交易筆數
@override final  int yesterdayTransactionsCount;
/// 低庫存商品數量
@override final  int lowStockProductsCount;
/// 銷售趨勢數據（最近7天）
 final  List<SalesTrendData> _salesTrendData;
/// 銷售趨勢數據（最近7天）
@override List<SalesTrendData> get salesTrendData {
  if (_salesTrendData is EqualUnmodifiableListView) return _salesTrendData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_salesTrendData);
}

/// 最暢銷商品數據
 final  List<TopSellingProductData> _topSellingProducts;
/// 最暢銷商品數據
@override List<TopSellingProductData> get topSellingProducts {
  if (_topSellingProducts is EqualUnmodifiableListView) return _topSellingProducts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_topSellingProducts);
}


/// Create a copy of DashboardData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DashboardDataCopyWith<_DashboardData> get copyWith => __$DashboardDataCopyWithImpl<_DashboardData>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DashboardData&&(identical(other.todayTotalSales, todayTotalSales) || other.todayTotalSales == todayTotalSales)&&(identical(other.yesterdayTotalSales, yesterdayTotalSales) || other.yesterdayTotalSales == yesterdayTotalSales)&&(identical(other.todayTransactionsCount, todayTransactionsCount) || other.todayTransactionsCount == todayTransactionsCount)&&(identical(other.yesterdayTransactionsCount, yesterdayTransactionsCount) || other.yesterdayTransactionsCount == yesterdayTransactionsCount)&&(identical(other.lowStockProductsCount, lowStockProductsCount) || other.lowStockProductsCount == lowStockProductsCount)&&const DeepCollectionEquality().equals(other._salesTrendData, _salesTrendData)&&const DeepCollectionEquality().equals(other._topSellingProducts, _topSellingProducts));
}


@override
int get hashCode => Object.hash(runtimeType,todayTotalSales,yesterdayTotalSales,todayTransactionsCount,yesterdayTransactionsCount,lowStockProductsCount,const DeepCollectionEquality().hash(_salesTrendData),const DeepCollectionEquality().hash(_topSellingProducts));

@override
String toString() {
  return 'DashboardData(todayTotalSales: $todayTotalSales, yesterdayTotalSales: $yesterdayTotalSales, todayTransactionsCount: $todayTransactionsCount, yesterdayTransactionsCount: $yesterdayTransactionsCount, lowStockProductsCount: $lowStockProductsCount, salesTrendData: $salesTrendData, topSellingProducts: $topSellingProducts)';
}


}

/// @nodoc
abstract mixin class _$DashboardDataCopyWith<$Res> implements $DashboardDataCopyWith<$Res> {
  factory _$DashboardDataCopyWith(_DashboardData value, $Res Function(_DashboardData) _then) = __$DashboardDataCopyWithImpl;
@override @useResult
$Res call({
 double todayTotalSales, double yesterdayTotalSales, int todayTransactionsCount, int yesterdayTransactionsCount, int lowStockProductsCount, List<SalesTrendData> salesTrendData, List<TopSellingProductData> topSellingProducts
});




}
/// @nodoc
class __$DashboardDataCopyWithImpl<$Res>
    implements _$DashboardDataCopyWith<$Res> {
  __$DashboardDataCopyWithImpl(this._self, this._then);

  final _DashboardData _self;
  final $Res Function(_DashboardData) _then;

/// Create a copy of DashboardData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? todayTotalSales = null,Object? yesterdayTotalSales = null,Object? todayTransactionsCount = null,Object? yesterdayTransactionsCount = null,Object? lowStockProductsCount = null,Object? salesTrendData = null,Object? topSellingProducts = null,}) {
  return _then(_DashboardData(
todayTotalSales: null == todayTotalSales ? _self.todayTotalSales : todayTotalSales // ignore: cast_nullable_to_non_nullable
as double,yesterdayTotalSales: null == yesterdayTotalSales ? _self.yesterdayTotalSales : yesterdayTotalSales // ignore: cast_nullable_to_non_nullable
as double,todayTransactionsCount: null == todayTransactionsCount ? _self.todayTransactionsCount : todayTransactionsCount // ignore: cast_nullable_to_non_nullable
as int,yesterdayTransactionsCount: null == yesterdayTransactionsCount ? _self.yesterdayTransactionsCount : yesterdayTransactionsCount // ignore: cast_nullable_to_non_nullable
as int,lowStockProductsCount: null == lowStockProductsCount ? _self.lowStockProductsCount : lowStockProductsCount // ignore: cast_nullable_to_non_nullable
as int,salesTrendData: null == salesTrendData ? _self._salesTrendData : salesTrendData // ignore: cast_nullable_to_non_nullable
as List<SalesTrendData>,topSellingProducts: null == topSellingProducts ? _self._topSellingProducts : topSellingProducts // ignore: cast_nullable_to_non_nullable
as List<TopSellingProductData>,
  ));
}


}

/// @nodoc
mixin _$SalesTrendData {

/// 日期
 DateTime get date;/// 銷售額
 double get sales;/// 交易筆數
 int get transactionsCount;
/// Create a copy of SalesTrendData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SalesTrendDataCopyWith<SalesTrendData> get copyWith => _$SalesTrendDataCopyWithImpl<SalesTrendData>(this as SalesTrendData, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SalesTrendData&&(identical(other.date, date) || other.date == date)&&(identical(other.sales, sales) || other.sales == sales)&&(identical(other.transactionsCount, transactionsCount) || other.transactionsCount == transactionsCount));
}


@override
int get hashCode => Object.hash(runtimeType,date,sales,transactionsCount);

@override
String toString() {
  return 'SalesTrendData(date: $date, sales: $sales, transactionsCount: $transactionsCount)';
}


}

/// @nodoc
abstract mixin class $SalesTrendDataCopyWith<$Res>  {
  factory $SalesTrendDataCopyWith(SalesTrendData value, $Res Function(SalesTrendData) _then) = _$SalesTrendDataCopyWithImpl;
@useResult
$Res call({
 DateTime date, double sales, int transactionsCount
});




}
/// @nodoc
class _$SalesTrendDataCopyWithImpl<$Res>
    implements $SalesTrendDataCopyWith<$Res> {
  _$SalesTrendDataCopyWithImpl(this._self, this._then);

  final SalesTrendData _self;
  final $Res Function(SalesTrendData) _then;

/// Create a copy of SalesTrendData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? date = null,Object? sales = null,Object? transactionsCount = null,}) {
  return _then(_self.copyWith(
date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,sales: null == sales ? _self.sales : sales // ignore: cast_nullable_to_non_nullable
as double,transactionsCount: null == transactionsCount ? _self.transactionsCount : transactionsCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc


class _SalesTrendData implements SalesTrendData {
  const _SalesTrendData({required this.date, required this.sales, required this.transactionsCount});
  

/// 日期
@override final  DateTime date;
/// 銷售額
@override final  double sales;
/// 交易筆數
@override final  int transactionsCount;

/// Create a copy of SalesTrendData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SalesTrendDataCopyWith<_SalesTrendData> get copyWith => __$SalesTrendDataCopyWithImpl<_SalesTrendData>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SalesTrendData&&(identical(other.date, date) || other.date == date)&&(identical(other.sales, sales) || other.sales == sales)&&(identical(other.transactionsCount, transactionsCount) || other.transactionsCount == transactionsCount));
}


@override
int get hashCode => Object.hash(runtimeType,date,sales,transactionsCount);

@override
String toString() {
  return 'SalesTrendData(date: $date, sales: $sales, transactionsCount: $transactionsCount)';
}


}

/// @nodoc
abstract mixin class _$SalesTrendDataCopyWith<$Res> implements $SalesTrendDataCopyWith<$Res> {
  factory _$SalesTrendDataCopyWith(_SalesTrendData value, $Res Function(_SalesTrendData) _then) = __$SalesTrendDataCopyWithImpl;
@override @useResult
$Res call({
 DateTime date, double sales, int transactionsCount
});




}
/// @nodoc
class __$SalesTrendDataCopyWithImpl<$Res>
    implements _$SalesTrendDataCopyWith<$Res> {
  __$SalesTrendDataCopyWithImpl(this._self, this._then);

  final _SalesTrendData _self;
  final $Res Function(_SalesTrendData) _then;

/// Create a copy of SalesTrendData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? date = null,Object? sales = null,Object? transactionsCount = null,}) {
  return _then(_SalesTrendData(
date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,sales: null == sales ? _self.sales : sales // ignore: cast_nullable_to_non_nullable
as double,transactionsCount: null == transactionsCount ? _self.transactionsCount : transactionsCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc
mixin _$TopSellingProductData {

/// 商品 ID
 String get productId;/// 商品名稱
 String get productName;/// 商品 SKU
 String get productSku;/// 銷售數量
 int get quantitySold;/// 銷售總額
 double get totalSales;
/// Create a copy of TopSellingProductData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TopSellingProductDataCopyWith<TopSellingProductData> get copyWith => _$TopSellingProductDataCopyWithImpl<TopSellingProductData>(this as TopSellingProductData, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TopSellingProductData&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.productSku, productSku) || other.productSku == productSku)&&(identical(other.quantitySold, quantitySold) || other.quantitySold == quantitySold)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales));
}


@override
int get hashCode => Object.hash(runtimeType,productId,productName,productSku,quantitySold,totalSales);

@override
String toString() {
  return 'TopSellingProductData(productId: $productId, productName: $productName, productSku: $productSku, quantitySold: $quantitySold, totalSales: $totalSales)';
}


}

/// @nodoc
abstract mixin class $TopSellingProductDataCopyWith<$Res>  {
  factory $TopSellingProductDataCopyWith(TopSellingProductData value, $Res Function(TopSellingProductData) _then) = _$TopSellingProductDataCopyWithImpl;
@useResult
$Res call({
 String productId, String productName, String productSku, int quantitySold, double totalSales
});




}
/// @nodoc
class _$TopSellingProductDataCopyWithImpl<$Res>
    implements $TopSellingProductDataCopyWith<$Res> {
  _$TopSellingProductDataCopyWithImpl(this._self, this._then);

  final TopSellingProductData _self;
  final $Res Function(TopSellingProductData) _then;

/// Create a copy of TopSellingProductData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? productId = null,Object? productName = null,Object? productSku = null,Object? quantitySold = null,Object? totalSales = null,}) {
  return _then(_self.copyWith(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,productSku: null == productSku ? _self.productSku : productSku // ignore: cast_nullable_to_non_nullable
as String,quantitySold: null == quantitySold ? _self.quantitySold : quantitySold // ignore: cast_nullable_to_non_nullable
as int,totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// @nodoc


class _TopSellingProductData implements TopSellingProductData {
  const _TopSellingProductData({required this.productId, required this.productName, required this.productSku, required this.quantitySold, required this.totalSales});
  

/// 商品 ID
@override final  String productId;
/// 商品名稱
@override final  String productName;
/// 商品 SKU
@override final  String productSku;
/// 銷售數量
@override final  int quantitySold;
/// 銷售總額
@override final  double totalSales;

/// Create a copy of TopSellingProductData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TopSellingProductDataCopyWith<_TopSellingProductData> get copyWith => __$TopSellingProductDataCopyWithImpl<_TopSellingProductData>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TopSellingProductData&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.productSku, productSku) || other.productSku == productSku)&&(identical(other.quantitySold, quantitySold) || other.quantitySold == quantitySold)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales));
}


@override
int get hashCode => Object.hash(runtimeType,productId,productName,productSku,quantitySold,totalSales);

@override
String toString() {
  return 'TopSellingProductData(productId: $productId, productName: $productName, productSku: $productSku, quantitySold: $quantitySold, totalSales: $totalSales)';
}


}

/// @nodoc
abstract mixin class _$TopSellingProductDataCopyWith<$Res> implements $TopSellingProductDataCopyWith<$Res> {
  factory _$TopSellingProductDataCopyWith(_TopSellingProductData value, $Res Function(_TopSellingProductData) _then) = __$TopSellingProductDataCopyWithImpl;
@override @useResult
$Res call({
 String productId, String productName, String productSku, int quantitySold, double totalSales
});




}
/// @nodoc
class __$TopSellingProductDataCopyWithImpl<$Res>
    implements _$TopSellingProductDataCopyWith<$Res> {
  __$TopSellingProductDataCopyWithImpl(this._self, this._then);

  final _TopSellingProductData _self;
  final $Res Function(_TopSellingProductData) _then;

/// Create a copy of TopSellingProductData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? productId = null,Object? productName = null,Object? productSku = null,Object? quantitySold = null,Object? totalSales = null,}) {
  return _then(_TopSellingProductData(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,productSku: null == productSku ? _self.productSku : productSku // ignore: cast_nullable_to_non_nullable
as String,quantitySold: null == quantitySold ? _self.quantitySold : quantitySold // ignore: cast_nullable_to_non_nullable
as int,totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
