import '../entities/daily_summary_entity.dart';

/// 每日摘要倉庫接口
/// 
/// 定義每日摘要數據的存取操作
abstract class DailySummaryRepository {
  /// 獲取指定店鋪和日期的每日摘要
  /// 
  /// [storeId] 店鋪 ID
  /// [date] 日期
  /// 
  /// 返回 [DailySummaryEntity] 或 null（如果不存在）
  Future<DailySummaryEntity?> getDailySummary(String storeId, DateTime date);

  /// 監聽指定店鋪和日期的每日摘要
  /// 
  /// [storeId] 店鋪 ID
  /// [date] 日期
  /// 
  /// 返回 [DailySummaryEntity] 的 Stream，如果不存在則發出 null
  Stream<DailySummaryEntity?> watchDailySummary(String storeId, DateTime date);

  /// 獲取指定店鋪的每日摘要列表
  /// 
  /// [storeId] 店鋪 ID
  /// [startDate] 開始日期（包含）
  /// [endDate] 結束日期（包含）
  /// [limit] 限制數量，默認為 30
  /// 
  /// 返回 [DailySummaryEntity] 列表，按日期降序排列
  Future<List<DailySummaryEntity>> getDailySummaries(
    String storeId, {
    DateTime? startDate,
    DateTime? endDate,
    int limit = 30,
  });

  /// 監聽指定店鋪的每日摘要列表
  /// 
  /// [storeId] 店鋪 ID
  /// [startDate] 開始日期（包含）
  /// [endDate] 結束日期（包含）
  /// [limit] 限制數量，默認為 30
  /// 
  /// 返回 [DailySummaryEntity] 列表的 Stream，按日期降序排列
  Stream<List<DailySummaryEntity>> watchDailySummaries(
    String storeId, {
    DateTime? startDate,
    DateTime? endDate,
    int limit = 30,
  });

  /// 創建或更新每日摘要
  /// 
  /// [summary] 每日摘要實體
  /// 
  /// 返回創建或更新的摘要實體
  Future<DailySummaryEntity> createOrUpdateDailySummary(DailySummaryEntity summary);

  /// 刪除指定店鋪和日期的每日摘要
  /// 
  /// [storeId] 店鋪 ID
  /// [date] 日期
  Future<void> deleteDailySummary(String storeId, DateTime date);

  /// 檢查指定店鋪和日期的每日摘要是否存在
  /// 
  /// [storeId] 店鋪 ID
  /// [date] 日期
  /// 
  /// 返回是否存在
  Future<bool> dailySummaryExists(String storeId, DateTime date);

  /// 獲取最新的每日摘要
  /// 
  /// [storeId] 店鋪 ID
  /// [limit] 限制數量，默認為 1
  /// 
  /// 返回最新的 [DailySummaryEntity] 列表
  Future<List<DailySummaryEntity>> getLatestDailySummaries(
    String storeId, {
    int limit = 1,
  });
}
