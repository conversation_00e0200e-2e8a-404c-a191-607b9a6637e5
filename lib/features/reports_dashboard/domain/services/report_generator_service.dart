import 'dart:typed_data';

/// PDF 報告生成服務接口
abstract class ReportGeneratorService {
  /// 生成每日銷售報告 PDF
  ///
  /// [storeId] 店鋪 ID
  /// [date] 報告日期
  ///
  /// 使用銷售數據或預計算的每日摘要生成 PDF
  /// 返回 PDF 文件的字節數據
  Future<Uint8List> generateDailySalesReportPdf(String storeId, DateTime date);

  /// 生成庫存報告 PDF
  ///
  /// [storeId] 店鋪 ID
  /// [tenantId] 租戶 ID（可選，null 表示全店庫存）
  ///
  /// 列出商品、庫存水平、價格等信息
  /// 可以是全店或特定租戶的庫存報告
  /// 返回 PDF 文件的字節數據
  Future<Uint8List> generateInventoryReportPdf(String storeId, {String? tenantId});
}
