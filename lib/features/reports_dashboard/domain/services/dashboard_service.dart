import '../entities/dashboard_data.dart';

/// 儀表板數據服務接口
abstract class DashboardService {
  /// 獲取管理員儀表板數據
  /// 
  /// [storeId] 店鋪 ID
  /// 
  /// 返回包含整個店鋪數據的儀表板數據
  Future<DashboardData> getAdminDashboardData(String storeId);

  /// 獲取租戶儀表板數據
  /// 
  /// [storeId] 店鋪 ID
  /// [tenantId] 租戶 ID
  /// 
  /// 返回包含租戶自己產品數據的儀表板數據
  Future<DashboardData> getTenantDashboardData(String storeId, String tenantId);

  /// 監聽管理員儀表板數據
  /// 
  /// [storeId] 店鋪 ID
  /// 
  /// 返回儀表板數據的 Stream
  Stream<DashboardData> watchAdminDashboardData(String storeId);

  /// 監聽租戶儀表板數據
  /// 
  /// [storeId] 店鋪 ID
  /// [tenantId] 租戶 ID
  /// 
  /// 返回儀表板數據的 Stream
  Stream<DashboardData> watchTenantDashboardData(String storeId, String tenantId);

  /// 獲取低庫存商品數量
  /// 
  /// [storeId] 店鋪 ID
  /// [tenantId] 租戶 ID（可選，如果提供則只計算該租戶的商品）
  /// 
  /// 返回低庫存商品數量
  Future<int> getLowStockProductsCount(String storeId, {String? tenantId});

  /// 獲取銷售趨勢數據
  /// 
  /// [storeId] 店鋪 ID
  /// [tenantId] 租戶 ID（可選）
  /// [days] 天數，默認為 7 天
  /// 
  /// 返回銷售趨勢數據列表
  Future<List<SalesTrendData>> getSalesTrendData(
    String storeId, {
    String? tenantId,
    int days = 7,
  });

  /// 獲取最暢銷商品數據
  /// 
  /// [storeId] 店鋪 ID
  /// [tenantId] 租戶 ID（可選）
  /// [limit] 限制數量，默認為 5
  /// 
  /// 返回最暢銷商品數據列表
  Future<List<TopSellingProductData>> getTopSellingProducts(
    String storeId, {
    String? tenantId,
    int limit = 5,
  });
}
