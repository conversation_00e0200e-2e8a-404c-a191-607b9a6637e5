/// 每日摘要生成服務接口
abstract class DailySummaryService {
  /// 生成指定日期的每日摘要
  /// 
  /// [storeId] 店鋪 ID
  /// [date] 要生成摘要的日期
  /// 
  /// 讀取該日期的所有銷售數據，聚合後寫入或更新 daily_summaries 集合
  Future<void> generateDailySummary(String storeId, DateTime date);

  /// 批量生成多日摘要
  /// 
  /// [storeId] 店鋪 ID
  /// [startDate] 開始日期
  /// [endDate] 結束日期
  /// 
  /// 為指定日期範圍內的每一天生成摘要
  Future<void> generateDailySummariesBatch(
    String storeId,
    DateTime startDate,
    DateTime endDate,
  );

  /// 檢查指定日期是否已有摘要
  /// 
  /// [storeId] 店鋪 ID
  /// [date] 要檢查的日期
  /// 
  /// 返回是否已存在該日期的摘要
  Future<bool> hasDailySummary(String storeId, DateTime date);

  /// 重新生成指定日期的摘要（覆蓋現有數據）
  /// 
  /// [storeId] 店鋪 ID
  /// [date] 要重新生成摘要的日期
  /// 
  /// 強制重新計算並覆蓋現有摘要數據
  Future<void> regenerateDailySummary(String storeId, DateTime date);
}
