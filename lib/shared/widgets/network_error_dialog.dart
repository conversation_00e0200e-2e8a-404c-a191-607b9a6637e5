import 'package:flutter/material.dart';
import 'package:grid_pos/shared/widgets/app_button.dart';

/// A dialog that shows when there's no internet connection.
class NetworkErrorDialog extends StatelessWidget {
  final VoidCallback onRetry;

  const NetworkErrorDialog({super.key, required this.onRetry});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.wifi_off_rounded, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text('No Internet Connection', style: Theme.of(context).textTheme.titleLarge, textAlign: TextAlign.center),
            const SizedBox(height: 8),
            Text('Please check your internet connection and try again.', style: Theme.of(context).textTheme.bodyMedium, textAlign: TextAlign.center),
            const Sized<PERSON>ox(height: 24),
            App<PERSON>utton(text: 'Retry', onPressed: onRetry, width: double.infinity),
          ],
        ),
      ),
    );
  }
}
