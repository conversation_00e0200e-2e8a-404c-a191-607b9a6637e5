import 'package:flutter/material.dart';

/// A reusable button widget that follows Material Design guidelines.
/// Supports different variants: primary, secondary, and text.
class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final AppButtonVariant variant;
  final bool isLoading;
  final double? width;
  final double height;
  final EdgeInsetsGeometry padding;

  const AppButton({
    super.key,
    required this.text,
    this.onPressed,
    this.variant = AppButtonVariant.primary,
    this.isLoading = false,
    this.width,
    this.height = 48.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0),
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    Widget buttonChild =
        isLoading
            ? SizedBox(
              height: 24.0,
              width: 24.0,
              child: CircularProgressIndicator(
                strokeWidth: 2.0,
                valueColor: AlwaysStoppedAnimation<Color>(variant == AppButtonVariant.primary ? theme.colorScheme.onPrimary : theme.colorScheme.primary),
              ),
            )
            : Text(text, style: theme.textTheme.labelLarge?.copyWith(color: variant == AppButtonVariant.primary ? theme.colorScheme.onPrimary : theme.colorScheme.primary));

    switch (variant) {
      case AppButtonVariant.primary:
        return SizedBox(
          width: width,
          height: height,
          child: ElevatedButton(
            onPressed: isLoading ? null : onPressed,
            style: ElevatedButton.styleFrom(backgroundColor: theme.colorScheme.primary, padding: padding, shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0))),
            child: buttonChild,
          ),
        );

      case AppButtonVariant.secondary:
        return SizedBox(
          width: width,
          height: height,
          child: OutlinedButton(
            onPressed: isLoading ? null : onPressed,
            style: OutlinedButton.styleFrom(padding: padding, shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0))),
            child: buttonChild,
          ),
        );

      case AppButtonVariant.text:
        return SizedBox(
          width: width,
          height: height,
          child: TextButton(
            onPressed: isLoading ? null : onPressed,
            style: TextButton.styleFrom(padding: padding, shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0))),
            child: buttonChild,
          ),
        );
    }
  }
}

/// The visual variant of the [AppButton].
enum AppButtonVariant {
  /// A filled button with the primary color.
  primary,

  /// An outlined button with the primary color.
  secondary,

  /// A text-only button with the primary color.
  text,
}
