import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';

/// A reusable widget to display QR codes
class QrCodeDisplay extends StatelessWidget {
  /// The data to encode in the QR code
  final String data;

  /// The size of the QR code
  final double size;

  /// Optional label to display below the QR code
  final String? label;

  /// Creates a QR code display widget
  const QrCodeDisplay({super.key, required this.data, this.size = 100.0, this.label});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        QrImageView(data: data, version: QrVersions.auto, size: size, gapless: false),
        if (label != null && label!.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(label!, style: Theme.of(context).textTheme.bodySmall, textAlign: TextAlign.center),
        ],
      ],
    );
  }
}
