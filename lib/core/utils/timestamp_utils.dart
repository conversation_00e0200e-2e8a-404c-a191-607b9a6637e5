import 'package:cloud_firestore/cloud_firestore.dart';

/// Utility class for timestamp conversions between Firestore and Dart
class TimestampUtils {
  /// Private constructor to prevent instantiation
  TimestampUtils._();

  /// Convert Firestore Timestamp to DateTime
  static DateTime? timestampToDateTime(Timestamp? timestamp) => timestamp?.toDate();

  /// Convert DateTime to Firestore Timestamp
  static Timestamp? dateTimeToTimestamp(DateTime? dateTime) => dateTime != null ? Timestamp.fromDate(dateTime) : null;

  /// Get current timestamp
  static Timestamp now() => Timestamp.now();

  /// Format a DateTime as a string in ISO 8601 format
  static String formatDateTime(DateTime dateTime) => dateTime.toIso8601String();

  /// Parse a string in ISO 8601 format to DateTime
  static DateTime? parseDateTime(String? dateTimeString) {
    if (dateTimeString == null || dateTimeString.isEmpty) return null;
    try {
      return DateTime.parse(dateTimeString);
    } catch (_) {
      return null;
    }
  }
}
