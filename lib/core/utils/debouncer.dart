import 'dart:async';

import 'package:flutter/foundation.dart';

/// A debouncer that delays the execution of a function
/// until after a specified duration has elapsed.
class Debouncer {
  /// The duration to wait before executing the function
  final Duration delay;

  /// The timer that keeps track of the delay
  Timer? _timer;

  /// Creates a debouncer with the given delay
  Debouncer({this.delay = const Duration(milliseconds: 500)});

  /// Runs the given function after the delay
  void run(VoidCallback action) {
    _timer?.cancel();
    _timer = Timer(delay, action);
  }

  /// Cancels the timer if it's active
  void dispose() {
    _timer?.cancel();
    _timer = null;
  }
}
