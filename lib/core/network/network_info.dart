import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A provider that monitors the network connectivity status.
final networkInfoProvider = StateNotifierProvider<NetworkInfo, bool>((ref) {
  return NetworkInfo();
});

/// A class that monitors network connectivity status using connectivity_plus.
class NetworkInfo extends StateNotifier<bool> {
  NetworkInfo() : super(true) {
    _init();
  }

  late final Connectivity _connectivity;
  StreamSubscription<List<ConnectivityResult>>? _subscription;

  void _init() {
    _connectivity = Connectivity();
    checkConnectivity();
    _setupConnectivityStream();
  }

  Future<void> checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      state = result != ConnectivityResult.none;
    } catch (e) {
      state = false;
    }
  }

  void _setupConnectivityStream() {
    _subscription = _connectivity.onConnectivityChanged.listen((results) {
      state = !results.contains(ConnectivityResult.none);
    });
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }
}
