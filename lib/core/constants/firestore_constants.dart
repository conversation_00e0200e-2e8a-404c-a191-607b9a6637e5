/// Constants for Firestore collections, fields, and queries
class FirestoreConstants {
  /// Private constructor to prevent instantiation
  FirestoreConstants._();

  // Collection names
  /// Root collections
  static const String users = 'users';
  static const String stores = 'stores';
  static const String dailySummaries = 'daily_summaries';
  static const String globalCounters = 'global_counters';

  // Subcollection names
  /// Subcollections under stores
  static const String tenants = 'tenants';
  static const String grids = 'grids';
  static const String sales = 'sales';

  /// Subcollections under tenants
  static const String products = 'products';
  static const String counters = 'counters';

  /// Subcollections under products
  static const String locks = 'locks';

  // Document IDs
  /// Counter document IDs
  static const String productSkuCounter = 'productSku';

  // Field names
  /// Common fields
  static const String id = 'id';
  static const String createdAt = 'createdAt';
  static const String updatedAt = 'updatedAt';
  static const String active = 'active';

  /// Store fields
  static const String name = 'name';
  static const String address = 'address';
  static const String timezone = 'timezone';
  static const String gridCount = 'gridCount';

  /// Tenant fields
  static const String contact = 'contact';
  static const String gridsArray = 'grids';
  static const String contract = 'contract';

  /// Product fields
  static const String sku = 'sku';
  static const String barcode = 'barcode';
  static const String price = 'price';
  static const String cost = 'cost';
  static const String stock = 'stock';
  static const String gridId = 'gridId';
  static const String lowStockLevel = 'lowStockLevel';
  static const String storeId = 'storeId';
  static const String tenantId = 'tenantId';

  /// Grid fields
  static const String code = 'code';
  static const String size = 'size';

  /// Sales fields
  static const String cashierId = 'cashierId';
  static const String status = 'status';
  static const String totalAmount = 'totalAmount';
  static const String items = 'items';
  static const String paymentType = 'paymentType';
  static const String printed = 'printed';

  /// Lock fields
  static const String byUserId = 'byUserId';
  static const String byDeviceId = 'byDeviceId';
  static const String expire = 'expire';

  /// User fields
  static const String email = 'email';
  static const String role = 'role';
  static const String displayName = 'displayName';

  // User roles
  /// User role constants
  static const String roleAdmin = 'admin';
  static const String roleTenant = 'tenant';
  static const String roleCashier = 'cashier';
  static const String rolePendingApproval = 'pending_approval';
}
