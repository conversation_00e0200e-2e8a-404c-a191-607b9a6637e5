import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart'; // For context.push
import 'package:grid_pos/core/constants/app_constants.dart'; // 導入應用常量
import 'package:grid_pos/features/auth/presentation/providers/auth_provider.dart'; // For authNotifierProvider, AuthState
import 'package:grid_pos/features/tenant_mgmt/presentation/widgets/store_selector.dart'; // For StoreSelector
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart'; // For selectedStoreIdProvider and kNoValidStoreSelectedId
import 'package:grid_pos/shared/widgets/custom_snackbar.dart'; // For showErrorSnackBar

// Make the class public
class ModernDashboardScreen extends ConsumerWidget {
  final String userRole;

  const ModernDashboardScreen({super.key, required this.userRole});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<AuthState>(authNotifierProvider, (previous, current) {
      if (current.errorMessage != null && previous?.errorMessage != current.errorMessage) {
        showErrorSnackBar(context, current.errorMessage!);
      }
    });

    final authState = ref.watch(authNotifierProvider);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      // backgroundColor: colorScheme.surface,
      appBar: AppBar(
        // elevation: 10,
        title: Text(
          AppConstants.appNameWithVersion,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          if (userRole == 'admin')
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 8),
              child: const StoreSelector(),
            ),
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: IconButton(
              icon:
                  authState.isLoading
                      ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: colorScheme.onPrimary,
                        ),
                      )
                      : const Icon(Icons.logout_rounded),
              tooltip: '退出登錄',
              onPressed:
                  authState.isLoading
                      ? null
                      : () async {
                        try {
                          await ref.read(authNotifierProvider.notifier).signOut();
                        } catch (e) {
                          if (context.mounted) {
                            showErrorSnackBar(context, '退出登錄失敗: ${e.toString()}');
                          }
                        }
                      },
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeHeader(context, userRole),
            const SizedBox(height: 32),
            _buildRoleSpecificContent(context, userRole, colorScheme, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeHeader(BuildContext context, String userRole) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    String roleDisplayName = _getRoleDisplayName(userRole);
    String greeting = _getGreeting();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            greeting,
            style: theme.textTheme.headlineSmall?.copyWith(
              color: colorScheme.onPrimaryContainer,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: colorScheme.primary,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  roleDisplayName,
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'admin':
        return '系統管理員';
      case 'tenant':
        return '租戶管理員';
      case 'cashier':
        return '收銀員';
      default:
        return '待審覈用戶';
    }
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return '早上好！';
    } else if (hour < 18) {
      return '下午好！';
    } else {
      return '晚上好！';
    }
  }

  Widget _buildRoleSpecificContent(
    BuildContext context,
    String userRole,
    ColorScheme colorScheme,
    WidgetRef ref,
  ) {
    switch (userRole) {
      case 'admin':
        return _buildAdminDashboard(context, colorScheme, ref);
      case 'tenant':
        return _buildTenantDashboard(context, colorScheme, ref);
      case 'cashier':
        return _buildCashierDashboard(context, colorScheme);
      default:
        return _buildPendingDashboard(context, colorScheme);
    }
  }

  Widget _buildAdminDashboard(BuildContext context, ColorScheme colorScheme, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '管理功能',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        ListView(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            _buildDashboardListItem(
              context: context,
              icon: Icons.people_rounded,
              title: '用戶管理',
              subtitle: '新增、編輯和刪除系統用戶，管理權限',
              color: Colors.blue,
              onTap: () => context.push('/admin/users'),
            ),
            const SizedBox(height: 12),
            _buildDashboardListItem(
              context: context,
              icon: Icons.store_rounded,
              title: '租戶管理',
              subtitle: '處理租戶合約、格位分配和租金設定',
              color: Colors.green,
              onTap: () => context.push('/tenants'),
            ),
            const SizedBox(height: 12),
            _buildDashboardListItem(
              context: context,
              icon: Icons.grid_4x4_rounded,
              title: '網格管理',
              subtitle: '規劃和調整展示區域的網格佈局',
              color: Colors.orange,
              onTap: () => context.push('/grids'),
            ),
            const SizedBox(height: 12),
            _buildDashboardListItem(
              context: context,
              icon: Icons.inventory_2_rounded,
              title: '商品管理',
              subtitle: '設定商品類別、價格和庫存規則',
              color: Colors.purple,
              onTap: () => context.push('/products'),
            ),
            const SizedBox(height: 12),
            _buildDashboardListItem(
              context: context,
              icon: Icons.storefront_rounded,
              title: '門店管理',
              subtitle: '設定分店資訊、營業時間和區域劃分',
              color: Colors.teal,
              onTap: () => context.push('/admin/stores'),
            ),
            const SizedBox(height: 12),
            _buildDashboardListItem(
              context: context,
              icon: Icons.point_of_sale_rounded,
              title: '銷售終端',
              subtitle: '進行商品銷售、處理付款和列印收據',
              color: Colors.red,
              onTap: () => context.push('/pos'),
            ),
            const SizedBox(height: 12),
            _buildDashboardListItem(
              context: context,
              icon: Icons.receipt_long_rounded,
              title: '收據管理',
              subtitle: '查看歷史交易記錄和生成報表',
              color: Colors.amber,
              onTap: () => context.push('/admin/receipts'),
            ),
            const SizedBox(height: 12),
            _buildDashboardListItem(
              context: context,
              icon: Icons.analytics_rounded,
              title: '報告與儀表板',
              subtitle: '查看銷售數據、庫存報告和業務分析',
              color: Colors.indigo,
              onTap: () {
                final selectedStoreId = ref.read(selectedStoreIdProvider);
                if (selectedStoreId != kNoValidStoreSelectedId) {
                  context.push('/reports', extra: {'storeId': selectedStoreId});
                } else {
                  context.push('/reports');
                }
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTenantDashboard(BuildContext context, ColorScheme colorScheme, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '租戶功能',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.1,
          children: [
            _buildDashboardCard(
              context: context,
              icon: Icons.inventory_2_rounded,
              title: '商品管理',
              subtitle: '管理您的商品',
              color: Colors.blue,
              onTap: () => context.push('/products'),
            ),
            _buildDashboardCard(
              context: context,
              icon: Icons.analytics_rounded,
              title: '我的報告',
              subtitle: '查看銷售數據和庫存報告',
              color: Colors.indigo,
              onTap: () {
                final currentUser = ref.read(currentUserProvider);
                if (currentUser?.storeId != null && currentUser?.tenantId != null) {
                  context.push(
                    '/reports',
                    extra: {'storeId': currentUser!.storeId!, 'tenantId': currentUser.tenantId!},
                  );
                } else {
                  showErrorSnackBar(context, '無法獲取用戶信息，請重新登錄');
                }
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCashierDashboard(BuildContext context, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '收銀功能',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.1,
          children: [
            _buildDashboardCard(
              context: context,
              icon: Icons.point_of_sale_rounded,
              title: '銷售終端',
              subtitle: '開始收銀服務',
              color: Colors.green,
              onTap: () => context.push('/pos'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPendingDashboard(BuildContext context, ColorScheme colorScheme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: colorScheme.errorContainer.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Icon(Icons.pending_actions_rounded, size: 64, color: colorScheme.primary),
          const SizedBox(height: 16),
          Text(
            '賬戶待審覈',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '您的賬戶正在等待管理員審覈，請耐心等待。',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: colorScheme.onSurface.withOpacity(0.7)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardCard({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            color: colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: colorScheme.outline.withOpacity(0.1)),
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, size: 32, color: color),
                ),
                const SizedBox(height: 16),
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withOpacity(0.6),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDashboardListItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            color: colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: colorScheme.outline.withOpacity(0.1)),
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, size: 24, color: color),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(Icons.chevron_right, color: colorScheme.onSurface.withOpacity(0.3)),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
