import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:grid_pos/core/utils/logger.dart';
import 'package:grid_pos/features/admin/presentation/pages/user_management_page.dart';
import 'package:grid_pos/features/auth/presentation/pages/forgot_password_page.dart';
import 'package:grid_pos/features/auth/presentation/pages/login_page.dart';
import 'package:grid_pos/features/auth/presentation/pages/signup_page.dart';
import 'package:grid_pos/features/auth/presentation/providers/auth_provider.dart';
// Import the new dashboard screen
import 'package:grid_pos/core/router/modern_dashboard_screen.dart'; // Adjust path as needed
// Import POS and Scan pages with aliases to avoid conflicts
import 'package:grid_pos/features/pos/presentation/pages/pos_page.dart' as pos;
import 'package:grid_pos/features/pos/presentation/pages/scan_page.dart' as scan;
import 'package:grid_pos/features/product_mgmt/domain/entities/product_entity.dart';
import 'package:grid_pos/features/product_mgmt/presentation/pages/product_form_page.dart';
import 'package:grid_pos/features/product_mgmt/presentation/pages/product_list_page.dart';
import 'package:grid_pos/features/store_mgmt/domain/entities/store_entity.dart';
import 'package:grid_pos/features/store_mgmt/presentation/pages/store_form_page.dart';
import 'package:grid_pos/features/store_mgmt/presentation/pages/store_list_page.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/grid_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/tenant_entity.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/pages/grid_form_page.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/pages/grid_list_page.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/pages/tenant_form_page.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/pages/tenant_list_page.dart';
// Import the receipt list page
import 'package:grid_pos/features/pos/presentation/pages/receipt_list_page.dart';

/// Provider for the app router
final appRouterProvider = Provider<GoRouter>((ref) {
  final isAuthenticated = ref.watch(isAuthenticatedProvider);
  final userRole = ref.watch(userRoleProvider);

  return GoRouter(
    debugLogDiagnostics: true,
    initialLocation: '/login',
    routes: [
      // Auth routes
      GoRoute(path: '/login', builder: (context, state) => const LoginPage()),
      GoRoute(path: '/signup', builder: (context, state) => const SignupPage()),
      GoRoute(path: '/forgot-password', builder: (context, state) => const ForgotPasswordPage()),

      // Main app routes
      GoRoute(path: '/', builder: (context, state) => ModernDashboardScreen(userRole: userRole)), // Use the imported screen
      // Admin routes
      GoRoute(path: '/admin/users', builder: (context, state) => const UserManagementPage()),
      GoRoute(path: '/admin/receipts', builder: (context, state) => const ReceiptListPage()),

      // Store management routes
      GoRoute(path: '/admin/stores', builder: (context, state) => const StoreListPage()),
      GoRoute(path: '/admin/stores/new', builder: (context, state) => const StoreFormPage()),
      GoRoute(
        path: '/admin/stores/:id/edit',
        builder: (context, state) {
          final store = state.extra as StoreEntity?;
          return StoreFormPage(store: store);
        },
      ),

      // Tenant management routes
      GoRoute(path: '/tenants', builder: (context, state) => const TenantListPage()),
      GoRoute(path: '/tenants/new', builder: (context, state) => const TenantFormPage()),
      GoRoute(
        path: '/tenants/:id/edit',
        builder: (context, state) {
          final tenant = state.extra as TenantEntity?;
          return TenantFormPage(tenant: tenant);
        },
      ),

      // Grid management routes
      GoRoute(path: '/grids', builder: (context, state) => const GridListPage()),
      GoRoute(path: '/grids/new', builder: (context, state) => const GridFormPage()),
      GoRoute(
        path: '/grids/:id/edit',
        builder: (context, state) {
          final gridId = state.pathParameters['id']!;
          final grid = state.extra as GridEntity?;
          return GridFormPage.edit(grid: grid, gridId: gridId);
        },
      ),
      // New route for grid form
      GoRoute(
        path: '/grid-form',
        builder: (context, state) {
          final Map<String, dynamic>? params = state.extra as Map<String, dynamic>?;
          if (params != null && params.containsKey('gridId')) {
            // 編輯現有格位
            return GridFormPage.edit(gridId: params['gridId'] as String);
          } else {
            // 創建新格位
            return const GridFormPage();
          }
        },
      ),

      // Product management routes
      GoRoute(path: '/products', builder: (context, state) => const ProductListPage()),
      GoRoute(
        path: '/products/new',
        builder: (context, state) {
          final Map<String, String>? args = state.extra as Map<String, String>?;
          final String? preSelectedTenantId = args?['tenantId'];
          return ProductFormPage(preSelectedTenantId: preSelectedTenantId);
        },
      ),
      GoRoute(
        path: '/products/:id/edit',
        builder: (context, state) {
          final productIdFromPath = state.pathParameters['id']!;
          ProductEntity? productFromExtra = state.extra as ProductEntity?;

          Logger.debug('路由 /products/:id/edit:');
          Logger.debug('  productIdFromPath: $productIdFromPath');
          Logger.debug('  productFromExtra: ${productFromExtra?.id} (名稱: ${productFromExtra?.name})');

          if (productFromExtra != null && productFromExtra.id != productIdFromPath) {
            Logger.warning('  不匹配！來自extra的產品ID (${productFromExtra.id}) 與路徑ID ($productIdFromPath) 不匹配。丟棄extra。');
            productFromExtra = null;
          }

          return ProductFormPage(product: productFromExtra, productIdForEdit: productIdFromPath);
        },
      ),

      // POS routes - using aliases to avoid conflicts
      GoRoute(path: '/pos', builder: (context, state) => const pos.POSPage()),
      GoRoute(path: '/scan', builder: (context, state) => const scan.ScanPage()),
    ],
    errorBuilder:
        (context, state) => Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                const SizedBox(height: 16),
                Text('頁面未找到', style: Theme.of(context).textTheme.headlineSmall),
                const SizedBox(height: 8),
                Text('錯誤: ${state.error}', style: Theme.of(context).textTheme.bodyMedium),
                const SizedBox(height: 24),
                ElevatedButton(onPressed: () => context.go('/'), child: const Text('返回首頁')),
              ],
            ),
          ),
        ),
    redirect: (context, state) {
      final isLoginRoute = state.matchedLocation == '/login';
      final isSignupRoute = state.matchedLocation == '/signup';
      final isForgotPasswordRoute = state.matchedLocation == '/forgot-password';
      final isAuthRoute = isLoginRoute || isSignupRoute || isForgotPasswordRoute;

      final isAdminRoute = state.matchedLocation.startsWith('/admin');
      final isAdmin = userRole == 'admin';

      if (isAuthenticated && isAuthRoute) {
        return '/';
      }

      if (!isAuthenticated && !isAuthRoute) {
        return '/login';
      }

      if (isAuthenticated && isAdminRoute && !isAdmin) {
        return '/';
      }

      return null;
    },
    refreshListenable: GoRouterRefreshStream(ref.read(authStateProvider.stream)),
  );
});

/// A [Listenable] implementation that refreshes when a stream emits a new value.
class GoRouterRefreshStream extends ChangeNotifier {
  /// Creates a [GoRouterRefreshStream] with the given [stream]
  GoRouterRefreshStream(Stream<dynamic> stream) {
    _subscription = stream.asBroadcastStream().listen((_) => notifyListeners());
  }

  late final dynamic _subscription;

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}
