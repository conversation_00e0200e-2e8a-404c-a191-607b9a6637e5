/// Base class for domain-level failures
abstract class Failure {
  final String message;
  final dynamic originalError;

  const Failure({required this.message, this.originalError});

  @override
  String toString() => message;
}

/// Network-related failures
class NetworkFailure extends Failure {
  const NetworkFailure({required super.message, super.originalError});

  factory NetworkFailure.noConnection() => const NetworkFailure(message: 'No internet connection available');
}

/// Firestore-related failures
class FirestoreFailure extends Failure {
  const FirestoreFailure({required super.message, super.originalError});

  factory FirestoreFailure.documentNotFound() => const FirestoreFailure(message: 'The requested document was not found');

  factory FirestoreFailure.permissionDenied() => const FirestoreFailure(message: 'Permission denied. You do not have access to this resource');

  factory FirestoreFailure.serverError() => const FirestoreFailure(message: 'A server error occurred. Please try again later');

  factory FirestoreFailure.unknown(dynamic error) => FirestoreFailure(message: 'An unexpected error occurred', originalError: error);
}

/// Authentication-related failures
class AuthFailure extends Failure {
  const AuthFailure({required super.message, super.originalError});

  factory AuthFailure.invalidEmail() => const AuthFailure(message: 'The email address is not valid');

  factory AuthFailure.wrongPassword() => const AuthFailure(message: 'Incorrect password');

  factory AuthFailure.userNotFound() => const AuthFailure(message: 'No user found with this email');

  factory AuthFailure.userDisabled() => const AuthFailure(message: 'This user account has been disabled');

  factory AuthFailure.tooManyRequests() => const AuthFailure(message: 'Too many requests. Please try again later');

  factory AuthFailure.operationNotAllowed() => const AuthFailure(message: 'This operation is not allowed');

  factory AuthFailure.emailAlreadyInUse() => const AuthFailure(message: 'An account already exists with this email');

  factory AuthFailure.unknown(dynamic error) => AuthFailure(message: 'An authentication error occurred', originalError: error);
}

/// Validation failures
class ValidationFailure extends Failure {
  const ValidationFailure({required super.message, super.originalError});

  factory ValidationFailure.emptyField(String fieldName) => ValidationFailure(message: '$fieldName cannot be empty');

  factory ValidationFailure.invalidFormat(String fieldName) => ValidationFailure(message: '$fieldName format is invalid');

  factory ValidationFailure.duplicateValue(String fieldName) => ValidationFailure(message: 'This $fieldName already exists');
}

/// Business logic failures
class BusinessFailure extends Failure {
  const BusinessFailure({required super.message, super.originalError});

  factory BusinessFailure.insufficientStock() => const BusinessFailure(message: 'Insufficient stock available');

  factory BusinessFailure.cannotDeleteAssignedGrid() => const BusinessFailure(message: 'Cannot delete a grid assigned to a tenant');
}
