import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/core/constants/app_constants.dart';
import 'package:grid_pos/core/network/network_info.dart';
import 'package:grid_pos/core/router/app_router.dart';
import 'package:grid_pos/features/tenant_mgmt/presentation/providers/store_provider.dart';
import 'package:grid_pos/shared/widgets/network_error_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize SharedPreferences
  final sharedPreferences = await SharedPreferences.getInstance();

  runApp(
    ProviderScope(
      overrides: [
        // Override the sharedPreferencesProvider with the actual instance
        sharedPreferencesProvider.overrideWithValue(sharedPreferences),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isConnected = ref.watch(networkInfoProvider);
    final router = ref.watch(appRouterProvider);

    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      title: AppConstants.appName,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      routerConfig: router,
      builder: (context, child) {
        if (!isConnected) {
          return const NetworkErrorScreen();
        }
        return child ?? const SizedBox.shrink();
      },
    );
  }
}

class NetworkErrorScreen extends ConsumerWidget {
  const NetworkErrorScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Center(
        child: NetworkErrorDialog(
          onRetry: () {
            ref.read(networkInfoProvider.notifier).checkConnectivity();
          },
        ),
      ),
    );
  }
}
