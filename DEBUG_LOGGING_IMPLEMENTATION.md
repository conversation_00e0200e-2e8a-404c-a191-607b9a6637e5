# Debug Logging Implementation

## 🎯 **目標**

為報告儀表板功能添加全面的錯誤日誌記錄，幫助開發者在控制台中追蹤和調試問題。

## 📋 **實施範圍**

### 1. 每日摘要查看頁面
**文件**: `lib/features/reports_dashboard/presentation/pages/daily_summaries_page.dart`

**添加的日誌**:
```dart
error: (error, stack) {
  // Debug logging for error tracking
  Logger.error('Error loading daily summaries', error);
  Logger.debug('Stack trace: $stack');
  
  return Card(/* error UI */);
},
```

**日誌輸出示例**:
```
ERROR: Error loading daily summaries
Error details: [core/no-app] No Firebase App '[DEFAULT]' has been created - call Firebase.initializeApp()
DEBUG: Stack trace: #1 ProviderElementBase.watch...
```

### 2. 每日摘要生成器
**文件**: `lib/features/reports_dashboard/presentation/widgets/daily_summary_generator.dart`

**添加的日誌**:
```dart
} catch (e) {
  // Debug logging for error tracking
  Logger.error(
    'Failed to generate daily summary for ${DateFormat('yyyy-MM-dd').format(_selectedDate)}',
    e,
  );
  
  if (mounted) {
    ScaffoldMessenger.of(context).showSnackBar(/* error message */);
  }
}
```

**日誌輸出示例**:
```
ERROR: Failed to generate daily summary for 2024-12-01
Error details: [cloud_firestore/permission-denied] Missing or insufficient permissions.
```

### 3. PDF 報告生成器
**文件**: `lib/features/reports_dashboard/presentation/widgets/pdf_report_generator.dart`

**添加的日誌**:
```dart
// 生成 PDF 錯誤
} catch (e) {
  // Debug logging for error tracking
  Logger.error('Failed to generate PDF report: ${_getReportTypeName(_selectedReportType)}', e);
  
  if (mounted) {
    ScaffoldMessenger.of(context).showSnackBar(/* error message */);
  }
}

// 預覽 PDF 錯誤
} catch (e) {
  // Debug logging for error tracking
  Logger.error('Failed to preview PDF report: ${_getReportTypeName(_selectedReportType)}', e);
  
  if (mounted) {
    ScaffoldMessenger.of(context).showSnackBar(/* error message */);
  }
}
```

**日誌輸出示例**:
```
ERROR: Failed to generate PDF report: 每日銷售報告
Error details: [core/network-error] Network request failed
```

### 4. 儀表板服務 (已存在)
**文件**: `lib/features/reports_dashboard/data/services/dashboard_service_impl.dart`

**現有的完整日誌**:
- ✅ 管理員儀表板數據錯誤
- ✅ 租戶儀表板數據錯誤
- ✅ 低庫存商品計數錯誤
- ✅ 銷售趨勢數據錯誤
- ✅ 最暢銷商品錯誤
- ✅ 指定日期銷售數據錯誤

## 🔧 **技術實現**

### Logger 類使用
所有錯誤日誌都使用項目中統一的 `Logger` 類：

```dart
import '../../../../core/utils/logger.dart';

// 錯誤日誌
Logger.error('Error message', errorObject);

// 調試日誌
Logger.debug('Debug information');
```

### 日誌級別
- **Logger.error()**: 用於捕獲的異常和錯誤
- **Logger.debug()**: 用於調試信息和堆疊追蹤

### 日誌格式
錯誤日誌包含：
1. **描述性訊息**: 說明發生錯誤的操作
2. **錯誤對象**: 完整的錯誤詳情
3. **堆疊追蹤**: 錯誤發生的調用堆疊（適用時）

## 📊 **日誌覆蓋範圍**

### UI 層錯誤日誌
| 組件 | 錯誤類型 | 日誌狀態 |
|------|----------|----------|
| DailySummariesPage | 載入摘要失敗 | ✅ 已添加 |
| DailySummaryGenerator | 生成摘要失敗 | ✅ 已添加 |
| PdfReportGenerator | PDF 生成失敗 | ✅ 已添加 |
| PdfReportGenerator | PDF 預覽失敗 | ✅ 已添加 |

### 服務層錯誤日誌
| 服務 | 錯誤類型 | 日誌狀態 |
|------|----------|----------|
| DashboardServiceImpl | 所有數據查詢錯誤 | ✅ 已存在 |
| ReportGeneratorServiceImpl | PDF 生成錯誤 | ✅ 已存在 |
| ProductRemoteDataSource | 低庫存查詢錯誤 | ✅ 已存在 |

## 🚀 **使用方式**

### 開發環境
1. **啟動應用程序**
2. **打開開發者控制台**
3. **觸發錯誤操作**（例如：網絡斷開時生成摘要）
4. **查看控制台輸出**

### 錯誤追蹤流程
1. **用戶報告問題**
2. **檢查控制台日誌**
3. **根據錯誤訊息定位問題**
4. **使用堆疊追蹤找到具體代碼位置**

## 📝 **日誌示例**

### 成功的錯誤捕獲
```
ERROR: Error loading daily summaries
Error details: [cloud_firestore/failed-precondition] The query requires an index.
DEBUG: Stack trace: #1 ProviderElementBase.watch (package:riverpod/src/framework/element.dart:742:20)
#2 recentDailySummariesProvider.<anonymous closure> (package:grid_pos/features/reports_dashboard/presentation/providers/daily_summary_providers.dart:76:8)
...
```

### PDF 生成錯誤
```
ERROR: Failed to generate PDF report: 每日銷售報告
Error details: [core/network-error] Failed to fetch sales data
```

### 摘要生成錯誤
```
ERROR: Failed to generate daily summary for 2024-12-01
Error details: [cloud_firestore/permission-denied] Missing or insufficient permissions.
```

## 🔍 **調試優勢**

### 1. 快速問題定位
- 清晰的錯誤描述
- 具體的操作上下文
- 完整的堆疊追蹤

### 2. 用戶體驗改善
- 錯誤不會導致應用崩潰
- 用戶看到友好的錯誤訊息
- 開發者獲得詳細的調試信息

### 3. 生產環境監控
- 可以集成到日誌收集系統
- 幫助識別常見問題
- 支援遠程調試

## 🛠️ **最佳實踐**

### 1. 錯誤訊息格式
```dart
// ✅ 好的做法
Logger.error('Failed to generate daily summary for ${date}', error);

// ❌ 避免的做法
Logger.error('Error', error);
```

### 2. 上下文信息
- 包含相關的參數值
- 說明正在執行的操作
- 提供足夠的調試信息

### 3. 用戶友好性
- 錯誤日誌不影響用戶界面
- 保持應用程序穩定運行
- 提供有意義的錯誤提示

## 📋 **測試驗證**

### 測試結果
- ✅ 所有錯誤日誌正常輸出
- ✅ 不影響現有功能
- ✅ 測試套件全部通過
- ✅ 錯誤處理不會導致崩潰

### 測試覆蓋
```
DailySummariesPage Widget Tests: 6/6 通過
- 包含錯誤狀態的日誌輸出測試
```

## 🔮 **未來改進**

### 1. 日誌聚合
- 集成到 Firebase Crashlytics
- 添加用戶 ID 和會話信息
- 實施日誌級別過濾

### 2. 性能監控
- 添加操作耗時記錄
- 監控 API 調用性能
- 追蹤用戶行為模式

### 3. 自動化報告
- 定期生成錯誤報告
- 識別高頻錯誤模式
- 自動創建問題追蹤

---

**實施狀態**: ✅ 完成  
**測試狀態**: ✅ 通過  
**部署準備**: ✅ 就緒  

**最後更新**: 2024年12月  
**實施者**: Augment Agent
