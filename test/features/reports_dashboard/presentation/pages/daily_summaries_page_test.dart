import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/reports_dashboard/presentation/pages/daily_summaries_page.dart';

void main() {
  group('DailySummariesPage Widget Tests', () {
    testWidgets('should display page title and basic UI elements', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(child: MaterialApp(home: DailySummariesPage(storeId: 'test_store'))),
      );

      // Assert
      expect(find.text('每日摘要管理'), findsOneWidget);
      expect(find.text('每日摘要生成器'), findsOneWidget);
      expect(find.text('最近的每日摘要'), findsOneWidget);
    });

    testWidgets('should display daily summary generator', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(child: MaterialApp(home: DailySummariesPage(storeId: 'test_store'))),
      );

      // Assert
      expect(find.text('選擇要生成摘要的日期：'), findsOneWidget);
      expect(find.text('生成摘要'), findsOneWidget);
      expect(find.byIcon(Icons.calendar_today), findsOneWidget);
      expect(find.byIcon(Icons.play_arrow), findsOneWidget);
    });

    testWidgets('should build without errors', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(child: MaterialApp(home: DailySummariesPage(storeId: 'test_store'))),
      );

      // Assert - widget should build successfully
      expect(find.byType(DailySummariesPage), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.byType(SingleChildScrollView), findsOneWidget);
    });

    testWidgets('should have proper app bar configuration', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(child: MaterialApp(home: DailySummariesPage(storeId: 'test_store'))),
      );

      // Assert
      final appBar = tester.widget<AppBar>(find.byType(AppBar));
      expect(appBar.title, isA<Text>());

      final titleWidget = appBar.title as Text;
      expect(titleWidget.data, '每日摘要管理');
    });

    testWidgets('should display content sections', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(child: MaterialApp(home: DailySummariesPage(storeId: 'test_store'))),
      );

      // Wait for initial build
      await tester.pump();

      // Assert - should show main content sections
      expect(find.text('每日摘要生成器'), findsOneWidget);
      expect(find.text('最近的每日摘要'), findsOneWidget);
    });

    testWidgets('should handle navigation back', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: Builder(
                builder:
                    (context) => ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => DailySummariesPage(storeId: 'test_store'),
                          ),
                        );
                      },
                      child: const Text('Go to Summaries'),
                    ),
              ),
            ),
          ),
        ),
      );

      // Navigate to summaries page
      await tester.tap(find.text('Go to Summaries'));
      await tester.pumpAndSettle();

      // Verify we're on the summaries page
      expect(find.text('每日摘要管理'), findsOneWidget);

      // Navigate back
      await tester.tap(find.byType(BackButton));
      await tester.pumpAndSettle();

      // Verify we're back to the original page
      expect(find.text('Go to Summaries'), findsOneWidget);
    });
  });
}
