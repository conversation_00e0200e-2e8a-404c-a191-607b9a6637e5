import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/reports_dashboard/presentation/widgets/low_stock_summary_card.dart';
import 'package:grid_pos/features/product_mgmt/presentation/providers/product_providers.dart';
import 'package:grid_pos/features/product_mgmt/domain/entities/product_entity.dart';

void main() {
  group('LowStockSummaryCard', () {
    testWidgets('should display loading state', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            lowStockProductsByStoreProvider('store1').overrideWith(
              (ref) => const Stream.empty(), // This will keep it in loading state
            ),
          ],
          child: const MaterialApp(home: Scaffold(body: LowStockSummaryCard(storeId: 'store1'))),
        ),
      );

      // Act
      await tester.pump();

      // Assert
      expect(find.text('低庫存商品'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('載入中...'), findsOneWidget);
    });

    testWidgets('should display zero count when no low stock products', (
      WidgetTester tester,
    ) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            lowStockProductsByStoreProvider(
              'store1',
            ).overrideWith((ref) => Stream.value(<ProductEntity>[])),
          ],
          child: const MaterialApp(home: Scaffold(body: LowStockSummaryCard(storeId: 'store1'))),
        ),
      );

      // Act
      await tester.pump();

      // Assert
      expect(find.text('低庫存商品'), findsOneWidget);
      expect(find.text('0'), findsOneWidget);
      expect(find.text('庫存充足'), findsOneWidget);
    });

    testWidgets('should display correct count for low stock products', (WidgetTester tester) async {
      // Arrange
      final mockProducts = [
        ProductEntity(
          id: '1',
          sku: 'SKU001',
          name: 'Product 1',
          barcode: 'BAR001',
          price: 10.0,
          stock: 2,
          lowStockLevel: 5,
          gridId: 'grid1',
          tenantId: 'tenant1',
          storeId: 'store1',
        ),
        ProductEntity(
          id: '2',
          sku: 'SKU002',
          name: 'Product 2',
          barcode: 'BAR002',
          price: 15.0,
          stock: 1,
          lowStockLevel: 3,
          gridId: 'grid2',
          tenantId: 'tenant1',
          storeId: 'store1',
        ),
      ];

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            lowStockProductsByStoreProvider(
              'store1',
            ).overrideWith((ref) => Stream.value(mockProducts)),
          ],
          child: const MaterialApp(home: Scaffold(body: LowStockSummaryCard(storeId: 'store1'))),
        ),
      );

      // Act
      await tester.pump();

      // Assert
      expect(find.text('低庫存商品'), findsOneWidget);
      expect(find.text('2'), findsOneWidget);
      expect(find.text('需要補貨'), findsOneWidget);
    });

    testWidgets('should display tenant view correctly', (WidgetTester tester) async {
      // Arrange
      final mockProducts = [
        ProductEntity(
          id: '1',
          sku: 'SKU001',
          name: 'Product 1',
          barcode: 'BAR001',
          price: 10.0,
          stock: 2,
          lowStockLevel: 5,
          gridId: 'grid1',
          tenantId: 'tenant1',
          storeId: 'store1',
        ),
      ];

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            lowStockProductsByTenantProvider((
              storeId: 'store1',
              tenantId: 'tenant1',
            )).overrideWith((ref) => Stream.value(mockProducts)),
          ],
          child: const MaterialApp(
            home: Scaffold(body: LowStockSummaryCard(storeId: 'store1', tenantId: 'tenant1')),
          ),
        ),
      );

      // Act
      await tester.pump();

      // Assert
      expect(find.text('低庫存商品'), findsOneWidget);
      expect(find.text('1'), findsOneWidget);
      expect(find.text('我的商品'), findsOneWidget);
    });

    testWidgets('should navigate to low stock products page when tapped', (
      WidgetTester tester,
    ) async {
      // Arrange
      final mockProducts = [
        ProductEntity(
          id: '1',
          sku: 'SKU001',
          name: 'Product 1',
          barcode: 'BAR001',
          price: 10.0,
          stock: 2,
          lowStockLevel: 5,
          gridId: 'grid1',
          tenantId: 'tenant1',
          storeId: 'store1',
        ),
      ];

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            lowStockProductsByStoreProvider(
              'store1',
            ).overrideWith((ref) => Stream.value(mockProducts)),
          ],
          child: const MaterialApp(home: Scaffold(body: LowStockSummaryCard(storeId: 'store1'))),
        ),
      );

      // Act
      await tester.pump();
      await tester.tap(find.byType(Card));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('低庫存商品 (全店)'), findsOneWidget);
    });

    testWidgets('should display error state', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            lowStockProductsByStoreProvider(
              'store1',
            ).overrideWith((ref) => Stream.error('Test error')),
          ],
          child: const MaterialApp(home: Scaffold(body: LowStockSummaryCard(storeId: 'store1'))),
        ),
      );

      // Act
      await tester.pump();

      // Assert
      expect(find.text('低庫存商品'), findsOneWidget);
      expect(find.text('載入失敗'), findsOneWidget);
      expect(find.text('點擊重試'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });
  });
}
