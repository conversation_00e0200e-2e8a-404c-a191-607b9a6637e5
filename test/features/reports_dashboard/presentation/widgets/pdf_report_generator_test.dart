import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/reports_dashboard/presentation/widgets/pdf_report_generator.dart';

void main() {
  group('PdfReportGenerator Widget Tests', () {
    testWidgets('should display basic UI elements', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PdfReportGenerator(storeId: 'test_store', tenantId: 'test_tenant'),
            ),
          ),
        ),
      );

      // Assert - just check that the widget builds without errors
      expect(find.byType(PdfReportGenerator), findsOneWidget);
      expect(find.text('PDF 報告生成器'), findsOneWidget);
    });

    testWidgets('should display date picker for sales report', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(home: Scaffold(body: PdfReportGenerator(storeId: 'test_store'))),
        ),
      );

      // Default is already daily sales report, so date picker should be visible
      // Assert
      expect(find.text('報告日期:'), findsOneWidget);
      expect(find.byIcon(Icons.calendar_today), findsOneWidget);
    });

    testWidgets('should build without errors', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(home: Scaffold(body: PdfReportGenerator(storeId: 'test_store'))),
        ),
      );

      // Assert - widget should build successfully
      expect(find.byType(PdfReportGenerator), findsOneWidget);
    });

    testWidgets('should handle button taps without errors', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PdfReportGenerator(storeId: 'test_store', tenantId: 'test_tenant'),
            ),
          ),
        ),
      );

      // Tap preview button
      await tester.tap(find.text('預覽'));
      await tester.pump();

      // Tap generate PDF button
      await tester.tap(find.text('生成 PDF'));
      await tester.pump();

      // Should not throw any exceptions
      expect(tester.takeException(), isNull);
    });

    testWidgets('should display description section', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(home: Scaffold(body: PdfReportGenerator(storeId: 'test_store'))),
        ),
      );

      // Assert
      expect(find.text('說明'), findsOneWidget);
      expect(find.byIcon(Icons.info_outline), findsOneWidget);
    });
  });
}
