import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grid_pos/features/reports_dashboard/presentation/widgets/pdf_report_generator.dart';
import 'package:grid_pos/features/reports_dashboard/domain/enums/report_format.dart';

void main() {
  group('PdfReportGenerator Excel Feature Tests', () {
    testWidgets('should display format selection dropdown', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PdfReportGenerator(storeId: 'test_store'),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('報告格式:'), findsOneWidget);
      expect(find.text('PDF'), findsOneWidget);
      expect(find.text('Excel'), findsOneWidget);
    });

    testWidgets('should show PDF and Excel options in format dropdown', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PdfReportGenerator(storeId: 'test_store'),
            ),
          ),
        ),
      );

      // Tap on format dropdown
      await tester.tap(find.byType(DropdownButtonFormField<ReportFormat>));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('PDF').hitTestable(), findsAtLeastNWidgets(1));
      expect(find.text('Excel').hitTestable(), findsAtLeastNWidgets(1));
      expect(find.byIcon(Icons.picture_as_pdf), findsOneWidget);
      expect(find.byIcon(Icons.table_chart), findsOneWidget);
    });

    testWidgets('should change button text when Excel format is selected', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PdfReportGenerator(storeId: 'test_store'),
            ),
          ),
        ),
      );

      // Initially should show PDF
      expect(find.text('生成 PDF'), findsOneWidget);

      // Tap on format dropdown
      await tester.tap(find.byType(DropdownButtonFormField<ReportFormat>));
      await tester.pumpAndSettle();

      // Select Excel
      await tester.tap(find.text('Excel').last);
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('生成 Excel'), findsOneWidget);
      expect(find.text('生成 PDF'), findsNothing);
    });

    testWidgets('should show correct icon for selected format', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PdfReportGenerator(storeId: 'test_store'),
            ),
          ),
        ),
      );

      // Initially should show PDF icon in button
      final generateButton = find.byType(ElevatedButton).first;
      expect(generateButton, findsOneWidget);

      // Change to Excel format
      await tester.tap(find.byType(DropdownButtonFormField<ReportFormat>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Excel').last);
      await tester.pumpAndSettle();

      // Should now show table chart icon
      expect(find.byIcon(Icons.table_chart), findsOneWidget);
    });

    testWidgets('should maintain report type selection when changing format', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PdfReportGenerator(storeId: 'test_store'),
            ),
          ),
        ),
      );

      // Change report type to inventory
      await tester.tap(find.byType(DropdownButtonFormField<ReportType>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('店鋪庫存報告').last);
      await tester.pumpAndSettle();

      // Change format to Excel
      await tester.tap(find.byType(DropdownButtonFormField<ReportFormat>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Excel').last);
      await tester.pumpAndSettle();

      // Assert both selections are maintained
      expect(find.text('店鋪庫存報告'), findsOneWidget);
      expect(find.text('Excel'), findsOneWidget);
    });

    testWidgets('should show updated title as "報告生成器"', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PdfReportGenerator(storeId: 'test_store'),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('報告生成器'), findsOneWidget);
      expect(find.text('PDF 報告生成器'), findsNothing);
      expect(find.byIcon(Icons.file_download), findsOneWidget);
    });

    testWidgets('should handle format selection state correctly', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PdfReportGenerator(storeId: 'test_store'),
            ),
          ),
        ),
      );

      // Verify initial state
      expect(find.text('PDF'), findsOneWidget);
      expect(find.text('生成 PDF'), findsOneWidget);

      // Change to Excel
      await tester.tap(find.byType(DropdownButtonFormField<ReportFormat>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Excel').last);
      await tester.pumpAndSettle();

      // Verify Excel state
      expect(find.text('Excel'), findsOneWidget);
      expect(find.text('生成 Excel'), findsOneWidget);

      // Change back to PDF
      await tester.tap(find.byType(DropdownButtonFormField<ReportFormat>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('PDF').last);
      await tester.pumpAndSettle();

      // Verify PDF state restored
      expect(find.text('PDF'), findsOneWidget);
      expect(find.text('生成 PDF'), findsOneWidget);
    });

    testWidgets('should show format dropdown with proper styling', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PdfReportGenerator(storeId: 'test_store'),
            ),
          ),
        ),
      );

      // Open dropdown
      await tester.tap(find.byType(DropdownButtonFormField<ReportFormat>));
      await tester.pumpAndSettle();

      // Assert dropdown items have icons and text
      final pdfOption = find.ancestor(
        of: find.text('PDF'),
        matching: find.byType(Row),
      );
      final excelOption = find.ancestor(
        of: find.text('Excel'),
        matching: find.byType(Row),
      );

      expect(pdfOption, findsOneWidget);
      expect(excelOption, findsOneWidget);
    });

    testWidgets('should disable format selection when generating', (WidgetTester tester) async {
      // This test would require mocking the report generation service
      // to simulate the generating state, which is complex in this context.
      // For now, we'll test the UI structure.
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PdfReportGenerator(storeId: 'test_store'),
            ),
          ),
        ),
      );

      // Verify format dropdown exists and is enabled
      final formatDropdown = find.byType(DropdownButtonFormField<ReportFormat>);
      expect(formatDropdown, findsOneWidget);
      
      final dropdown = tester.widget<DropdownButtonFormField<ReportFormat>>(formatDropdown);
      expect(dropdown.onChanged, isNotNull);
    });
  });

  group('ReportFormat Enum Tests', () {
    test('should have correct display names', () {
      expect(ReportFormat.pdf.displayName, 'PDF');
      expect(ReportFormat.excel.displayName, 'Excel');
    });

    test('should have correct file extensions', () {
      expect(ReportFormat.pdf.fileExtension, 'pdf');
      expect(ReportFormat.excel.fileExtension, 'xlsx');
    });

    test('should have all expected values', () {
      expect(ReportFormat.values.length, 2);
      expect(ReportFormat.values, contains(ReportFormat.pdf));
      expect(ReportFormat.values, contains(ReportFormat.excel));
    });
  });
}
