import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:grid_pos/features/reports_dashboard/presentation/widgets/dashboard_card.dart';

void main() {
  group('DashboardCard', () {
    testWidgets('should display title and value correctly', (WidgetTester tester) async {
      // Arrange
      const title = 'Today Sales';
      const value = 'NT\$1,500';
      const subtitle = 'Yesterday: NT\$1,200';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DashboardCard(
              title: title,
              value: value,
              subtitle: subtitle,
              icon: Icons.attach_money,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(title), findsOneWidget);
      expect(find.text(value), findsOneWidget);
      expect(find.text(subtitle), findsOneWidget);
      expect(find.byIcon(Icons.attach_money), findsOneWidget);
    });

    testWidgets('should display change percentage correctly', (WidgetTester tester) async {
      // Arrange
      const title = 'Today Sales';
      const value = 'NT\$1,500';
      const changePercentage = 25.0;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DashboardCard(
              title: title,
              value: value,
              icon: Icons.attach_money,
              changePercentage: changePercentage,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(title), findsOneWidget);
      expect(find.text(value), findsOneWidget);
      expect(find.text('25.0%'), findsOneWidget);
      expect(find.byIcon(Icons.trending_up), findsOneWidget);
    });

    testWidgets('should display negative change percentage correctly', (WidgetTester tester) async {
      // Arrange
      const title = 'Today Sales';
      const value = 'NT\$1,200';
      const changePercentage = -15.0;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DashboardCard(
              title: title,
              value: value,
              icon: Icons.attach_money,
              changePercentage: changePercentage,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(title), findsOneWidget);
      expect(find.text(value), findsOneWidget);
      expect(find.text('15.0%'), findsOneWidget);
      expect(find.byIcon(Icons.trending_down), findsOneWidget);
    });

    testWidgets('should handle tap correctly', (WidgetTester tester) async {
      // Arrange
      bool tapped = false;
      const title = 'Today Sales';
      const value = 'NT\$1,500';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DashboardCard(
              title: title,
              value: value,
              icon: Icons.attach_money,
              onTap: () {
                tapped = true;
              },
            ),
          ),
        ),
      );

      await tester.tap(find.byType(DashboardCard));
      await tester.pump();

      // Assert
      expect(tapped, isTrue);
    });

    testWidgets('should display loading state with placeholder text', (WidgetTester tester) async {
      // Arrange
      const title = 'Today Sales';
      const value = 'Loading...'; // Use placeholder text instead of null

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: DashboardCard(title: title, value: value, icon: Icons.attach_money)),
        ),
      );

      // Assert
      expect(find.text(title), findsOneWidget);
      expect(find.text(value), findsOneWidget);
      expect(find.byIcon(Icons.attach_money), findsOneWidget);
    });

    testWidgets('should apply custom icon color correctly', (WidgetTester tester) async {
      // Arrange
      const title = 'Today Sales';
      const value = 'NT\$1,500';
      const iconColor = Colors.green;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DashboardCard(
              title: title,
              value: value,
              icon: Icons.attach_money,
              iconColor: iconColor,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(title), findsOneWidget);
      expect(find.text(value), findsOneWidget);

      // Find the icon and check its color
      final iconWidget = tester.widget<Icon>(find.byIcon(Icons.attach_money));
      expect(iconWidget.color, iconColor);
    });

    testWidgets('should handle zero change percentage', (WidgetTester tester) async {
      // Arrange
      const title = 'Today Sales';
      const value = 'NT\$1,500';
      const changePercentage = 0.0;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DashboardCard(
              title: title,
              value: value,
              icon: Icons.attach_money,
              changePercentage: changePercentage,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(title), findsOneWidget);
      expect(find.text(value), findsOneWidget);
      expect(find.text('0.0%'), findsOneWidget);
      // Zero is considered positive (>= 0), so shows trending up icon
      expect(find.byIcon(Icons.trending_up), findsOneWidget);
      expect(find.byIcon(Icons.trending_down), findsNothing);
    });
  });

  group('formatCurrency', () {
    test('should format currency correctly', () {
      // Act & Assert
      expect(formatCurrency(1500.0), '\$1,500');
      expect(formatCurrency(1500.50), '\$1,501');
      expect(formatCurrency(0.0), '\$0');
    });
  });

  group('formatNumber', () {
    test('should format number correctly', () {
      // Act & Assert
      expect(formatNumber(1500), '1,500');
      expect(formatNumber(1000000), '1,000,000');
      expect(formatNumber(0), '0');
    });
  });
}
