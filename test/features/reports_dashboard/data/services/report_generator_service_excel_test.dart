import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:excel/excel.dart';
import 'package:grid_pos/features/reports_dashboard/data/services/report_generator_service_impl.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';

void main() {
  group('ReportGeneratorServiceImpl Excel Tests', () {
    late ReportGeneratorServiceImpl service;
    late FakeFirebaseFirestore fakeFirestore;

    setUp(() {
      fakeFirestore = FakeFirebaseFirestore();
      service = ReportGeneratorServiceImpl(fakeFirestore);
    });

    group('generateDailySalesReportExcel', () {
      test('should generate Excel file with sales data', () async {
        // Arrange
        const storeId = 'test_store';
        final date = DateTime(2024, 12, 1);

        // Act
        final result = await service.generateDailySalesReportExcel(storeId, date);

        // Assert
        expect(result, isA<Uint8List>());
        expect(result.isNotEmpty, true);
        
        // Verify Excel content
        final excel = Excel.decodeBytes(result);
        expect(excel.sheets.containsKey('Daily Sales Report'), true);
        
        final sheet = excel.sheets['Daily Sales Report']!;
        expect(sheet.cell(CellIndex.indexByString('A1')).value?.toString(), 'Daily Sales Report');
      });
    });

    group('generateInventoryReportExcel', () {
      test('should generate Excel file with inventory data', () async {
        // Arrange
        const storeId = 'test_store';

        // Act
        final result = await service.generateInventoryReportExcel(storeId);

        // Assert
        expect(result, isA<Uint8List>());
        expect(result.isNotEmpty, true);
        
        // Verify Excel content
        final excel = Excel.decodeBytes(result);
        expect(excel.sheets.containsKey('Store Inventory'), true);
        
        final sheet = excel.sheets['Store Inventory']!;
        expect(sheet.cell(CellIndex.indexByString('A1')).value?.toString(), 'Store Inventory Report');
      });

      test('should generate tenant-specific Excel file', () async {
        // Arrange
        const storeId = 'test_store';
        const tenantId = 'test_tenant';

        // Act
        final result = await service.generateInventoryReportExcel(storeId, tenantId: tenantId);

        // Assert
        expect(result, isA<Uint8List>());
        expect(result.isNotEmpty, true);
        
        // Verify Excel content
        final excel = Excel.decodeBytes(result);
        expect(excel.sheets.containsKey('Tenant Inventory'), true);
        
        final sheet = excel.sheets['Tenant Inventory']!;
        expect(sheet.cell(CellIndex.indexByString('A1')).value?.toString(), 'Tenant Inventory Report');
      });
    });

    group('Excel formatting', () {
      test('should format currency values correctly', () async {
        // Arrange
        const storeId = 'test_store';
        final date = DateTime(2024, 12, 1);

        // Act
        final result = await service.generateDailySalesReportExcel(storeId, date);

        // Assert
        expect(result, isA<Uint8List>());
        
        // Verify Excel structure
        final excel = Excel.decodeBytes(result);
        final sheet = excel.sheets['Daily Sales Report']!;
        
        // Check headers exist
        expect(sheet.cell(CellIndex.indexByString('A5')).value?.toString(), 'Sales Summary');
        expect(sheet.cell(CellIndex.indexByString('A7')).value?.toString(), 'Total Sales:');
        expect(sheet.cell(CellIndex.indexByString('A11')).value?.toString(), 'Transaction Details');
      });

      test('should apply proper styling to headers', () async {
        // Arrange
        const storeId = 'test_store';

        // Act
        final result = await service.generateInventoryReportExcel(storeId);

        // Assert
        final excel = Excel.decodeBytes(result);
        final sheet = excel.sheets['Store Inventory']!;
        
        // Check that title cell has styling
        final titleCell = sheet.cell(CellIndex.indexByString('A1'));
        expect(titleCell.cellStyle?.isBold, true);
        expect(titleCell.cellStyle?.fontSize, 16);
      });
    });
  });
}
