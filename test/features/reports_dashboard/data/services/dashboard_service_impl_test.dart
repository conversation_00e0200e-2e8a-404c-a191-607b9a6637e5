import 'package:flutter_test/flutter_test.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:grid_pos/features/reports_dashboard/data/services/dashboard_service_impl.dart';
import 'package:grid_pos/core/constants/firestore_constants.dart';

void main() {
  group('DashboardServiceImpl', () {
    late FakeFirebaseFirestore fakeFirestore;
    late DashboardServiceImpl dashboardService;

    setUp(() {
      fakeFirestore = FakeFirebaseFirestore();
      dashboardService = DashboardServiceImpl(fakeFirestore);
    });

    group('getLowStockProductsCount', () {
      test('should return 0 when no products exist', () async {
        // Act
        final result = await dashboardService.getLowStockProductsCount('store1');

        // Assert
        expect(result, 0);
      });

      test('should count low stock products for specific tenant', () async {
        // Arrange
        const storeId = 'store1';
        const tenantId = 'tenant1';

        // Add some products
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .add({
              FirestoreConstants.active: true,
              FirestoreConstants.stock: 3,
              FirestoreConstants.lowStockLevel: 5,
            });

        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .add({
              FirestoreConstants.active: true,
              FirestoreConstants.stock: 10,
              FirestoreConstants.lowStockLevel: 5,
            });

        // Act
        final result = await dashboardService.getLowStockProductsCount(storeId, tenantId: tenantId);

        // Assert
        expect(result, 1); // Only one product has stock <= lowStockLevel
      });

      test('should count low stock products for all tenants when tenantId is null', () async {
        // Arrange
        const storeId = 'store1';

        // Add tenant1 with low stock product
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc('tenant1')
            .set({'name': 'Tenant 1'});

        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc('tenant1')
            .collection(FirestoreConstants.products)
            .add({
              FirestoreConstants.active: true,
              FirestoreConstants.stock: 2,
              FirestoreConstants.lowStockLevel: 5,
            });

        // Add tenant2 with low stock product
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc('tenant2')
            .set({'name': 'Tenant 2'});

        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc('tenant2')
            .collection(FirestoreConstants.products)
            .add({
              FirestoreConstants.active: true,
              FirestoreConstants.stock: 1,
              FirestoreConstants.lowStockLevel: 3,
            });

        // Act
        final result = await dashboardService.getLowStockProductsCount(storeId);

        // Assert
        expect(result, 2); // Both products are low stock
      });

      test('should ignore inactive products', () async {
        // Arrange
        const storeId = 'store1';
        const tenantId = 'tenant1';

        // Add inactive product with low stock
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .add({
              FirestoreConstants.active: false,
              FirestoreConstants.stock: 1,
              FirestoreConstants.lowStockLevel: 5,
            });

        // Act
        final result = await dashboardService.getLowStockProductsCount(storeId, tenantId: tenantId);

        // Assert
        expect(result, 0); // Inactive product should be ignored
      });
    });

    group('getSalesTrendData', () {
      test('should return data points for specified days even when no sales exist', () async {
        // Act
        final result = await dashboardService.getSalesTrendData('store1', days: 3);

        // Assert
        expect(result.length, 3); // Should return 3 data points
        expect(result.every((data) => data.sales == 0.0), isTrue); // All should have zero sales
        expect(
          result.every((data) => data.transactionsCount == 0),
          isTrue,
        ); // All should have zero transactions
      });

      test('should return trend data for specified days', () async {
        // Arrange
        const storeId = 'store1';
        final today = DateTime.now();
        final yesterday = today.subtract(const Duration(days: 1));

        // Add sales for yesterday
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.sales)
            .add({
              FirestoreConstants.totalAmount: 100.0,
              FirestoreConstants.status: 'completed',
              FirestoreConstants.createdAt: Timestamp.fromDate(yesterday),
            });

        // Act
        final result = await dashboardService.getSalesTrendData(storeId, days: 2);

        // Assert
        expect(result.length, 2); // Should return data for 2 days
        expect(result.any((data) => data.sales > 0), isTrue); // Should have some sales data
      });
    });

    group('getTopSellingProducts', () {
      test('should return empty list when no sales exist', () async {
        // Act
        final result = await dashboardService.getTopSellingProducts('store1');

        // Assert
        expect(result, isEmpty);
      });

      test('should return top selling products correctly', () async {
        // Arrange
        const storeId = 'store1';
        final now = DateTime.now();

        // Add sales with items
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.sales)
            .add({
              FirestoreConstants.status: 'completed',
              FirestoreConstants.items: [
                {
                  'productId': 'product1',
                  'name': 'Product 1',
                  'sku': 'SKU001',
                  'qty': 5,
                  'price': 10.0,
                  FirestoreConstants.tenantId: 'tenant1',
                },
                {
                  'productId': 'product2',
                  'name': 'Product 2',
                  'sku': 'SKU002',
                  'qty': 3,
                  'price': 15.0,
                  FirestoreConstants.tenantId: 'tenant1',
                },
              ],
              FirestoreConstants.createdAt: Timestamp.fromDate(now),
            });

        // Act
        final result = await dashboardService.getTopSellingProducts(storeId, limit: 2);

        // Assert
        expect(result.length, 2);
        expect(result.first.productId, 'product1'); // Product 1 should be first (qty: 5)
        expect(result.first.quantitySold, 5);
        expect(result.last.productId, 'product2'); // Product 2 should be second (qty: 3)
        expect(result.last.quantitySold, 3);
      });
    });

    group('Data Aggregation Logic Tests', () {
      group('getAdminDashboardData', () {
        test('should aggregate data correctly for admin view', () async {
          // Arrange
          const storeId = 'store1';
          final today = DateTime.now();
          final yesterday = today.subtract(const Duration(days: 1));

          // Add sales data for today
          await fakeFirestore
              .collection(FirestoreConstants.stores)
              .doc(storeId)
              .collection(FirestoreConstants.sales)
              .add({
                FirestoreConstants.totalAmount: 150.0,
                FirestoreConstants.status: 'completed',
                FirestoreConstants.createdAt: Timestamp.fromDate(today),
              });

          // Add sales data for yesterday
          await fakeFirestore
              .collection(FirestoreConstants.stores)
              .doc(storeId)
              .collection(FirestoreConstants.sales)
              .add({
                FirestoreConstants.totalAmount: 100.0,
                FirestoreConstants.status: 'completed',
                FirestoreConstants.createdAt: Timestamp.fromDate(yesterday),
              });

          // Add tenant with low stock product
          await fakeFirestore
              .collection(FirestoreConstants.stores)
              .doc(storeId)
              .collection(FirestoreConstants.tenants)
              .doc('tenant1')
              .set({'name': 'Tenant 1'});

          await fakeFirestore
              .collection(FirestoreConstants.stores)
              .doc(storeId)
              .collection(FirestoreConstants.tenants)
              .doc('tenant1')
              .collection(FirestoreConstants.products)
              .add({
                FirestoreConstants.active: true,
                FirestoreConstants.stock: 2,
                FirestoreConstants.lowStockLevel: 5,
              });

          // Act
          final result = await dashboardService.getAdminDashboardData(storeId);

          // Assert
          expect(result.todayTotalSales, 150.0);
          expect(result.yesterdayTotalSales, 100.0);
          expect(result.todayTransactionsCount, 1);
          expect(result.yesterdayTransactionsCount, 1);
          expect(result.lowStockProductsCount, 1);
          expect(result.salesChangePercentage, 50.0); // (150-100)/100 * 100
          expect(result.transactionsChangePercentage, 0.0); // (1-1)/1 * 100
        });

        test('should handle zero division in percentage calculations', () async {
          // Arrange
          const storeId = 'store1';
          final today = DateTime.now();

          // Add sales data only for today
          await fakeFirestore
              .collection(FirestoreConstants.stores)
              .doc(storeId)
              .collection(FirestoreConstants.sales)
              .add({
                FirestoreConstants.totalAmount: 100.0,
                FirestoreConstants.status: 'completed',
                FirestoreConstants.createdAt: Timestamp.fromDate(today),
              });

          // Act
          final result = await dashboardService.getAdminDashboardData(storeId);

          // Assert
          expect(result.todayTotalSales, 100.0);
          expect(result.yesterdayTotalSales, 0.0);
          expect(
            result.salesChangePercentage,
            100.0,
          ); // When yesterday is 0 and today > 0, returns 100%
          expect(result.transactionsChangePercentage, 100.0); // Same logic for transactions
        });

        test('should ignore cancelled sales', () async {
          // Arrange
          const storeId = 'store1';
          final today = DateTime.now();

          // Add completed sale
          await fakeFirestore
              .collection(FirestoreConstants.stores)
              .doc(storeId)
              .collection(FirestoreConstants.sales)
              .add({
                FirestoreConstants.totalAmount: 100.0,
                FirestoreConstants.status: 'completed',
                FirestoreConstants.createdAt: Timestamp.fromDate(today),
              });

          // Add cancelled sale
          await fakeFirestore
              .collection(FirestoreConstants.stores)
              .doc(storeId)
              .collection(FirestoreConstants.sales)
              .add({
                FirestoreConstants.totalAmount: 50.0,
                FirestoreConstants.status: 'cancelled',
                FirestoreConstants.createdAt: Timestamp.fromDate(today),
              });

          // Act
          final result = await dashboardService.getAdminDashboardData(storeId);

          // Assert
          expect(result.todayTotalSales, 100.0); // Only completed sales
          expect(result.todayTransactionsCount, 1); // Only completed transactions
        });
      });

      group('getTenantDashboardData', () {
        test('should filter by tenant correctly', () async {
          // Arrange
          const storeId = 'store1';
          const tenantId = 'tenant1';
          final today = DateTime.now();

          // Add sales with items from different tenants
          await fakeFirestore
              .collection(FirestoreConstants.stores)
              .doc(storeId)
              .collection(FirestoreConstants.sales)
              .add({
                FirestoreConstants.totalAmount: 200.0,
                FirestoreConstants.status: 'completed',
                FirestoreConstants.createdAt: Timestamp.fromDate(today),
                FirestoreConstants.items: [
                  {
                    'productId': 'product1',
                    'qty': 2,
                    'price': 50.0,
                    FirestoreConstants.tenantId: tenantId,
                  },
                  {
                    'productId': 'product2',
                    'qty': 1,
                    'price': 100.0,
                    FirestoreConstants.tenantId: 'other_tenant',
                  },
                ],
              });

          // Act
          final result = await dashboardService.getTenantDashboardData(storeId, tenantId);

          // Assert
          expect(result.todayTotalSales, 100.0); // Only tenant1's items: 2 * 50.0
          expect(result.todayTransactionsCount, 1); // Transaction contains tenant's items
        });
      });

      group('getSalesTrendData', () {
        test('should return correct trend data for specified days', () async {
          // Arrange
          const storeId = 'store1';
          final today = DateTime.now();
          final yesterday = today.subtract(const Duration(days: 1));
          final twoDaysAgo = today.subtract(const Duration(days: 2));

          // Add sales for different days
          await fakeFirestore
              .collection(FirestoreConstants.stores)
              .doc(storeId)
              .collection(FirestoreConstants.sales)
              .add({
                FirestoreConstants.totalAmount: 100.0,
                FirestoreConstants.status: 'completed',
                FirestoreConstants.createdAt: Timestamp.fromDate(today),
              });

          await fakeFirestore
              .collection(FirestoreConstants.stores)
              .doc(storeId)
              .collection(FirestoreConstants.sales)
              .add({
                FirestoreConstants.totalAmount: 150.0,
                FirestoreConstants.status: 'completed',
                FirestoreConstants.createdAt: Timestamp.fromDate(yesterday),
              });

          await fakeFirestore
              .collection(FirestoreConstants.stores)
              .doc(storeId)
              .collection(FirestoreConstants.sales)
              .add({
                FirestoreConstants.totalAmount: 75.0,
                FirestoreConstants.status: 'completed',
                FirestoreConstants.createdAt: Timestamp.fromDate(twoDaysAgo),
              });

          // Act
          final result = await dashboardService.getSalesTrendData(storeId, days: 3);

          // Assert
          expect(result.length, 3);

          // Find today's data
          final todayData = result.firstWhere(
            (data) =>
                data.date.day == today.day &&
                data.date.month == today.month &&
                data.date.year == today.year,
          );
          expect(todayData.sales, 100.0);
          expect(todayData.transactionsCount, 1);

          // Find yesterday's data
          final yesterdayData = result.firstWhere(
            (data) =>
                data.date.day == yesterday.day &&
                data.date.month == yesterday.month &&
                data.date.year == yesterday.year,
          );
          expect(yesterdayData.sales, 150.0);
          expect(yesterdayData.transactionsCount, 1);
        });

        test('should filter by tenant in trend data', () async {
          // Arrange
          const storeId = 'store1';
          const tenantId = 'tenant1';
          final today = DateTime.now();

          // Add sales with mixed tenant items
          await fakeFirestore
              .collection(FirestoreConstants.stores)
              .doc(storeId)
              .collection(FirestoreConstants.sales)
              .add({
                FirestoreConstants.totalAmount: 200.0,
                FirestoreConstants.status: 'completed',
                FirestoreConstants.createdAt: Timestamp.fromDate(today),
                FirestoreConstants.items: [
                  {
                    'productId': 'product1',
                    'qty': 1,
                    'price': 100.0,
                    FirestoreConstants.tenantId: tenantId,
                  },
                  {
                    'productId': 'product2',
                    'qty': 1,
                    'price': 100.0,
                    FirestoreConstants.tenantId: 'other_tenant',
                  },
                ],
              });

          // Act
          final result = await dashboardService.getSalesTrendData(
            storeId,
            tenantId: tenantId,
            days: 1,
          );

          // Assert
          expect(result.length, 1);
          expect(result.first.sales, 100.0); // Only tenant1's items
          expect(result.first.transactionsCount, 1); // Transaction contains tenant's items
        });
      });
    });
  });
}
