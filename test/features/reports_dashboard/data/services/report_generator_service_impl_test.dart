import 'package:flutter_test/flutter_test.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:grid_pos/features/reports_dashboard/data/services/report_generator_service_impl.dart';
import 'package:grid_pos/core/constants/firestore_constants.dart';

void main() {
  group('ReportGeneratorServiceImpl', () {
    late FakeFirebaseFirestore fakeFirestore;
    late ReportGeneratorServiceImpl reportService;

    setUp(() {
      fakeFirestore = FakeFirebaseFirestore();
      reportService = ReportGeneratorServiceImpl(fakeFirestore);
    });

    group('generateDailySalesReportPdf', () {
      test('should generate PDF with sales data when no daily summary exists', () async {
        // Arrange
        const storeId = 'store1';
        final date = DateTime(2024, 1, 15);

        // Add sales data
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.sales)
            .add({
              FirestoreConstants.totalAmount: 100.0,
              FirestoreConstants.status: 'completed',
              FirestoreConstants.createdAt: Timestamp.fromDate(date),
              FirestoreConstants.items: [
                {'productId': 'product1', 'name': 'Product 1', 'qty': 2, 'price': 50.0},
              ],
            });

        // Act
        final pdfBytes = await reportService.generateDailySalesReportPdf(storeId, date);

        // Assert
        expect(pdfBytes, isNotEmpty);
        expect(pdfBytes.length, greaterThan(1000)); // PDF should have reasonable size
      });

      test('should generate PDF with daily summary data when available', () async {
        // Arrange
        const storeId = 'store1';
        final date = DateTime(2024, 1, 15);
        final documentId = '20240115_$storeId';

        // Add daily summary
        await fakeFirestore.collection(FirestoreConstants.dailySummaries).doc(documentId).set({
          'date': '2024-01-15',
          FirestoreConstants.storeId: storeId,
          'totalSales': 250.0,
          'transactionsCount': 5,
          'lowStockProductsCount': 2,
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.updatedAt: Timestamp.now(),
        });

        // Add some sales transactions for details
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.sales)
            .add({
              FirestoreConstants.totalAmount: 150.0,
              FirestoreConstants.status: 'completed',
              FirestoreConstants.createdAt: Timestamp.fromDate(date),
              FirestoreConstants.items: [
                {'productId': 'product1', 'name': 'Product 1', 'qty': 3, 'price': 50.0},
              ],
            });

        // Act
        final pdfBytes = await reportService.generateDailySalesReportPdf(storeId, date);

        // Assert
        expect(pdfBytes, isNotEmpty);
        expect(pdfBytes.length, greaterThan(1000));
      });

      test('should handle empty sales data gracefully', () async {
        // Arrange
        const storeId = 'store1';
        final date = DateTime(2024, 1, 15);

        // Act
        final pdfBytes = await reportService.generateDailySalesReportPdf(storeId, date);

        // Assert
        expect(pdfBytes, isNotEmpty);
        expect(pdfBytes.length, greaterThan(500)); // Should still generate a valid PDF
      });
    });

    group('generateInventoryReportPdf', () {
      test('should generate PDF for tenant inventory', () async {
        // Arrange
        const storeId = 'store1';
        const tenantId = 'tenant1';

        // Add products for tenant
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .add({
              FirestoreConstants.name: 'Product 1',
              FirestoreConstants.sku: 'SKU001',
              FirestoreConstants.barcode: 'BAR001',
              FirestoreConstants.price: 10.0,
              FirestoreConstants.stock: 5,
              FirestoreConstants.lowStockLevel: 3,
              FirestoreConstants.gridId: 'grid1',
              FirestoreConstants.active: true,
              FirestoreConstants.tenantId: tenantId,
              FirestoreConstants.storeId: storeId,
              FirestoreConstants.createdAt: Timestamp.now(),
              FirestoreConstants.updatedAt: Timestamp.now(),
            });

        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .add({
              FirestoreConstants.name: 'Product 2',
              FirestoreConstants.sku: 'SKU002',
              FirestoreConstants.barcode: 'BAR002',
              FirestoreConstants.price: 15.0,
              FirestoreConstants.stock: 2,
              FirestoreConstants.lowStockLevel: 5,
              FirestoreConstants.gridId: 'grid2',
              FirestoreConstants.active: true,
              FirestoreConstants.tenantId: tenantId,
              FirestoreConstants.storeId: storeId,
              FirestoreConstants.createdAt: Timestamp.now(),
              FirestoreConstants.updatedAt: Timestamp.now(),
            });

        // Act
        final pdfBytes = await reportService.generateInventoryReportPdf(
          storeId,
          tenantId: tenantId,
        );

        // Assert
        expect(pdfBytes, isNotEmpty);
        expect(pdfBytes.length, greaterThan(1000));
      });

      test('should generate PDF for store-wide inventory', () async {
        // Arrange
        const storeId = 'store1';

        // Add products for multiple tenants
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc('tenant1')
            .collection(FirestoreConstants.products)
            .add({
              FirestoreConstants.name: 'Tenant1 Product',
              FirestoreConstants.sku: 'SKU001',
              FirestoreConstants.barcode: 'BAR001',
              FirestoreConstants.price: 10.0,
              FirestoreConstants.stock: 5,
              FirestoreConstants.lowStockLevel: 3,
              FirestoreConstants.gridId: 'grid1',
              FirestoreConstants.active: true,
              FirestoreConstants.tenantId: 'tenant1',
              FirestoreConstants.storeId: storeId,
              FirestoreConstants.createdAt: Timestamp.now(),
              FirestoreConstants.updatedAt: Timestamp.now(),
            });

        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc('tenant2')
            .collection(FirestoreConstants.products)
            .add({
              FirestoreConstants.name: 'Tenant2 Product',
              FirestoreConstants.sku: 'SKU002',
              FirestoreConstants.barcode: 'BAR002',
              FirestoreConstants.price: 15.0,
              FirestoreConstants.stock: 8,
              FirestoreConstants.lowStockLevel: 5,
              FirestoreConstants.gridId: 'grid2',
              FirestoreConstants.active: true,
              FirestoreConstants.tenantId: 'tenant2',
              FirestoreConstants.storeId: storeId,
              FirestoreConstants.createdAt: Timestamp.now(),
              FirestoreConstants.updatedAt: Timestamp.now(),
            });

        // Act
        final pdfBytes = await reportService.generateInventoryReportPdf(storeId);

        // Assert
        expect(pdfBytes, isNotEmpty);
        expect(pdfBytes.length, greaterThan(1000));
      });

      test('should handle empty inventory gracefully', () async {
        // Arrange
        const storeId = 'store1';
        const tenantId = 'tenant1';

        // Act
        final pdfBytes = await reportService.generateInventoryReportPdf(
          storeId,
          tenantId: tenantId,
        );

        // Assert
        expect(pdfBytes, isNotEmpty);
        expect(pdfBytes.length, greaterThan(500)); // Should still generate a valid PDF
      });
    });
  });
}
