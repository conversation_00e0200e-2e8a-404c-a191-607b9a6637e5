import 'package:flutter_test/flutter_test.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:grid_pos/features/reports_dashboard/data/models/daily_summary_dto.dart';
import 'package:grid_pos/features/reports_dashboard/domain/entities/daily_summary_entity.dart';

void main() {
  group('DailySummaryDto', () {
    test('should create DTO with all required fields', () {
      // Arrange
      final now = Timestamp.now();
      
      // Act
      final dto = DailySummaryDto(
        date: '2024-12-01',
        storeId: 'store1',
        totalSales: 1000.0,
        transactionsCount: 10,
        lowStockProductsCount: 5,
        updatedAt: now,
        createdAt: now,
      );

      // Assert
      expect(dto.date, '2024-12-01');
      expect(dto.storeId, 'store1');
      expect(dto.totalSales, 1000.0);
      expect(dto.transactionsCount, 10);
      expect(dto.lowStockProductsCount, 5);
      expect(dto.updatedAt, now);
      expect(dto.createdAt, now);
    });

    test('should convert to JSON correctly', () {
      // Arrange
      final now = Timestamp.now();
      final dto = DailySummaryDto(
        date: '2024-12-01',
        storeId: 'store1',
        totalSales: 1000.0,
        transactionsCount: 10,
        lowStockProductsCount: 5,
        updatedAt: now,
        createdAt: now,
      );

      // Act
      final json = dto.toJson();

      // Assert
      expect(json['date'], '2024-12-01');
      expect(json['storeId'], 'store1');
      expect(json['totalSales'], 1000.0);
      expect(json['transactionsCount'], 10);
      expect(json['lowStockProductsCount'], 5);
      expect(json['updatedAt'], now);
      expect(json['createdAt'], now);
    });

    test('should create from JSON correctly', () {
      // Arrange
      final now = Timestamp.now();
      final json = {
        'date': '2024-12-01',
        'storeId': 'store1',
        'totalSales': 1000.0,
        'transactionsCount': 10,
        'lowStockProductsCount': 5,
        'updatedAt': now,
        'createdAt': now,
      };

      // Act
      final dto = DailySummaryDto.fromJson(json);

      // Assert
      expect(dto.date, '2024-12-01');
      expect(dto.storeId, 'store1');
      expect(dto.totalSales, 1000.0);
      expect(dto.transactionsCount, 10);
      expect(dto.lowStockProductsCount, 5);
      expect(dto.updatedAt, now);
      expect(dto.createdAt, now);
    });

    test('should convert to entity correctly', () {
      // Arrange
      final now = Timestamp.now();
      final dto = DailySummaryDto(
        date: '2024-12-01',
        storeId: 'store1',
        totalSales: 1000.0,
        transactionsCount: 10,
        lowStockProductsCount: 5,
        updatedAt: now,
        createdAt: now,
      );
      const documentId = '20241201_store1';

      // Act
      final entity = dto.toEntity(documentId);

      // Assert
      expect(entity.id, documentId);
      expect(entity.date, DateTime(2024, 12, 1));
      expect(entity.storeId, 'store1');
      expect(entity.totalSales, 1000.0);
      expect(entity.transactionsCount, 10);
      expect(entity.lowStockProductsCount, 5);
      expect(entity.updatedAt, now.toDate());
      expect(entity.createdAt, now.toDate());
    });

    test('should create from entity correctly', () {
      // Arrange
      final now = DateTime.now();
      final entity = DailySummaryEntity(
        id: '20241201_store1',
        date: DateTime(2024, 12, 1),
        storeId: 'store1',
        totalSales: 1000.0,
        transactionsCount: 10,
        lowStockProductsCount: 5,
        updatedAt: now,
        createdAt: now,
      );

      // Act
      final dto = DailySummaryDto.fromEntity(entity);

      // Assert
      expect(dto.date, '2024-12-01');
      expect(dto.storeId, 'store1');
      expect(dto.totalSales, 1000.0);
      expect(dto.transactionsCount, 10);
      expect(dto.lowStockProductsCount, 5);
      expect(dto.updatedAt.toDate(), now);
      expect(dto.createdAt.toDate(), now);
    });

    test('should handle invalid date format gracefully', () {
      // Arrange
      final now = Timestamp.now();
      final dto = DailySummaryDto(
        date: 'invalid-date',
        storeId: 'store1',
        totalSales: 1000.0,
        transactionsCount: 10,
        lowStockProductsCount: 5,
        updatedAt: now,
        createdAt: now,
      );
      const documentId = '20241201_store1';

      // Act
      final entity = dto.toEntity(documentId);

      // Assert
      // Should fallback to parsing from document ID
      expect(entity.date, DateTime(2024, 12, 1));
      expect(entity.storeId, 'store1');
    });

    test('should support copyWith functionality', () {
      // Arrange
      final now = Timestamp.now();
      final dto = DailySummaryDto(
        date: '2024-12-01',
        storeId: 'store1',
        totalSales: 1000.0,
        transactionsCount: 10,
        lowStockProductsCount: 5,
        updatedAt: now,
        createdAt: now,
      );

      // Act
      final updatedDto = dto.copyWith(
        totalSales: 2000.0,
        transactionsCount: 20,
      );

      // Assert
      expect(updatedDto.date, dto.date);
      expect(updatedDto.storeId, dto.storeId);
      expect(updatedDto.totalSales, 2000.0);
      expect(updatedDto.transactionsCount, 20);
      expect(updatedDto.lowStockProductsCount, dto.lowStockProductsCount);
      expect(updatedDto.updatedAt, dto.updatedAt);
      expect(updatedDto.createdAt, dto.createdAt);
    });
  });
}
