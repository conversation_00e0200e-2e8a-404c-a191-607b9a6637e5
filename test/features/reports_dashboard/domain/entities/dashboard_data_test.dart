import 'package:flutter_test/flutter_test.dart';
import 'package:grid_pos/features/reports_dashboard/domain/entities/dashboard_data.dart';

void main() {
  group('DashboardData', () {
    test('should create dashboard data with all required fields', () {
      // Arrange
      final salesTrendData = [
        SalesTrendData(
          date: DateTime(2024, 12, 1),
          sales: 1000.0,
          transactionsCount: 10,
        ),
      ];

      final topSellingProducts = [
        const TopSellingProductData(
          productId: 'product1',
          productName: 'Product 1',
          productSku: 'SKU001',
          quantitySold: 50,
          totalSales: 2500.0,
        ),
      ];

      // Act
      final dashboardData = DashboardData(
        todayTotalSales: 1500.0,
        yesterdayTotalSales: 1200.0,
        todayTransactionsCount: 15,
        yesterdayTransactionsCount: 12,
        lowStockProductsCount: 3,
        salesTrendData: salesTrendData,
        topSellingProducts: topSellingProducts,
      );

      // Assert
      expect(dashboardData.todayTotalSales, 1500.0);
      expect(dashboardData.yesterdayTotalSales, 1200.0);
      expect(dashboardData.todayTransactionsCount, 15);
      expect(dashboardData.yesterdayTransactionsCount, 12);
      expect(dashboardData.lowStockProductsCount, 3);
      expect(dashboardData.salesTrendData, salesTrendData);
      expect(dashboardData.topSellingProducts, topSellingProducts);
    });

    test('should calculate sales change percentage correctly', () {
      // Arrange
      final dashboardData = DashboardData(
        todayTotalSales: 1500.0,
        yesterdayTotalSales: 1200.0,
        todayTransactionsCount: 15,
        yesterdayTransactionsCount: 12,
        lowStockProductsCount: 3,
        salesTrendData: const [],
        topSellingProducts: const [],
      );

      // Act & Assert
      expect(dashboardData.salesChangePercentage, 25.0); // (1500-1200)/1200 * 100 = 25%
    });

    test('should handle zero yesterday sales correctly', () {
      // Arrange
      final dashboardData = DashboardData(
        todayTotalSales: 1500.0,
        yesterdayTotalSales: 0.0,
        todayTransactionsCount: 15,
        yesterdayTransactionsCount: 0,
        lowStockProductsCount: 3,
        salesTrendData: const [],
        topSellingProducts: const [],
      );

      // Act & Assert
      expect(dashboardData.salesChangePercentage, 100.0);
      expect(dashboardData.transactionsChangePercentage, 100.0);
    });

    test('should calculate average transaction amount correctly', () {
      // Arrange
      final dashboardData = DashboardData(
        todayTotalSales: 1500.0,
        yesterdayTotalSales: 1200.0,
        todayTransactionsCount: 15,
        yesterdayTransactionsCount: 12,
        lowStockProductsCount: 3,
        salesTrendData: const [],
        topSellingProducts: const [],
      );

      // Act & Assert
      expect(dashboardData.todayAverageTransactionAmount, 100.0); // 1500/15 = 100
      expect(dashboardData.yesterdayAverageTransactionAmount, 100.0); // 1200/12 = 100
    });

    test('should handle zero transactions correctly', () {
      // Arrange
      final dashboardData = DashboardData(
        todayTotalSales: 1500.0,
        yesterdayTotalSales: 1200.0,
        todayTransactionsCount: 0,
        yesterdayTransactionsCount: 0,
        lowStockProductsCount: 3,
        salesTrendData: const [],
        topSellingProducts: const [],
      );

      // Act & Assert
      expect(dashboardData.todayAverageTransactionAmount, 0.0);
      expect(dashboardData.yesterdayAverageTransactionAmount, 0.0);
    });

    test('should support copyWith functionality', () {
      // Arrange
      final originalData = DashboardData(
        todayTotalSales: 1500.0,
        yesterdayTotalSales: 1200.0,
        todayTransactionsCount: 15,
        yesterdayTransactionsCount: 12,
        lowStockProductsCount: 3,
        salesTrendData: const [],
        topSellingProducts: const [],
      );

      // Act
      final updatedData = originalData.copyWith(
        todayTotalSales: 2000.0,
        lowStockProductsCount: 5,
      );

      // Assert
      expect(updatedData.todayTotalSales, 2000.0);
      expect(updatedData.lowStockProductsCount, 5);
      expect(updatedData.yesterdayTotalSales, originalData.yesterdayTotalSales);
      expect(updatedData.todayTransactionsCount, originalData.todayTransactionsCount);
    });
  });

  group('SalesTrendData', () {
    test('should create sales trend data correctly', () {
      // Arrange & Act
      final trendData = SalesTrendData(
        date: DateTime(2024, 12, 1),
        sales: 1000.0,
        transactionsCount: 10,
      );

      // Assert
      expect(trendData.date, DateTime(2024, 12, 1));
      expect(trendData.sales, 1000.0);
      expect(trendData.transactionsCount, 10);
    });
  });

  group('TopSellingProductData', () {
    test('should create top selling product data correctly', () {
      // Arrange & Act
      const productData = TopSellingProductData(
        productId: 'product1',
        productName: 'Product 1',
        productSku: 'SKU001',
        quantitySold: 50,
        totalSales: 2500.0,
      );

      // Assert
      expect(productData.productId, 'product1');
      expect(productData.productName, 'Product 1');
      expect(productData.productSku, 'SKU001');
      expect(productData.quantitySold, 50);
      expect(productData.totalSales, 2500.0);
    });
  });

  group('DashboardDataExtension', () {
    test('should create empty dashboard data correctly', () {
      // Act
      final emptyData = DashboardDataExtension.empty();

      // Assert
      expect(emptyData.todayTotalSales, 0.0);
      expect(emptyData.yesterdayTotalSales, 0.0);
      expect(emptyData.todayTransactionsCount, 0);
      expect(emptyData.yesterdayTransactionsCount, 0);
      expect(emptyData.lowStockProductsCount, 0);
      expect(emptyData.salesTrendData, isEmpty);
      expect(emptyData.topSellingProducts, isEmpty);
    });
  });
}
