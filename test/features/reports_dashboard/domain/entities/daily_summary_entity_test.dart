import 'package:flutter_test/flutter_test.dart';
import 'package:grid_pos/features/reports_dashboard/domain/entities/daily_summary_entity.dart';

void main() {
  group('DailySummaryEntity', () {
    test('should create entity with all required fields', () {
      // Arrange
      final now = DateTime.now();
      final date = DateTime(2024, 12, 1);
      
      // Act
      final entity = DailySummaryEntity(
        id: '20241201_store1',
        date: date,
        storeId: 'store1',
        totalSales: 1000.0,
        transactionsCount: 10,
        lowStockProductsCount: 5,
        updatedAt: now,
        createdAt: now,
      );

      // Assert
      expect(entity.id, '20241201_store1');
      expect(entity.date, date);
      expect(entity.storeId, 'store1');
      expect(entity.totalSales, 1000.0);
      expect(entity.transactionsCount, 10);
      expect(entity.lowStockProductsCount, 5);
      expect(entity.updatedAt, now);
      expect(entity.createdAt, now);
    });

    test('should generate correct document ID', () {
      // Arrange
      final date = DateTime(2024, 12, 1);
      const storeId = 'store123';

      // Act
      final docId = DailySummaryEntity.generateDocumentId(date, storeId);

      // Assert
      expect(docId, '20241201_store123');
    });

    test('should parse date from document ID correctly', () {
      // Arrange
      const documentId = '20241201_store123';

      // Act
      final parsedDate = DailySummaryEntity.parseDateFromDocumentId(documentId);

      // Assert
      expect(parsedDate, DateTime(2024, 12, 1));
    });

    test('should parse store ID from document ID correctly', () {
      // Arrange
      const documentId = '20241201_store123';

      // Act
      final storeId = DailySummaryEntity.parseStoreIdFromDocumentId(documentId);

      // Assert
      expect(storeId, 'store123');
    });

    test('should handle store ID with underscores', () {
      // Arrange
      const documentId = '20241201_store_with_underscores';

      // Act
      final storeId = DailySummaryEntity.parseStoreIdFromDocumentId(documentId);

      // Assert
      expect(storeId, 'store_with_underscores');
    });

    test('should return null for invalid document ID format', () {
      // Arrange
      const invalidDocumentId = 'invalid_format';

      // Act
      final parsedDate = DailySummaryEntity.parseDateFromDocumentId(invalidDocumentId);
      final storeId = DailySummaryEntity.parseStoreIdFromDocumentId(invalidDocumentId);

      // Assert
      expect(parsedDate, isNull);
      expect(storeId, isNull);
    });

    test('should check if entity is today correctly', () {
      // Arrange
      final today = DateTime.now();
      final todayEntity = DailySummaryEntity(
        id: 'test_id',
        date: today,
        storeId: 'store1',
        totalSales: 100.0,
        transactionsCount: 1,
        lowStockProductsCount: 0,
        updatedAt: today,
        createdAt: today,
      );

      final yesterday = today.subtract(const Duration(days: 1));
      final yesterdayEntity = DailySummaryEntity(
        id: 'test_id',
        date: yesterday,
        storeId: 'store1',
        totalSales: 100.0,
        transactionsCount: 1,
        lowStockProductsCount: 0,
        updatedAt: today,
        createdAt: today,
      );

      // Act & Assert
      expect(todayEntity.isToday, isTrue);
      expect(yesterdayEntity.isToday, isFalse);
    });

    test('should check if entity is yesterday correctly', () {
      // Arrange
      final today = DateTime.now();
      final yesterday = today.subtract(const Duration(days: 1));
      
      final yesterdayEntity = DailySummaryEntity(
        id: 'test_id',
        date: yesterday,
        storeId: 'store1',
        totalSales: 100.0,
        transactionsCount: 1,
        lowStockProductsCount: 0,
        updatedAt: today,
        createdAt: today,
      );

      final todayEntity = DailySummaryEntity(
        id: 'test_id',
        date: today,
        storeId: 'store1',
        totalSales: 100.0,
        transactionsCount: 1,
        lowStockProductsCount: 0,
        updatedAt: today,
        createdAt: today,
      );

      // Act & Assert
      expect(yesterdayEntity.isYesterday, isTrue);
      expect(todayEntity.isYesterday, isFalse);
    });

    test('should format date correctly', () {
      // Arrange
      final date = DateTime(2024, 12, 1);
      final entity = DailySummaryEntity(
        id: 'test_id',
        date: date,
        storeId: 'store1',
        totalSales: 100.0,
        transactionsCount: 1,
        lowStockProductsCount: 0,
        updatedAt: date,
        createdAt: date,
      );

      // Act & Assert
      expect(entity.formattedDate, '2024-12-01');
    });

    test('should calculate average transaction amount correctly', () {
      // Arrange
      final date = DateTime(2024, 12, 1);
      final entity = DailySummaryEntity(
        id: 'test_id',
        date: date,
        storeId: 'store1',
        totalSales: 1000.0,
        transactionsCount: 10,
        lowStockProductsCount: 0,
        updatedAt: date,
        createdAt: date,
      );

      final zeroTransactionsEntity = DailySummaryEntity(
        id: 'test_id',
        date: date,
        storeId: 'store1',
        totalSales: 1000.0,
        transactionsCount: 0,
        lowStockProductsCount: 0,
        updatedAt: date,
        createdAt: date,
      );

      // Act & Assert
      expect(entity.averageTransactionAmount, 100.0);
      expect(zeroTransactionsEntity.averageTransactionAmount, 0.0);
    });

    test('should support copyWith functionality', () {
      // Arrange
      final date = DateTime(2024, 12, 1);
      final entity = DailySummaryEntity(
        id: 'test_id',
        date: date,
        storeId: 'store1',
        totalSales: 1000.0,
        transactionsCount: 10,
        lowStockProductsCount: 5,
        updatedAt: date,
        createdAt: date,
      );

      // Act
      final updatedEntity = entity.copyWith(
        totalSales: 2000.0,
        transactionsCount: 20,
      );

      // Assert
      expect(updatedEntity.id, entity.id);
      expect(updatedEntity.date, entity.date);
      expect(updatedEntity.storeId, entity.storeId);
      expect(updatedEntity.totalSales, 2000.0);
      expect(updatedEntity.transactionsCount, 20);
      expect(updatedEntity.lowStockProductsCount, entity.lowStockProductsCount);
      expect(updatedEntity.updatedAt, entity.updatedAt);
      expect(updatedEntity.createdAt, entity.createdAt);
    });
  });
}
