import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:grid_pos/features/reports_dashboard/presentation/pages/admin_reports_page.dart';

void main() {
  group('Reports Dashboard Navigation Tests', () {
    testWidgets('should navigate to admin reports page with store ID', (WidgetTester tester) async {
      const testStoreId = 'test_store_123';

      // Create a simple router for testing
      final router = GoRouter(
        routes: [
          GoRoute(
            path: '/',
            builder:
                (context, state) => Scaffold(
                  body: ElevatedButton(
                    onPressed: () => context.push('/reports', extra: {'storeId': testStoreId}),
                    child: const Text('Go to Reports'),
                  ),
                ),
          ),
          GoRoute(
            path: '/reports',
            builder: (context, state) {
              final args = state.extra as Map<String, String>?;
              final storeId = args?['storeId'];
              if (storeId != null) {
                return AdminReportsPage(storeId: storeId);
              } else {
                return const Scaffold(body: Center(child: Text('請先選擇店鋪')));
              }
            },
          ),
        ],
      );

      // Build the app with router
      await tester.pumpWidget(ProviderScope(child: MaterialApp.router(routerConfig: router)));

      // Verify initial page
      expect(find.text('Go to Reports'), findsOneWidget);

      // Tap the navigation button
      await tester.tap(find.text('Go to Reports'));
      await tester.pumpAndSettle();

      // Verify navigation to admin reports page
      expect(find.text('報告與儀表板'), findsOneWidget);
    });

    testWidgets('should handle reports navigation with store parameters', (
      WidgetTester tester,
    ) async {
      const testStoreId = 'test_store_123';

      // Create a router that accepts parameters
      final router = GoRouter(
        routes: [
          GoRoute(
            path: '/',
            builder:
                (context, state) => Scaffold(
                  body: ElevatedButton(
                    onPressed: () => context.push('/reports', extra: {'storeId': testStoreId}),
                    child: const Text('Go to Reports with Store'),
                  ),
                ),
          ),
          GoRoute(
            path: '/reports',
            builder: (context, state) {
              final Map<String, String>? args = state.extra as Map<String, String>?;
              final String? storeId = args?['storeId'];

              return Scaffold(
                body: Column(
                  children: [
                    const Text('Reports Dashboard'),
                    if (storeId != null) Text('Store ID: $storeId'),
                  ],
                ),
              );
            },
          ),
        ],
      );

      // Build the app with router
      await tester.pumpWidget(ProviderScope(child: MaterialApp.router(routerConfig: router)));

      // Verify initial page
      expect(find.text('Go to Reports with Store'), findsOneWidget);

      // Tap the navigation button
      await tester.tap(find.text('Go to Reports with Store'));
      await tester.pumpAndSettle();

      // Verify navigation with parameters
      expect(find.text('Reports Dashboard'), findsOneWidget);
      expect(find.text('Store ID: $testStoreId'), findsOneWidget);
    });

    testWidgets('should handle low stock navigation', (WidgetTester tester) async {
      const testStoreId = 'test_store_123';
      const testTenantId = 'test_tenant_456';

      // Create a router for low stock page
      final router = GoRouter(
        routes: [
          GoRoute(
            path: '/',
            builder:
                (context, state) => Scaffold(
                  body: ElevatedButton(
                    onPressed:
                        () => context.push(
                          '/reports/low-stock',
                          extra: {'storeId': testStoreId, 'tenantId': testTenantId},
                        ),
                    child: const Text('Go to Low Stock'),
                  ),
                ),
          ),
          GoRoute(
            path: '/reports/low-stock',
            builder: (context, state) {
              final Map<String, String>? args = state.extra as Map<String, String>?;
              final String? storeId = args?['storeId'];
              final String? tenantId = args?['tenantId'];

              return Scaffold(
                body: Column(
                  children: [
                    const Text('Low Stock Products'),
                    if (storeId != null) Text('Store ID: $storeId'),
                    if (tenantId != null) Text('Tenant ID: $tenantId'),
                  ],
                ),
              );
            },
          ),
        ],
      );

      // Build the app with router
      await tester.pumpWidget(ProviderScope(child: MaterialApp.router(routerConfig: router)));

      // Verify initial page
      expect(find.text('Go to Low Stock'), findsOneWidget);

      // Tap the navigation button
      await tester.tap(find.text('Go to Low Stock'));
      await tester.pumpAndSettle();

      // Verify navigation with parameters
      expect(find.text('Low Stock Products'), findsOneWidget);
      expect(find.text('Store ID: $testStoreId'), findsOneWidget);
      expect(find.text('Tenant ID: $testTenantId'), findsOneWidget);
    });

    testWidgets('should show store selection warning when no store ID provided', (
      WidgetTester tester,
    ) async {
      // Create a router that simulates no store ID provided
      final router = GoRouter(
        routes: [
          GoRoute(
            path: '/',
            builder:
                (context, state) => Scaffold(
                  body: ElevatedButton(
                    onPressed: () => context.push('/reports'),
                    child: const Text('Go to Reports'),
                  ),
                ),
          ),
          GoRoute(
            path: '/reports',
            builder: (context, state) {
              return const Scaffold(
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.warning, size: 64, color: Colors.orange),
                      SizedBox(height: 16),
                      Text('請先選擇店鋪'),
                      Text('需要選擇店鋪才能查看報告'),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      );

      // Build the app with router
      await tester.pumpWidget(ProviderScope(child: MaterialApp.router(routerConfig: router)));

      // Verify initial page
      expect(find.text('Go to Reports'), findsOneWidget);

      // Tap the navigation button
      await tester.tap(find.text('Go to Reports'));
      await tester.pumpAndSettle();

      // Verify warning message is shown
      expect(find.text('請先選擇店鋪'), findsOneWidget);
      expect(find.text('需要選擇店鋪才能查看報告'), findsOneWidget);
      expect(find.byIcon(Icons.warning), findsOneWidget);
    });
  });
}
