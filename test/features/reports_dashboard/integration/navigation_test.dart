import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:grid_pos/features/reports_dashboard/presentation/pages/dashboard_demo_page.dart';

void main() {
  group('Reports Dashboard Navigation Tests', () {
    testWidgets('should navigate to reports dashboard demo page', (WidgetTester tester) async {
      // Create a simple router for testing
      final router = GoRouter(
        routes: [
          GoRoute(
            path: '/',
            builder:
                (context, state) => Scaffold(
                  body: ElevatedButton(
                    onPressed: () => context.push('/reports'),
                    child: const Text('Go to Reports'),
                  ),
                ),
          ),
          GoRoute(path: '/reports', builder: (context, state) => const DashboardDemoPage()),
        ],
      );

      // Build the app with router
      await tester.pumpWidget(ProviderScope(child: MaterialApp.router(routerConfig: router)));

      // Verify initial page
      expect(find.text('Go to Reports'), findsOneWidget);

      // Tap the navigation button
      await tester.tap(find.text('Go to Reports'));
      await tester.pumpAndSettle();

      // Verify navigation to reports dashboard
      expect(find.text('儀表板演示'), findsOneWidget);
    });

    testWidgets('should handle reports navigation with store parameters', (
      WidgetTester tester,
    ) async {
      const testStoreId = 'test_store_123';

      // Create a router that accepts parameters
      final router = GoRouter(
        routes: [
          GoRoute(
            path: '/',
            builder:
                (context, state) => Scaffold(
                  body: ElevatedButton(
                    onPressed: () => context.push('/reports', extra: {'storeId': testStoreId}),
                    child: const Text('Go to Reports with Store'),
                  ),
                ),
          ),
          GoRoute(
            path: '/reports',
            builder: (context, state) {
              final Map<String, String>? args = state.extra as Map<String, String>?;
              final String? storeId = args?['storeId'];

              return Scaffold(
                body: Column(
                  children: [
                    const Text('Reports Dashboard'),
                    if (storeId != null) Text('Store ID: $storeId'),
                  ],
                ),
              );
            },
          ),
        ],
      );

      // Build the app with router
      await tester.pumpWidget(ProviderScope(child: MaterialApp.router(routerConfig: router)));

      // Verify initial page
      expect(find.text('Go to Reports with Store'), findsOneWidget);

      // Tap the navigation button
      await tester.tap(find.text('Go to Reports with Store'));
      await tester.pumpAndSettle();

      // Verify navigation with parameters
      expect(find.text('Reports Dashboard'), findsOneWidget);
      expect(find.text('Store ID: $testStoreId'), findsOneWidget);
    });

    testWidgets('should handle low stock navigation', (WidgetTester tester) async {
      const testStoreId = 'test_store_123';
      const testTenantId = 'test_tenant_456';

      // Create a router for low stock page
      final router = GoRouter(
        routes: [
          GoRoute(
            path: '/',
            builder:
                (context, state) => Scaffold(
                  body: ElevatedButton(
                    onPressed:
                        () => context.push(
                          '/reports/low-stock',
                          extra: {'storeId': testStoreId, 'tenantId': testTenantId},
                        ),
                    child: const Text('Go to Low Stock'),
                  ),
                ),
          ),
          GoRoute(
            path: '/reports/low-stock',
            builder: (context, state) {
              final Map<String, String>? args = state.extra as Map<String, String>?;
              final String? storeId = args?['storeId'];
              final String? tenantId = args?['tenantId'];

              return Scaffold(
                body: Column(
                  children: [
                    const Text('Low Stock Products'),
                    if (storeId != null) Text('Store ID: $storeId'),
                    if (tenantId != null) Text('Tenant ID: $tenantId'),
                  ],
                ),
              );
            },
          ),
        ],
      );

      // Build the app with router
      await tester.pumpWidget(ProviderScope(child: MaterialApp.router(routerConfig: router)));

      // Verify initial page
      expect(find.text('Go to Low Stock'), findsOneWidget);

      // Tap the navigation button
      await tester.tap(find.text('Go to Low Stock'));
      await tester.pumpAndSettle();

      // Verify navigation with parameters
      expect(find.text('Low Stock Products'), findsOneWidget);
      expect(find.text('Store ID: $testStoreId'), findsOneWidget);
      expect(find.text('Tenant ID: $testTenantId'), findsOneWidget);
    });

    testWidgets('should build dashboard demo page without errors', (WidgetTester tester) async {
      // Test that the dashboard demo page can be built independently
      await tester.pumpWidget(const ProviderScope(child: MaterialApp(home: DashboardDemoPage())));

      // Verify the page builds successfully
      expect(find.byType(DashboardDemoPage), findsOneWidget);
      expect(find.text('儀表板演示'), findsOneWidget);
    });
  });
}
