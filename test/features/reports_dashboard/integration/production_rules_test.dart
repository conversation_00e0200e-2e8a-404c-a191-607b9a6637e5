import 'package:flutter_test/flutter_test.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:grid_pos/core/constants/firestore_constants.dart';

/// 生產環境規則測試
/// 
/// 這些測試驗證 Firestore 安全規則在生產環境中的行為
/// 特別關注管理員和租戶的數據訪問權限
void main() {
  group('Production Rules Tests', () {
    late FakeFirebaseFirestore fakeFirestore;

    setUp(() {
      fakeFirestore = FakeFirebaseFirestore();
    });

    group('Admin Access Tests', () {
      test('admin should be able to read daily summaries', () async {
        // Arrange
        const storeId = 'store1';
        const documentId = '20241201_$storeId';

        // Add daily summary
        await fakeFirestore
            .collection(FirestoreConstants.dailySummaries)
            .doc(documentId)
            .set({
          'date': '2024-12-01',
          FirestoreConstants.storeId: storeId,
          'totalSales': 100.0,
          'transactionsCount': 5,
          'lowStockProductsCount': 2,
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.updatedAt: Timestamp.now(),
        });

        // Act - Admin reading daily summary
        final snapshot = await fakeFirestore
            .collection(FirestoreConstants.dailySummaries)
            .doc(documentId)
            .get();

        // Assert
        expect(snapshot.exists, true);
        expect(snapshot.data()?['totalSales'], 100.0);
        expect(snapshot.data()?['transactionsCount'], 5);
      });

      test('admin should be able to write daily summaries', () async {
        // Arrange
        const storeId = 'store1';
        const documentId = '20241202_$storeId';

        // Act - Admin creating daily summary
        await fakeFirestore
            .collection(FirestoreConstants.dailySummaries)
            .doc(documentId)
            .set({
          'date': '2024-12-02',
          FirestoreConstants.storeId: storeId,
          'totalSales': 150.0,
          'transactionsCount': 8,
          'lowStockProductsCount': 1,
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.updatedAt: Timestamp.now(),
        });

        // Assert
        final snapshot = await fakeFirestore
            .collection(FirestoreConstants.dailySummaries)
            .doc(documentId)
            .get();

        expect(snapshot.exists, true);
        expect(snapshot.data()?['totalSales'], 150.0);
      });

      test('admin should be able to read all sales data', () async {
        // Arrange
        const storeId = 'store1';

        // Add sales data
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.sales)
            .add({
          FirestoreConstants.totalAmount: 100.0,
          FirestoreConstants.status: 'completed',
          FirestoreConstants.cashierId: 'cashier1',
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.items: [
            {
              'productId': 'product1',
              'name': 'Product 1',
              'qty': 2,
              'price': 50.0,
              FirestoreConstants.tenantId: 'tenant1',
            },
          ],
        });

        // Act - Admin reading sales data
        final snapshot = await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.sales)
            .get();

        // Assert
        expect(snapshot.docs.length, 1);
        expect(snapshot.docs.first.data()[FirestoreConstants.totalAmount], 100.0);
      });

      test('admin should be able to read all product data', () async {
        // Arrange
        const storeId = 'store1';
        const tenantId = 'tenant1';

        // Add product data
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .add({
          FirestoreConstants.name: 'Product 1',
          FirestoreConstants.sku: 'SKU001',
          FirestoreConstants.barcode: 'BAR001',
          FirestoreConstants.price: 10.0,
          FirestoreConstants.stock: 5,
          FirestoreConstants.lowStockLevel: 3,
          FirestoreConstants.active: true,
          FirestoreConstants.tenantId: tenantId,
          FirestoreConstants.storeId: storeId,
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.updatedAt: Timestamp.now(),
        });

        // Act - Admin reading product data
        final snapshot = await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .get();

        // Assert
        expect(snapshot.docs.length, 1);
        expect(snapshot.docs.first.data()[FirestoreConstants.name], 'Product 1');
      });
    });

    group('Tenant Access Tests', () {
      test('tenant should be able to read sales data from their store', () async {
        // Arrange
        const storeId = 'store1';
        const tenantId = 'tenant1';

        // Add sales data with tenant items
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.sales)
            .add({
          FirestoreConstants.totalAmount: 100.0,
          FirestoreConstants.status: 'completed',
          FirestoreConstants.cashierId: 'cashier1',
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.items: [
            {
              'productId': 'product1',
              'name': 'Product 1',
              'qty': 2,
              'price': 50.0,
              FirestoreConstants.tenantId: tenantId,
            },
          ],
        });

        // Act - Tenant reading sales data (simulated)
        final snapshot = await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.sales)
            .get();

        // Assert - In production, security rules would filter this
        // Here we verify the data structure is correct for tenant filtering
        expect(snapshot.docs.length, 1);
        final saleData = snapshot.docs.first.data();
        final items = saleData[FirestoreConstants.items] as List<dynamic>;
        
        // Verify tenant can identify their items
        final tenantItems = items.where((item) => 
          (item as Map<String, dynamic>)[FirestoreConstants.tenantId] == tenantId
        ).toList();
        
        expect(tenantItems.length, 1);
        expect(tenantItems.first['name'], 'Product 1');
      });

      test('tenant should be able to read their own products', () async {
        // Arrange
        const storeId = 'store1';
        const tenantId = 'tenant1';

        // Add tenant's product
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .add({
          FirestoreConstants.name: 'Tenant Product',
          FirestoreConstants.sku: 'TENANT001',
          FirestoreConstants.barcode: 'TENANTBAR001',
          FirestoreConstants.price: 15.0,
          FirestoreConstants.stock: 8,
          FirestoreConstants.lowStockLevel: 5,
          FirestoreConstants.active: true,
          FirestoreConstants.tenantId: tenantId,
          FirestoreConstants.storeId: storeId,
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.updatedAt: Timestamp.now(),
        });

        // Act - Tenant reading their products
        final snapshot = await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .get();

        // Assert
        expect(snapshot.docs.length, 1);
        expect(snapshot.docs.first.data()[FirestoreConstants.name], 'Tenant Product');
        expect(snapshot.docs.first.data()[FirestoreConstants.tenantId], tenantId);
      });

      test('tenant should be able to identify low stock products', () async {
        // Arrange
        const storeId = 'store1';
        const tenantId = 'tenant1';

        // Add low stock product
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .add({
          FirestoreConstants.name: 'Low Stock Product',
          FirestoreConstants.sku: 'LOW001',
          FirestoreConstants.barcode: 'LOWBAR001',
          FirestoreConstants.price: 20.0,
          FirestoreConstants.stock: 2, // Below low stock level
          FirestoreConstants.lowStockLevel: 5,
          FirestoreConstants.active: true,
          FirestoreConstants.tenantId: tenantId,
          FirestoreConstants.storeId: storeId,
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.updatedAt: Timestamp.now(),
        });

        // Act - Query for low stock products
        final snapshot = await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .where(FirestoreConstants.active, isEqualTo: true)
            .get();

        // Assert - Filter low stock products in application logic
        final lowStockProducts = snapshot.docs.where((doc) {
          final data = doc.data();
          final stock = data[FirestoreConstants.stock] as int;
          final lowStockLevel = data[FirestoreConstants.lowStockLevel] as int;
          return stock <= lowStockLevel;
        }).toList();

        expect(lowStockProducts.length, 1);
        expect(lowStockProducts.first.data()[FirestoreConstants.name], 'Low Stock Product');
      });
    });

    group('Data Aggregation Tests', () {
      test('should correctly aggregate sales data for dashboard', () async {
        // Arrange
        const storeId = 'store1';
        const tenantId = 'tenant1';
        final today = DateTime.now();

        // Add multiple sales with mixed tenant items
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.sales)
            .add({
          FirestoreConstants.totalAmount: 200.0,
          FirestoreConstants.status: 'completed',
          FirestoreConstants.cashierId: 'cashier1',
          FirestoreConstants.createdAt: Timestamp.fromDate(today),
          FirestoreConstants.items: [
            {
              'productId': 'product1',
              'name': 'Product 1',
              'qty': 2,
              'price': 50.0,
              FirestoreConstants.tenantId: tenantId,
            },
            {
              'productId': 'product2',
              'name': 'Product 2',
              'qty': 1,
              'price': 100.0,
              FirestoreConstants.tenantId: 'other_tenant',
            },
          ],
        });

        // Act - Aggregate data for tenant dashboard
        final snapshot = await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.sales)
            .where(FirestoreConstants.status, isEqualTo: 'completed')
            .get();

        // Calculate tenant-specific sales
        double tenantSales = 0.0;
        int tenantTransactions = 0;

        for (final doc in snapshot.docs) {
          final data = doc.data();
          final items = data[FirestoreConstants.items] as List<dynamic>;
          
          bool hasTenantItems = false;
          double tenantItemsTotal = 0.0;

          for (final item in items) {
            final itemData = item as Map<String, dynamic>;
            if (itemData[FirestoreConstants.tenantId] == tenantId) {
              hasTenantItems = true;
              final qty = itemData['qty'] as int;
              final price = (itemData['price'] as num).toDouble();
              tenantItemsTotal += qty * price;
            }
          }

          if (hasTenantItems) {
            tenantSales += tenantItemsTotal;
            tenantTransactions++;
          }
        }

        // Assert
        expect(tenantSales, 100.0); // Only tenant1's items: 2 * 50.0
        expect(tenantTransactions, 1); // One transaction contains tenant's items
      });

      test('should correctly count low stock products for tenant', () async {
        // Arrange
        const storeId = 'store1';
        const tenantId = 'tenant1';

        // Add products with different stock levels
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .add({
          FirestoreConstants.name: 'Normal Stock Product',
          FirestoreConstants.stock: 10,
          FirestoreConstants.lowStockLevel: 5,
          FirestoreConstants.active: true,
          FirestoreConstants.tenantId: tenantId,
        });

        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .add({
          FirestoreConstants.name: 'Low Stock Product',
          FirestoreConstants.stock: 3,
          FirestoreConstants.lowStockLevel: 5,
          FirestoreConstants.active: true,
          FirestoreConstants.tenantId: tenantId,
        });

        // Act - Count low stock products
        final snapshot = await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .where(FirestoreConstants.active, isEqualTo: true)
            .get();

        final lowStockCount = snapshot.docs.where((doc) {
          final data = doc.data();
          final stock = data[FirestoreConstants.stock] as int;
          final lowStockLevel = data[FirestoreConstants.lowStockLevel] as int;
          return stock <= lowStockLevel;
        }).length;

        // Assert
        expect(lowStockCount, 1);
      });
    });
  });
}
