import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:grid_pos/features/product_mgmt/presentation/pages/product_form_page.dart';
import 'package:grid_pos/features/product_mgmt/presentation/providers/product_form_provider.dart';
import 'package:grid_pos/features/tenant_mgmt/domain/entities/grid_entity.dart';

// Mock classes
class MockRef extends Mock implements WidgetRef {}

void main() {
  group('ProductFormPage Grid Loading Tests', () {
    testWidgets('should show loading state when grids are loading', (WidgetTester tester) async {
      // 創建一個測試用的 Provider 容器
      final container = ProviderContainer(
        overrides: [
          // 模擬格位載入狀態
          productFormTenantGridsProvider.overrideWith((ref, params) {
            return Stream<List<GridEntity>>.fromIterable([]);
          }),
        ],
      );

      await tester.pumpWidget(UncontrolledProviderScope(container: container, child: MaterialApp(home: ProductFormPage())));

      // 驗證載入狀態顯示
      expect(find.text('載入格位中...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsWidgets);
    });

    testWidgets('should show retry button when grid loading fails', (WidgetTester tester) async {
      final container = ProviderContainer(
        overrides: [
          // 模擬格位載入錯誤
          productFormTenantGridsProvider.overrideWith((ref, params) {
            return Stream<List<GridEntity>>.error(Exception('載入格位失敗'));
          }),
        ],
      );

      await tester.pumpWidget(UncontrolledProviderScope(container: container, child: MaterialApp(home: ProductFormPage())));

      // 等待錯誤狀態顯示
      await tester.pumpAndSettle();

      // 驗證錯誤訊息和重試按鈕
      expect(find.text('載入錯誤'), findsOneWidget);
      expect(find.text('重試'), findsOneWidget);
      expect(find.byIcon(Icons.refresh), findsOneWidget);
    });

    testWidgets('should show timeout error after 30 seconds', (WidgetTester tester) async {
      final container = ProviderContainer(
        overrides: [
          // 模擬超時情況
          productFormTenantGridsProvider.overrideWith((ref, params) {
            return Stream<List<GridEntity>>.periodic(const Duration(seconds: 35), (count) => []);
          }),
        ],
      );

      await tester.pumpWidget(UncontrolledProviderScope(container: container, child: MaterialApp(home: ProductFormPage())));

      // 等待超時
      await tester.pump(const Duration(seconds: 31));
      await tester.pumpAndSettle();

      // 驗證超時錯誤訊息
      expect(find.textContaining('載入格位超時'), findsOneWidget);
    });

    testWidgets('should successfully load grids when data is available', (WidgetTester tester) async {
      final testGrids = <GridEntity>[
        GridEntity(id: 'grid1', code: 'A01', tenantId: 'tenant1', size: 'S', createdAt: DateTime.now(), updatedAt: DateTime.now()),
        GridEntity(id: 'grid2', code: 'A02', tenantId: 'tenant1', size: 'M', createdAt: DateTime.now(), updatedAt: DateTime.now()),
      ];

      final container = ProviderContainer(
        overrides: [
          // 模擬成功載入格位
          productFormTenantGridsProvider.overrideWith((ref, params) {
            return Stream.value(testGrids);
          }),
        ],
      );

      await tester.pumpWidget(UncontrolledProviderScope(container: container, child: MaterialApp(home: ProductFormPage())));

      await tester.pumpAndSettle();

      // 驗證格位選擇器顯示
      expect(find.text('選擇格位'), findsOneWidget);
      expect(find.text('格位: A01 (S)'), findsOneWidget);
      expect(find.text('格位: A02 (M)'), findsOneWidget);
    });

    testWidgets('retry button should refresh grid data', (WidgetTester tester) async {
      bool hasRetried = false;

      final container = ProviderContainer(
        overrides: [
          productFormTenantGridsProvider.overrideWith((ref, params) {
            if (!hasRetried) {
              return Stream<List<GridEntity>>.error(Exception('載入格位失敗'));
            } else {
              return Stream.value([GridEntity(id: 'grid1', code: 'A01', tenantId: 'tenant1', size: 'S', createdAt: DateTime.now(), updatedAt: DateTime.now())]);
            }
          }),
        ],
      );

      await tester.pumpWidget(UncontrolledProviderScope(container: container, child: MaterialApp(home: ProductFormPage())));

      await tester.pumpAndSettle();

      // 驗證錯誤狀態
      expect(find.text('載入錯誤'), findsOneWidget);
      expect(find.text('重試'), findsOneWidget);

      // 點擊重試按鈕
      hasRetried = true;
      await tester.tap(find.text('重試'));
      await tester.pumpAndSettle();

      // 驗證重試後成功載入
      expect(find.text('選擇格位'), findsOneWidget);
      expect(find.text('格位: A01 (S)'), findsOneWidget);
    });
  });
}
