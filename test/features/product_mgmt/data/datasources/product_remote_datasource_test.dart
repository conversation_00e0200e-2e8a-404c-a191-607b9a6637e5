import 'package:flutter_test/flutter_test.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:grid_pos/features/product_mgmt/data/datasources/product_remote_datasource.dart';
import 'package:grid_pos/features/product_mgmt/domain/entities/product_entity.dart';
import 'package:grid_pos/core/constants/firestore_constants.dart';

void main() {
  group('ProductRemoteDataSourceImpl - Low Stock Products', () {
    late FakeFirebaseFirestore fakeFirestore;
    late ProductRemoteDataSourceImpl dataSource;

    setUp(() {
      fakeFirestore = FakeFirebaseFirestore();
      dataSource = ProductRemoteDataSourceImpl(fakeFirestore);
    });

    group('watchLowStockProductsByTenant', () {
      test('should return low stock products for specific tenant', () async {
        // Arrange
        const storeId = 'store1';
        const tenantId = 'tenant1';

        // Add products with different stock levels
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .add({
          FirestoreConstants.name: 'Low Stock Product',
          FirestoreConstants.sku: 'SKU001',
          FirestoreConstants.barcode: 'BAR001',
          FirestoreConstants.price: 10.0,
          FirestoreConstants.stock: 2,
          FirestoreConstants.lowStockLevel: 5,
          FirestoreConstants.gridId: 'grid1',
          FirestoreConstants.active: true,
          FirestoreConstants.tenantId: tenantId,
          FirestoreConstants.storeId: storeId,
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.updatedAt: Timestamp.now(),
        });

        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .add({
          FirestoreConstants.name: 'Normal Stock Product',
          FirestoreConstants.sku: 'SKU002',
          FirestoreConstants.barcode: 'BAR002',
          FirestoreConstants.price: 15.0,
          FirestoreConstants.stock: 10,
          FirestoreConstants.lowStockLevel: 5,
          FirestoreConstants.gridId: 'grid2',
          FirestoreConstants.active: true,
          FirestoreConstants.tenantId: tenantId,
          FirestoreConstants.storeId: storeId,
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.updatedAt: Timestamp.now(),
        });

        // Act
        final stream = dataSource.watchLowStockProductsByTenant(storeId, tenantId);
        final products = await stream.first;

        // Assert
        expect(products.length, 1);
        expect(products.first.name, 'Low Stock Product');
        expect(products.first.stock, 2);
        expect(products.first.lowStockLevel, 5);
      });

      test('should ignore inactive products', () async {
        // Arrange
        const storeId = 'store1';
        const tenantId = 'tenant1';

        // Add inactive low stock product
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc(tenantId)
            .collection(FirestoreConstants.products)
            .add({
          FirestoreConstants.name: 'Inactive Low Stock Product',
          FirestoreConstants.sku: 'SKU001',
          FirestoreConstants.barcode: 'BAR001',
          FirestoreConstants.price: 10.0,
          FirestoreConstants.stock: 2,
          FirestoreConstants.lowStockLevel: 5,
          FirestoreConstants.gridId: 'grid1',
          FirestoreConstants.active: false, // Inactive
          FirestoreConstants.tenantId: tenantId,
          FirestoreConstants.storeId: storeId,
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.updatedAt: Timestamp.now(),
        });

        // Act
        final stream = dataSource.watchLowStockProductsByTenant(storeId, tenantId);
        final products = await stream.first;

        // Assert
        expect(products, isEmpty);
      });
    });

    group('watchLowStockProductsByStore', () {
      test('should return low stock products from all tenants', () async {
        // Arrange
        const storeId = 'store1';

        // Add low stock product for tenant1
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc('tenant1')
            .collection(FirestoreConstants.products)
            .add({
          FirestoreConstants.name: 'Tenant1 Low Stock',
          FirestoreConstants.sku: 'SKU001',
          FirestoreConstants.barcode: 'BAR001',
          FirestoreConstants.price: 10.0,
          FirestoreConstants.stock: 1,
          FirestoreConstants.lowStockLevel: 3,
          FirestoreConstants.gridId: 'grid1',
          FirestoreConstants.active: true,
          FirestoreConstants.tenantId: 'tenant1',
          FirestoreConstants.storeId: storeId,
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.updatedAt: Timestamp.now(),
        });

        // Add low stock product for tenant2
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc('tenant2')
            .collection(FirestoreConstants.products)
            .add({
          FirestoreConstants.name: 'Tenant2 Low Stock',
          FirestoreConstants.sku: 'SKU002',
          FirestoreConstants.barcode: 'BAR002',
          FirestoreConstants.price: 15.0,
          FirestoreConstants.stock: 2,
          FirestoreConstants.lowStockLevel: 5,
          FirestoreConstants.gridId: 'grid2',
          FirestoreConstants.active: true,
          FirestoreConstants.tenantId: 'tenant2',
          FirestoreConstants.storeId: storeId,
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.updatedAt: Timestamp.now(),
        });

        // Add normal stock product for tenant1
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc('tenant1')
            .collection(FirestoreConstants.products)
            .add({
          FirestoreConstants.name: 'Normal Stock Product',
          FirestoreConstants.sku: 'SKU003',
          FirestoreConstants.barcode: 'BAR003',
          FirestoreConstants.price: 20.0,
          FirestoreConstants.stock: 10,
          FirestoreConstants.lowStockLevel: 5,
          FirestoreConstants.gridId: 'grid3',
          FirestoreConstants.active: true,
          FirestoreConstants.tenantId: 'tenant1',
          FirestoreConstants.storeId: storeId,
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.updatedAt: Timestamp.now(),
        });

        // Act
        final stream = dataSource.watchLowStockProductsByStore(storeId);
        final products = await stream.first;

        // Assert
        expect(products.length, 2);
        expect(products.any((p) => p.name == 'Tenant1 Low Stock'), isTrue);
        expect(products.any((p) => p.name == 'Tenant2 Low Stock'), isTrue);
        expect(products.any((p) => p.name == 'Normal Stock Product'), isFalse);
      });

      test('should return empty list when no low stock products exist', () async {
        // Arrange
        const storeId = 'store1';

        // Add normal stock product
        await fakeFirestore
            .collection(FirestoreConstants.stores)
            .doc(storeId)
            .collection(FirestoreConstants.tenants)
            .doc('tenant1')
            .collection(FirestoreConstants.products)
            .add({
          FirestoreConstants.name: 'Normal Stock Product',
          FirestoreConstants.sku: 'SKU001',
          FirestoreConstants.barcode: 'BAR001',
          FirestoreConstants.price: 10.0,
          FirestoreConstants.stock: 10,
          FirestoreConstants.lowStockLevel: 5,
          FirestoreConstants.gridId: 'grid1',
          FirestoreConstants.active: true,
          FirestoreConstants.tenantId: 'tenant1',
          FirestoreConstants.storeId: storeId,
          FirestoreConstants.createdAt: Timestamp.now(),
          FirestoreConstants.updatedAt: Timestamp.now(),
        });

        // Act
        final stream = dataSource.watchLowStockProductsByStore(storeId);
        final products = await stream.first;

        // Assert
        expect(products, isEmpty);
      });
    });
  });
}
