/**
 * Firebase Rules Playground 測試腳本
 * 
 * 這個腳本包含了可以在 Firebase Console Rules Playground 中執行的測試場景
 * 複製相應的測試代碼到 Playground 中執行
 */

// ============================================================================
// 測試數據設置
// ============================================================================

const testUsers = {
  admin: {
    uid: 'test_admin_001',
    token: {
      email: '<EMAIL>'
    }
  },
  tenant: {
    uid: 'test_tenant_001',
    token: {
      email: '<EMAIL>'
    }
  },
  cashier: {
    uid: 'test_cashier_001',
    token: {
      email: '<EMAIL>'
    }
  },
  unauthorized: {
    uid: 'test_user_001',
    token: {
      email: '<EMAIL>'
    }
  }
};

const testData = {
  users: {
    'test_admin_001': {
      email: '<EMAIL>',
      role: 'admin',
      displayName: 'Test Admin'
    },
    'test_tenant_001': {
      email: '<EMAIL>',
      role: 'tenant',
      tenantId: 'test_tenant_001',
      storeId: 'test_store_001',
      displayName: 'Test Tenant'
    },
    'test_cashier_001': {
      email: '<EMAIL>',
      role: 'cashier',
      storeId: 'test_store_001',
      displayName: 'Test Cashier'
    },
    'test_user_001': {
      email: '<EMAIL>',
      role: 'pending_approval',
      displayName: 'Test User'
    }
  },
  stores: {
    'test_store_001': {
      name: 'Test Store',
      address: 'Test Address',
      active: true
    }
  },
  tenants: {
    'test_tenant_001': {
      name: 'Test Tenant',
      contactEmail: '<EMAIL>',
      active: true
    }
  },
  products: {
    'test_product_001': {
      name: 'Test Product',
      sku: 'TEST001',
      barcode: 'BAR001',
      price: 10.0,
      stock: 5,
      lowStockLevel: 3,
      gridId: 'test_grid_001',
      active: true,
      tenantId: 'test_tenant_001',
      storeId: 'test_store_001'
    }
  },
  sales: {
    'test_sale_001': {
      storeId: 'test_store_001',
      cashierId: 'test_cashier_001',
      totalAmount: 50.0,
      status: 'completed',
      paymentType: 'cash',
      items: [
        {
          productId: 'test_product_001',
          name: 'Test Product',
          qty: 2,
          price: 10.0,
          tenantId: 'test_tenant_001'
        }
      ]
    }
  },
  dailySummaries: {
    '20241201_test_store_001': {
      date: '2024-12-01',
      storeId: 'test_store_001',
      totalSales: 100.0,
      transactionsCount: 5,
      lowStockProductsCount: 2
    }
  }
};

// ============================================================================
// A. 用戶管理測試
// ============================================================================

// A1. 管理員讀取任何用戶數據 - 應該成功
const testA1 = {
  auth: testUsers.admin,
  operation: 'get',
  path: '/databases/(default)/documents/users/test_tenant_001',
  expected: 'ALLOW'
};

// A2. 用戶讀取自己的數據 - 應該成功
const testA2 = {
  auth: testUsers.tenant,
  operation: 'get',
  path: '/databases/(default)/documents/users/test_tenant_001',
  expected: 'ALLOW'
};

// A3. 用戶讀取他人數據 - 應該失敗
const testA3 = {
  auth: testUsers.tenant,
  operation: 'get',
  path: '/databases/(default)/documents/users/test_admin_001',
  expected: 'DENY'
};

// ============================================================================
// B. 商品管理測試
// ============================================================================

// B1. 租戶讀取自己的商品 - 應該成功
const testB1 = {
  auth: testUsers.tenant,
  operation: 'get',
  path: '/databases/(default)/documents/stores/test_store_001/tenants/test_tenant_001/products/test_product_001',
  expected: 'ALLOW'
};

// B2. 租戶創建商品 - 應該成功
const testB2 = {
  auth: testUsers.tenant,
  operation: 'create',
  path: '/databases/(default)/documents/stores/test_store_001/tenants/test_tenant_001/products/new_product',
  data: {
    name: 'New Product',
    sku: 'NEW001',
    barcode: 'NEWBAR001',
    price: 15.0,
    stock: 10,
    lowStockLevel: 5,
    gridId: 'test_grid_001',
    active: true,
    tenantId: 'test_tenant_001',
    storeId: 'test_store_001'
  },
  expected: 'ALLOW'
};

// B3. 租戶創建商品但 tenantId 不匹配 - 應該失敗
const testB3 = {
  auth: testUsers.tenant,
  operation: 'create',
  path: '/databases/(default)/documents/stores/test_store_001/tenants/test_tenant_001/products/invalid_product',
  data: {
    name: 'Invalid Product',
    sku: 'INV001',
    barcode: 'INVBAR001',
    price: 15.0,
    stock: 10,
    lowStockLevel: 5,
    gridId: 'test_grid_001',
    active: true,
    tenantId: 'other_tenant',  // 不匹配路徑
    storeId: 'test_store_001'
  },
  expected: 'DENY'
};

// ============================================================================
// C. 銷售數據測試
// ============================================================================

// C1. 管理員讀取銷售數據 - 應該成功
const testC1 = {
  auth: testUsers.admin,
  operation: 'get',
  path: '/databases/(default)/documents/stores/test_store_001/sales/test_sale_001',
  expected: 'ALLOW'
};

// C2. 收銀員讀取自己創建的銷售數據 - 應該成功
const testC2 = {
  auth: testUsers.cashier,
  operation: 'get',
  path: '/databases/(default)/documents/stores/test_store_001/sales/test_sale_001',
  expected: 'ALLOW'
};

// C3. 租戶讀取店鋪銷售數據 - 應該成功（用於儀表板）
const testC3 = {
  auth: testUsers.tenant,
  operation: 'get',
  path: '/databases/(default)/documents/stores/test_store_001/sales/test_sale_001',
  expected: 'ALLOW'
};

// C4. 收銀員創建銷售記錄 - 應該成功
const testC4 = {
  auth: testUsers.cashier,
  operation: 'create',
  path: '/databases/(default)/documents/stores/test_store_001/sales/new_sale',
  data: {
    storeId: 'test_store_001',
    cashierId: 'test_cashier_001',
    totalAmount: 25.0,
    status: 'completed',
    paymentType: 'card',
    items: [
      {
        productId: 'test_product_001',
        name: 'Test Product',
        qty: 1,
        price: 25.0,
        tenantId: 'test_tenant_001'
      }
    ]
  },
  expected: 'ALLOW'
};

// ============================================================================
// D. 每日摘要測試
// ============================================================================

// D1. 管理員讀取每日摘要 - 應該成功
const testD1 = {
  auth: testUsers.admin,
  operation: 'get',
  path: '/databases/(default)/documents/daily_summaries/20241201_test_store_001',
  expected: 'ALLOW'
};

// D2. 租戶讀取每日摘要 - 應該失敗
const testD2 = {
  auth: testUsers.tenant,
  operation: 'get',
  path: '/databases/(default)/documents/daily_summaries/20241201_test_store_001',
  expected: 'DENY'
};

// D3. 管理員創建每日摘要 - 應該成功
const testD3 = {
  auth: testUsers.admin,
  operation: 'create',
  path: '/databases/(default)/documents/daily_summaries/20241202_test_store_001',
  data: {
    date: '2024-12-02',
    storeId: 'test_store_001',
    totalSales: 150.0,
    transactionsCount: 8,
    lowStockProductsCount: 1,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  expected: 'ALLOW'
};

// D4. 管理員創建格式錯誤的每日摘要 - 應該失敗
const testD4 = {
  auth: testUsers.admin,
  operation: 'create',
  path: '/databases/(default)/documents/daily_summaries/invalid_format',
  data: {
    date: '2024-12-02',
    storeId: 'test_store_001',
    totalSales: 150.0,
    transactionsCount: 8,
    lowStockProductsCount: 1,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  expected: 'DENY'
};

// ============================================================================
// 測試執行指南
// ============================================================================

/*
在 Firebase Console Rules Playground 中執行測試：

1. 前往 Firebase Console > Firestore Database > Rules > Playground
2. 設置測試數據（在 "Firestore data" 部分）
3. 選擇測試用例並設置：
   - Authentication: 設置對應的 auth 對象
   - Operation: 選擇操作類型 (get, create, update, delete)
   - Path: 設置文檔路徑
   - Data: 如果是寫操作，設置數據
4. 點擊 "Run" 執行測試
5. 檢查結果是否符合預期

測試數據設置示例：
將 testData 對象中的數據複製到 Playground 的 "Firestore data" 部分

測試執行示例：
1. 選擇 testA1 測試用例
2. 設置 Authentication 為 testUsers.admin
3. 設置 Operation 為 "get"
4. 設置 Path 為 "/databases/(default)/documents/users/test_tenant_001"
5. 執行並驗證結果為 ALLOW
*/
